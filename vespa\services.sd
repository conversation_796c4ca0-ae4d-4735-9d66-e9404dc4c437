# Vespa Schema for HappiDost Services
# Optimized for hybrid search: BM25 + Vector + Metadata filtering

schema services {

    # Document definition
    document services {
        
        # Primary identifiers
        field service_id type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field provider_id type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field vespa_document_id type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Service information
        field title type string {
            indexing: summary | index
            index: enable-bm25
            bolding: on
        }
        
        field description type string {
            indexing: summary | index
            index: enable-bm25
            summary: dynamic
        }
        
        field category type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field subcategory type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field keywords type array<string> {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Pricing information
        field price_min type double {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field price_max type double {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field currency type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field pricing_model type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field is_negotiable type bool {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Location information
        field location type position {
            indexing: summary | attribute
        }
        
        field service_radius_km type int {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field service_type type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Availability
        field availability type string {
            indexing: summary | index
        }
        
        field timezone type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Quality metrics
        field verification_status type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field quality_score type double {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field total_bookings type int {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field total_reviews type int {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field average_rating type double {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Vector embedding for semantic search
        field embedding type tensor<float>(x[1536]) {
            indexing: summary | attribute | index
            attribute {
                distance-metric: angular
            }
            index {
                hnsw {
                    max-links-per-node: 32
                    neighbors-to-explore-at-insert: 500
                    multi-threaded-indexing: true
                }
            }
        }
        
        field embedding_model type string {
            indexing: summary | attribute
        }
        
        field embedding_dims type int {
            indexing: summary | attribute
        }
        
        # Search optimization
        field search_tags type array<string> {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Status and metadata
        field status type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field featured type bool {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Timestamps
        field created_at type long {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field updated_at type long {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Provider information (denormalized for performance)
        field provider_name type string {
            indexing: summary | index
            index: enable-bm25
        }
        
        field provider_rating type double {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        field provider_verification_status type string {
            indexing: summary | attribute
            attribute: fast-search
        }
        
        # Additional metadata
        field metadata type string {
            indexing: summary
        }
    }
    
    # Field sets for search
    fieldset default {
        fields: title, description, keywords, search_tags, provider_name
    }
    
    fieldset title_description {
        fields: title, description
    }
    
    # Rank profiles for different search scenarios
    
    # 1. Hybrid search: BM25 + Vector similarity + Quality boost
    rank-profile hybrid_search inherits default {
        inputs {
            query(user_embedding) tensor<float>(x[1536])
            query(location_lat) double: 0.0
            query(location_lon) double: 0.0
            query(max_distance_km) double: 50.0
            query(boost_verified) double: 1.2
            query(boost_featured) double: 1.1
        }
        
        first-phase {
            expression {
                bm25(title) * 2.0 + 
                bm25(description) * 1.5 + 
                bm25(keywords) * 1.8 +
                closeness(field, embedding) * 0.7 +
                (average_rating / 5.0) * 0.3 +
                (quality_score / 1.0) * 0.2 +
                if(verification_status == "verified", query(boost_verified), 1.0) +
                if(featured, query(boost_featured), 1.0)
            }
        }
        
        second-phase {
            rerank-count: 100
            expression {
                firstPhase +
                closeness(field, embedding) * 1.0 +
                (total_bookings > 0 ? log(total_bookings) * 0.1 : 0) +
                (total_reviews > 0 ? log(total_reviews) * 0.1 : 0) +
                if(great_circle_distance(attribute(location), query(location_lat), query(location_lon)) < query(max_distance_km) * 1000, 0.5, 0)
            }
        }
        
        match-features {
            bm25(title)
            bm25(description)
            closeness(field, embedding)
            attribute(average_rating)
            attribute(quality_score)
            attribute(total_bookings)
            great_circle_distance(attribute(location), query(location_lat), query(location_lon))
        }
    }
    
    # 2. Pure semantic search using vector similarity
    rank-profile semantic_search inherits default {
        inputs {
            query(user_embedding) tensor<float>(x[1536])
        }
        
        first-phase {
            expression: closeness(field, embedding)
        }
        
        second-phase {
            rerank-count: 50
            expression {
                closeness(field, embedding) * 2.0 +
                (average_rating / 5.0) * 0.5 +
                (quality_score / 1.0) * 0.3
            }
        }
    }
    
    # 3. Location-based search
    rank-profile location_search inherits default {
        inputs {
            query(location_lat) double: 0.0
            query(location_lon) double: 0.0
            query(max_distance_km) double: 10.0
        }
        
        first-phase {
            expression {
                if(great_circle_distance(attribute(location), query(location_lat), query(location_lon)) < query(max_distance_km) * 1000,
                   1.0 / (1.0 + great_circle_distance(attribute(location), query(location_lat), query(location_lon)) / 1000),
                   0.0
                ) +
                (average_rating / 5.0) * 0.3 +
                (quality_score / 1.0) * 0.2
            }
        }
    }
    
    # 4. Popular services (trending/featured)
    rank-profile popular inherits default {
        first-phase {
            expression {
                (average_rating / 5.0) * 0.4 +
                (quality_score / 1.0) * 0.3 +
                (total_bookings > 0 ? log(total_bookings) * 0.2 : 0) +
                (total_reviews > 0 ? log(total_reviews) * 0.1 : 0) +
                if(featured, 2.0, 0.0) +
                if(verification_status == "verified", 1.0, 0.0)
            }
        }
    }
    
    # 5. Price-optimized search
    rank-profile price_search inherits default {
        inputs {
            query(max_budget) double: 10000.0
            query(prefer_negotiable) double: 1.1
        }
        
        first-phase {
            expression {
                if(price_min <= query(max_budget), 
                   1.0 - (price_min / query(max_budget)) * 0.5,
                   0.0
                ) +
                if(is_negotiable, query(prefer_negotiable), 1.0) +
                (average_rating / 5.0) * 0.3
            }
        }
    }
    
    # Summary features for result presentation
    summary default {
        from-disk
    }
    
    summary search_result {
        from-disk
        fields: service_id, title, description, category, subcategory, 
                price_min, price_max, currency, is_negotiable,
                location, service_radius_km, service_type,
                average_rating, total_reviews, verification_status,
                provider_name, provider_rating, featured, created_at
    }
    
    summary minimal {
        from-disk
        fields: service_id, title, price_min, price_max, average_rating, location
    }
}

# Document processing configuration
document-processing {
    chain indexing {
        processor com.yahoo.docproc.indexing.IndexingProcessor
    }
}

# Container cluster configuration
container {
    search {
        chain vespa {
            searcher com.yahoo.search.querytransform.DefaultPositionSearcher
            searcher com.yahoo.search.querytransform.LowercasingSearcher
            searcher com.yahoo.search.grouping.GroupingSearcher
        }
    }
    
    document-api {
        # Enable document API for CRUD operations
    }
    
    http {
        server {
            port 8080
        }
    }
}

# Content cluster for document storage and indexing
content {
    redundancy 2
    documents {
        document services {
            mode index
        }
    }
    
    nodes {
        count 2
        resources {
            vcpu 4
            memory 8Gb
            disk 100Gb
        }
    }
    
    engine {
        proton {
            searchable-copies 2
            
            tuning {
                searchnode {
                    requestthreads {
                        search 64
                        persearch 8
                    }
                    
                    flushstrategy {
                        native {
                            total {
                                maxmemorygain 1Gb
                                diskbloatfactor 0.2
                            }
                        }
                    }
                    
                    index {
                        io {
                            write directio
                            read mmap
                        }
                    }
                }
            }
        }
    }
}
