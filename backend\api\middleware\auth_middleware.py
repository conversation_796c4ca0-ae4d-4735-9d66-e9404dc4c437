"""
Authentication Middleware
"""
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware


class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for API requests."""
    
    async def dispatch(self, request: Request, call_next):
        # Skip auth for public endpoints
        public_paths = ["/", "/health", "/docs", "/openapi.json"]
        
        if request.url.path in public_paths:
            return await call_next(request)
        
        # Process request
        response = await call_next(request)
        return response
