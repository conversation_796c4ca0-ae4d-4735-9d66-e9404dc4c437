"""
Agent Manager
Coordinates multiple AI agents and routes messages between them
"""
import logging
from typing import Dict, List, Optional
from sqlalchemy.orm import Session

from ...dost.events.models import DOSTEvent
from ..assistants.user_assistant import user_assistant

logger = logging.getLogger(__name__)


class AgentManager:
    """
    Manages and coordinates multiple AI agents in the system.
    Routes messages to appropriate agents and handles multi-agent workflows.
    """
    
    def __init__(self):
        self.agents = {}
        self.routing_rules = {}
    
    async def initialize(self):
        """Initialize agent manager and register agents."""
        try:
            # Register available agents
            self.agents["user_assistant"] = user_assistant
            
            # Set up routing rules
            self.routing_rules = {
                "ai.happidost.user_assistant": "user_assistant"
            }
            
            logger.info("Agent manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing agent manager: {str(e)}")
    
    async def route_message(self, event: DOSTEvent, db: Session) -> Optional[DOSTEvent]:
        """Route message to appropriate agent."""
        try:
            target_agent = self.routing_rules.get(event.target_entity_id)
            
            if target_agent and target_agent in self.agents:
                agent = self.agents[target_agent]
                return await agent.process_event(event, db)
            else:
                logger.warning(f"No agent found for target: {event.target_entity_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error routing message: {str(e)}")
            return None
    
    async def handle_service_registration(self, event: DOSTEvent, db: Session) -> Optional[DOSTEvent]:
        """Handle service registration events."""
        # Implementation will be added when service assistant is created
        return None
    
    async def handle_service_discovery(self, event: DOSTEvent, db: Session) -> Optional[DOSTEvent]:
        """Handle service discovery events."""
        # Route to user assistant for now
        return await user_assistant.process_event(event, db)


# Global agent manager instance
agent_manager = AgentManager()
