# DOST Event Specification v1.0

## 🎯 Overview

The **DOST Event** is the universal communication protocol for the DOST ecosystem. It enables seamless, standardized communication between all entities - humans, businesses, AI assistants, and services - across any platform, language, or modality.

## 🏗️ Core Philosophy

Every interaction in the DOST ecosystem is represented as an immutable event that:
- **Preserves Context**: Maintains conversation history and relationships
- **Enables AI Processing**: Structured for machine understanding and generation
- **Supports Multimodality**: Text, audio, video, images, and structured data
- **Ensures Traceability**: Complete audit trail of all communications
- **Facilitates Translation**: Cross-language and cross-modal communication

## 📋 DOST Event Schema

```json
{
  "dostEvent": {
    "metadata": {
      "semanticVersion": "1.0.0",
      "timestamp": "2024-01-15T14:30:45.123Z",
      "eventId": "550e8400-e29b-41d4-a716-************",
      "sessionId": "7c9e6679-7425-40de-944b-e07fc1f90ae7",
      "correlationId": "parent-event-uuid-if-response",
      "eventType": "message|request|response|notification|system|transaction",
      "priority": 5,
      "ttl": 3600,
      "retryCount": 0,
      "maxRetries": 3
    },

    "routing": {
      "sourceEntityId": "hum.john.doe.1234",
      "targetEntityId": "com.pizzahut.delivery.marathalli",
      "sourceEndpoint": "https://api.happidost.com/entities/hum.john.doe.1234/events",
      "targetEndpoint": "https://api.pizzahut.com/dost/events",
      "replyToEndpoint": "https://api.happidost.com/entities/hum.john.doe.1234/responses",
      "routingHints": {
        "preferredLanguage": "en",
        "preferredModality": "text",
        "urgency": "normal",
        "deliveryMode": "async"
      }
    },

    "security": {
      "apiKeyId": "dost_key_abc123def456",
      "encryptionLevel": "standard",
      "signature": "sha256_hash_of_event_content",
      "permissions": ["read", "respond", "forward"],
      "accessLevel": "public|private|restricted",
      "dataClassification": "public|internal|confidential|restricted"
    },

    "source": {
      "entityType": "human|business|ai_assistant|system",
      "isAiGenerated": false,
      "generationModel": null,
      "confidence": null,
      "humanVerified": true,
      "deviceInfo": {
        "type": "mobile|desktop|tablet|iot|api",
        "platform": "android|ios|web|server",
        "version": "1.2.3",
        "capabilities": ["text", "audio", "video", "location"]
      },
      "context": {
        "userAgent": "HappiDost-Mobile/1.2.3",
        "ipAddress": "*************",
        "location": {
          "latitude": 12.9716,
          "longitude": 77.5946,
          "accuracy": 10,
          "timestamp": "2024-01-15T14:30:45.123Z"
        },
        "timezone": "Asia/Kolkata"
      }
    },

    "content": {
      "intent": {
        "primary": "food_order",
        "confidence": 0.92,
        "entities": [
          {
            "type": "food_item",
            "value": "pizza",
            "confidence": 0.95
          },
          {
            "type": "location",
            "value": "marathalli",
            "confidence": 0.88
          }
        ],
        "sentiment": "positive",
        "urgency": "normal"
      },

      "message": {
        "text": {
          "content": "I want to order a large pepperoni pizza for delivery to Marathalli",
          "language": "en",
          "encoding": "utf-8",
          "wordCount": 12,
          "readingLevel": "elementary"
        },
        "audio": {
          "url": "https://storage.happidost.com/audio/event_550e8400.mp3",
          "duration": 4.2,
          "format": "mp3",
          "quality": "high",
          "language": "en",
          "transcript": "I want to order a large pepperoni pizza for delivery to Marathalli",
          "confidence": 0.94,
          "speakerInfo": {
            "gender": "male",
            "ageGroup": "adult",
            "accent": "indian_english"
          }
        },
        "video": {
          "urls": [
            "https://storage.happidost.com/video/event_550e8400.mp4"
          ],
          "duration": 15.5,
          "format": "mp4",
          "resolution": "1080p",
          "thumbnails": [
            "https://storage.happidost.com/thumbs/event_550e8400_t1.jpg"
          ],
          "captions": {
            "available": true,
            "languages": ["en", "hi"],
            "url": "https://storage.happidost.com/captions/event_550e8400.vtt"
          }
        },
        "images": {
          "urls": [
            "https://storage.happidost.com/images/event_550e8400_img1.jpg"
          ],
          "metadata": [
            {
              "width": 1920,
              "height": 1080,
              "format": "jpg",
              "size": 245760,
              "altText": "User pointing at pizza menu",
              "ocrText": "Pepperoni Pizza - Large - ₹599",
              "objects": ["pizza", "menu", "hand"],
              "faces": 1,
              "location": {
                "latitude": 12.9716,
                "longitude": 77.5946
              }
            }
          ]
        },
        "documents": {
          "attachments": [
            {
              "url": "https://storage.happidost.com/docs/event_550e8400_receipt.pdf",
              "type": "application/pdf",
              "name": "previous_order_receipt.pdf",
              "size": 102400,
              "pages": 1,
              "extractedText": "Pizza Hut Order #12345..."
            }
          ]
        },
        "structuredData": {
          "orderDetails": {
            "items": [
              {
                "name": "Large Pepperoni Pizza",
                "quantity": 1,
                "price": 599,
                "customizations": ["extra_cheese", "thin_crust"]
              }
            ],
            "deliveryAddress": {
              "street": "123 Main Street",
              "area": "Marathalli",
              "city": "Bangalore",
              "pincode": "560037",
              "landmark": "Near Metro Station"
            },
            "preferences": {
              "spiceLevel": "medium",
              "cookingInstructions": "well done",
              "deliveryTime": "asap"
            }
          }
        }
      },

      "payload": {
        "dostObjects": [
          {
            "objectId": "pizza-pepperoni-large-001",
            "title": "Large Pepperoni Pizza",
            "description": "Delicious large pizza with pepperoni toppings",
            "price": 599,
            "currency": "INR",
            "availability": "in_stock",
            "estimatedTime": "30 minutes",
            "actions": [
              {
                "verb": "order",
                "label": "Order Now",
                "endpoint": "/api/v1/orders/create"
              }
            ]
          }
        ],
        "recommendations": [
          {
            "objectId": "garlic-bread-001",
            "reason": "Frequently ordered together",
            "confidence": 0.87
          }
        ],
        "alternatives": [
          {
            "objectId": "pizza-margherita-large-001",
            "reason": "Similar size, different topping",
            "priceDifference": -50
          }
        ]
      }
    },

    "tags": [
      {
        "key": "category",
        "value": "food_order",
        "confidence": 0.95,
        "sourceEntityId": "ai.happidost.intent_classifier",
        "sourceReputation": 0.92,
        "timestamp": "2024-01-15T14:30:45.123Z",
        "metadata": {
          "model": "intent-classifier-v2.1",
          "trainingData": "food_orders_dataset_2024"
        }
      },
      {
        "key": "urgency",
        "value": "normal",
        "confidence": 0.88,
        "sourceEntityId": "ai.happidost.urgency_detector",
        "sourceReputation": 0.89,
        "timestamp": "2024-01-15T14:30:45.123Z"
      },
      {
        "key": "location",
        "value": "marathalli_bangalore",
        "confidence": 0.94,
        "sourceEntityId": "ai.happidost.location_extractor",
        "sourceReputation": 0.96,
        "timestamp": "2024-01-15T14:30:45.123Z",
        "metadata": {
          "extractionMethod": "named_entity_recognition",
          "coordinates": {
            "latitude": 12.9716,
            "longitude": 77.5946
          }
        }
      },
      {
        "key": "customer_type",
        "value": "returning",
        "confidence": 1.0,
        "sourceEntityId": "com.pizzahut.customer_service",
        "sourceReputation": 0.94,
        "timestamp": "2024-01-15T14:30:45.123Z",
        "metadata": {
          "lastOrder": "2024-01-10T19:15:30.000Z",
          "totalOrders": 15,
          "loyaltyTier": "gold"
        }
      }
    ],

    "workflow": {
      "stage": "initial_request",
      "nextStages": ["confirmation", "payment", "fulfillment"],
      "requiredActions": ["validate_address", "check_availability", "calculate_total"],
      "timeoutActions": ["send_reminder", "escalate_to_human"],
      "escalationRules": [
        {
          "condition": "no_response_30_minutes",
          "action": "notify_customer_service"
        }
      ]
    },

    "analytics": {
      "trackingId": "track_550e8400_analytics",
      "campaignId": "pizza_promo_jan2024",
      "channelId": "mobile_app",
      "sessionMetrics": {
        "messageCount": 3,
        "sessionDuration": 180,
        "userEngagement": "high"
      },
      "businessMetrics": {
        "conversionProbability": 0.78,
        "estimatedValue": 599,
        "customerLifetimeValue": 8500
      }
    },

    "compliance": {
      "dataRetention": {
        "retainUntil": "2026-01-15T14:30:45.123Z",
        "reason": "business_transaction",
        "policy": "7_year_retention"
      },
      "privacy": {
        "piiDetected": true,
        "piiTypes": ["phone_number", "address"],
        "consentRequired": true,
        "consentObtained": true,
        "consentId": "consent_550e8400_privacy"
      },
      "legal": {
        "jurisdiction": "india",
        "applicableLaws": ["IT_Act_2000", "Consumer_Protection_Act_2019"],
        "dataProcessingBasis": "contract_performance"
      }
    },

    "quality": {
      "validationStatus": "passed",
      "validationErrors": [],
      "contentModeration": {
        "status": "approved",
        "flags": [],
        "confidence": 0.99
      },
      "languageDetection": {
        "detected": "en",
        "confidence": 0.97,
        "alternatives": ["hi"]
      },
      "duplicateDetection": {
        "isDuplicate": false,
        "similarEvents": []
      }
    }
  }
}
```

## 🔄 DOST Update Event

For modifying characteristics of existing events (translations, modality changes, etc.):

```json
{
  "dostUpdateEvent": {
    "metadata": {
      "semanticVersion": "1.0.0",
      "timestamp": "2024-01-15T14:31:15.456Z",
      "updateId": "update-550e8400-e29b-41d4-a716-************",
      "originalEventId": "550e8400-e29b-41d4-a716-************",
      "updateType": "translation|modality_change|correction|enhancement",
      "sourceEntityId": "ai.happidost.translator"
    },

    "updates": [
      {
        "path": "content.message.text",
        "operation": "add|replace|remove",
        "value": {
          "content": "मैं मराठहल्ली में डिलीवरी के लिए एक बड़ा पेपरोनी पिज्जा ऑर्डर करना चाहता हूं",
          "language": "hi",
          "encoding": "utf-8",
          "translatedFrom": "en",
          "translationConfidence": 0.94,
          "translationModel": "google-translate-v3"
        }
      },
      {
        "path": "content.message.audio",
        "operation": "add",
        "value": {
          "url": "https://storage.happidost.com/audio/event_550e8400_hi.mp3",
          "duration": 5.1,
          "format": "mp3",
          "language": "hi",
          "voice": "female",
          "synthesizedFrom": "text",
          "ttsModel": "azure-neural-voice-hi"
        }
      }
    ],

    "reason": "User requested Hindi translation",
    "requestedBy": "hum.john.doe.1234",
    "processedBy": "ai.happidost.translator",
    "confidence": 0.94
  }
}
```

## 🏷️ DOST Event Tags

Global tagging system for event classification and reputation tracking:

```json
{
  "dostEventTag": {
    "tagId": "tag-550e8400-category-001",
    "eventId": "550e8400-e29b-41d4-a716-************",
    "timestamp": "2024-01-15T14:30:45.123Z",

    "tag": {
      "key": "category",
      "value": "food_order",
      "type": "classification|sentiment|quality|business|custom",
      "confidence": 0.95,
      "weight": 1.0
    },

    "source": {
      "entityId": "ai.happidost.intent_classifier",
      "entityType": "ai_assistant",
      "reputation": 0.92,
      "specialization": ["intent_classification", "food_domain"],
      "model": {
        "name": "intent-classifier-v2.1",
        "version": "2.1.0",
        "trainingData": "food_orders_dataset_2024",
        "accuracy": 0.94
      }
    },

    "validation": {
      "humanVerified": false,
      "verificationCount": 0,
      "agreementScore": null,
      "disputeCount": 0
    },

    "metadata": {
      "extractionMethod": "neural_classification",
      "processingTime": 0.045,
      "alternativeValues": [
        {"value": "restaurant_inquiry", "confidence": 0.23},
        {"value": "food_delivery", "confidence": 0.18}
      ],
      "contextFactors": ["time_of_day", "user_history", "location"]
    }
  }
}
```

## 🎯 Event Type Examples

### 1. Human-to-Business Message
```json
{
  "dostEvent": {
    "metadata": {
      "eventType": "message",
      "eventId": "msg-human-to-biz-001"
    },
    "routing": {
      "sourceEntityId": "hum.sarah.smith.5678",
      "targetEntityId": "com.uber.rides.bangalore"
    },
    "source": {
      "entityType": "human",
      "isAiGenerated": false
    },
    "content": {
      "intent": {
        "primary": "book_ride",
        "confidence": 0.89
      },
      "message": {
        "text": {
          "content": "I need a cab from Koramangala to Airport right now",
          "language": "en"
        }
      }
    }
  }
}
```

### 2. AI Assistant Response
```json
{
  "dostEvent": {
    "metadata": {
      "eventType": "response",
      "correlationId": "msg-human-to-biz-001"
    },
    "routing": {
      "sourceEntityId": "ai.uber.booking_assistant",
      "targetEntityId": "hum.sarah.smith.5678"
    },
    "source": {
      "entityType": "ai_assistant",
      "isAiGenerated": true,
      "generationModel": "gpt-4-turbo",
      "confidence": 0.92
    },
    "content": {
      "message": {
        "text": {
          "content": "I found 3 available cabs near you. The fastest option is UberGo arriving in 4 minutes for ₹450. Shall I book it?",
          "language": "en"
        }
      },
      "payload": {
        "dostObjects": [
          {
            "objectId": "uber-go-ride-001",
            "title": "UberGo to Airport",
            "price": 450,
            "estimatedTime": "4 minutes",
            "actions": [
              {
                "verb": "book",
                "label": "Book Now",
                "endpoint": "/api/v1/rides/book"
              }
            ]
          }
        ]
      }
    }
  }
}
```

### 3. System Notification
```json
{
  "dostEvent": {
    "metadata": {
      "eventType": "notification",
      "priority": 8
    },
    "routing": {
      "sourceEntityId": "sys.happidost.payment_gateway",
      "targetEntityId": "hum.john.doe.1234"
    },
    "source": {
      "entityType": "system",
      "isAiGenerated": false
    },
    "content": {
      "message": {
        "text": {
          "content": "Your payment of ₹599 for Pizza Hut order #PH12345 has been successfully processed.",
          "language": "en"
        }
      },
      "structuredData": {
        "transactionDetails": {
          "orderId": "PH12345",
          "amount": 599,
          "currency": "INR",
          "status": "completed",
          "paymentMethod": "upi",
          "transactionId": "TXN789012345"
        }
      }
    }
  }
}
```

### 4. Business-to-Human Update
```json
{
  "dostEvent": {
    "metadata": {
      "eventType": "notification"
    },
    "routing": {
      "sourceEntityId": "com.pizzahut.delivery.marathalli",
      "targetEntityId": "hum.john.doe.1234"
    },
    "source": {
      "entityType": "business",
      "isAiGenerated": false,
      "humanVerified": true
    },
    "content": {
      "message": {
        "text": {
          "content": "Your pizza is ready and out for delivery! Estimated arrival: 15 minutes. Track your order: PH12345",
          "language": "en"
        }
      },
      "payload": {
        "dostObjects": [
          {
            "objectId": "delivery-tracking-PH12345",
            "title": "Track Your Pizza Delivery",
            "actions": [
              {
                "verb": "track",
                "label": "Track Order",
                "endpoint": "/api/v1/orders/PH12345/track"
              }
            ]
          }
        ]
      }
    }
  }
}
```

## 🔧 Implementation Features

### 1. **Multimodal Communication**
- **Text**: Natural language in any language
- **Audio**: Voice messages with transcription
- **Video**: Visual communication with captions
- **Images**: Photos with OCR and object detection
- **Documents**: File attachments with text extraction
- **Structured Data**: JSON payloads for complex information

### 2. **AI-First Design**
- **Intent Recognition**: Automatic classification of user intents
- **Entity Extraction**: Identification of key information
- **Sentiment Analysis**: Emotional tone detection
- **Language Detection**: Automatic language identification
- **Content Moderation**: Safety and appropriateness checks

### 3. **Cross-Platform Compatibility**
- **Universal Format**: Works across all platforms and devices
- **Version Control**: Semantic versioning for evolution
- **Backward Compatibility**: Graceful handling of version differences
- **Standard Compliance**: Follows industry standards

### 4. **Security & Privacy**
- **Encryption**: End-to-end encryption support
- **Authentication**: API key and signature verification
- **Privacy Protection**: PII detection and handling
- **Compliance**: GDPR, data retention, and legal requirements

### 5. **Quality Assurance**
- **Validation**: Schema validation and error detection
- **Duplicate Detection**: Prevention of message duplication
- **Content Quality**: Readability and appropriateness scoring
- **Reputation System**: Trust scoring for entities and tags

## 🚀 Benefits

### **For Developers**
- **Standardized Format**: Single event format for all communications
- **Rich Metadata**: Complete context for intelligent processing
- **Extensible Schema**: Easy to add new fields and capabilities
- **Type Safety**: Well-defined structure for validation

### **For AI Systems**
- **Structured Input**: Perfect format for machine learning
- **Context Preservation**: Complete conversation history
- **Multi-modal Processing**: Handle any type of content
- **Intent Understanding**: Built-in classification and tagging

### **For Businesses**
- **Universal Integration**: Works with any system
- **Analytics Ready**: Built-in tracking and metrics
- **Compliance Built-in**: Privacy and legal requirements handled
- **Scalable Architecture**: Handle millions of events

### **For Users**
- **Seamless Experience**: Consistent across all platforms
- **Multi-language Support**: Automatic translation capabilities
- **Rich Interactions**: Text, voice, video, and visual communication
- **Context Awareness**: AI remembers conversation history

This DOST Event specification creates the foundation for truly intelligent, universal communication in the digital ecosystem! 🌟
