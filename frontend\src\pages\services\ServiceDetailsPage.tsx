import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Rating,
  Divider,
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  Verified as VerifiedIcon,
  Message as MessageIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

import { useService } from '../../contexts/ServiceContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const ServiceDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { getService, currentService, loading } = useService();

  useEffect(() => {
    if (id) {
      getService(id);
    }
  }, [id]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <LoadingSpinner size={50} message="Loading service details..." />
      </Box>
    );
  }

  if (!currentService) {
    return (
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <Typography variant="h4" sx={{ mb: 2 }}>
          Service not found
        </Typography>
        <Typography variant="body1" color="text.secondary">
          The service you're looking for doesn't exist or has been removed.
        </Typography>
      </Container>
    );
  }

  return (
    <>
      <Helmet>
        <title>{currentService.title} - HappiDost</title>
        <meta name="description" content={currentService.description} />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Grid container spacing={4}>
            {/* Main Content */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent sx={{ p: 4 }}>
                  {/* Header */}
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                    <Avatar
                      src={currentService.provider?.profile_image_url}
                      sx={{ width: 80, height: 80, mr: 3 }}
                    >
                      {currentService.provider?.full_name?.charAt(0)}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h4" sx={{ mb: 1, fontWeight: 'bold' }}>
                        {currentService.title}
                      </Typography>
                      <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                        by {currentService.provider?.full_name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                        <Rating value={currentService.rating} readOnly />
                        <Typography variant="body2">
                          {currentService.rating} ({currentService.total_reviews} reviews)
                        </Typography>
                        {currentService.is_verified && (
                          <Chip
                            icon={<VerifiedIcon />}
                            label="Verified"
                            color="success"
                            size="small"
                          />
                        )}
                      </Box>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Chip label={currentService.category} color="primary" />
                        <Chip label={currentService.subcategory} variant="outlined" />
                      </Box>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 3 }} />

                  {/* Description */}
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    About This Service
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7 }}>
                    {currentService.description}
                  </Typography>

                  {/* Details */}
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2">
                          {currentService.location.city}, {currentService.location.state}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2">
                          {currentService.service_type.replace('_', ' ')}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        <strong>Experience:</strong> {currentService.experience_years} years
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        <strong>Languages:</strong> {currentService.languages.join(', ')}
                      </Typography>
                    </Grid>
                  </Grid>

                  {/* Qualifications */}
                  {currentService.qualifications.length > 0 && (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        Qualifications
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {currentService.qualifications.map((qualification, index) => (
                          <Chip
                            key={index}
                            label={qualification}
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </Box>
                  )}

                  {/* Availability */}
                  {currentService.availability.length > 0 && (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        Availability
                      </Typography>
                      <Grid container spacing={1}>
                        {currentService.availability.map((slot, index) => (
                          <Grid item key={index}>
                            <Chip
                              label={`${slot.day.toUpperCase()}: ${slot.from_time}-${slot.to_time}`}
                              variant="outlined"
                              size="small"
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Sidebar */}
            <Grid item xs={12} md={4}>
              {/* Pricing Card */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Pricing
                  </Typography>
                  <Typography variant="h4" color="primary.main" sx={{ mb: 1, fontWeight: 'bold' }}>
                    ₹{currentService.price_min}
                    {currentService.price_max > currentService.price_min && 
                      ` - ₹${currentService.price_max}`
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    per {currentService.pricing_model}
                    {currentService.is_negotiable && ' (Negotiable)'}
                  </Typography>
                  
                  <Button
                    variant="contained"
                    fullWidth
                    size="large"
                    sx={{
                      mb: 2,
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    }}
                  >
                    Book Now
                  </Button>
                  
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<MessageIcon />}
                    sx={{ mb: 1 }}
                  >
                    Send Message
                  </Button>
                  
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<PhoneIcon />}
                  >
                    Call Provider
                  </Button>
                </CardContent>
              </Card>

              {/* Provider Info */}
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    About the Provider
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={currentService.provider?.profile_image_url}
                      sx={{ mr: 2 }}
                    >
                      {currentService.provider?.full_name?.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {currentService.provider?.full_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Reputation Score: {currentService.provider?.reputation_score}/100
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Total Bookings:</strong> {currentService.total_bookings}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Response Time:</strong> {currentService.response_time_hours} hours
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {currentService.provider?.verification_status?.email_verified && (
                      <Chip label="Email Verified" color="success" size="small" />
                    )}
                    {currentService.provider?.verification_status?.phone_verified && (
                      <Chip label="Phone Verified" color="success" size="small" />
                    )}
                    {currentService.provider?.verification_status?.identity_verified && (
                      <Chip label="ID Verified" color="success" size="small" />
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>
      </Container>
    </>
  );
};

export default ServiceDetailsPage;
