"""
DOST Object Factory
Creates DOST Objects from various input formats and service descriptions
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .models import (
    DOSTObject, DOSTObjectMetadata, DOSTObjectIdentity, ObjectType, ObjectStatus,
    MultimodalAttributes, Availability, Financials, DOSTAction, ActionMethod,
    Relationships, AIMetadata, PurchaseOptions, RentalOptions, SubscriptionOptions,
    OwnershipOptions, TemporalAvailability, GeographicalAvailability, ServiceArea
)
from ...ai.models.llm_client import LLMClient

logger = logging.getLogger(__name__)


class DOSTObjectFactory:
    """
    Factory class for creating DOST Objects from various input sources.
    Handles natural language processing and structured data extraction.
    """
    
    def __init__(self):
        self.llm_client = LLMClient()
        
        # Default actions for different object types
        self.default_actions = {
            ObjectType.SERVICE: [
                {
                    "verb": "inquire",
                    "label": "Ask Questions",
                    "endpoint": "/api/v1/objects/{id}/inquire",
                    "method": "POST"
                },
                {
                    "verb": "book",
                    "label": "Book Service",
                    "endpoint": "/api/v1/bookings/create",
                    "method": "POST"
                }
            ],
            ObjectType.PRODUCT: [
                {
                    "verb": "addToCart",
                    "label": "Add to Cart",
                    "endpoint": "/api/v1/cart/add",
                    "method": "POST"
                },
                {
                    "verb": "purchase",
                    "label": "Buy Now",
                    "endpoint": "/api/v1/transactions/purchase",
                    "method": "POST"
                }
            ],
            ObjectType.EXPERIENCE: [
                {
                    "verb": "book",
                    "label": "Book Experience",
                    "endpoint": "/api/v1/bookings/create",
                    "method": "POST"
                }
            ],
            ObjectType.RESOURCE: [
                {
                    "verb": "access",
                    "label": "Access Resource",
                    "endpoint": "/api/v1/resources/{id}/access",
                    "method": "GET"
                }
            ]
        }
    
    async def create_from_natural_language(
        self, 
        entity_id: str,
        description: str,
        additional_data: Dict[str, Any] = None
    ) -> DOSTObject:
        """
        Create DOST Object from natural language description.
        
        Args:
            entity_id: Entity providing this object
            description: Natural language description
            additional_data: Additional structured data
            
        Returns:
            Complete DOST Object
        """
        try:
            # Process description with AI
            processed_data = await self._process_natural_language(description)
            
            # Merge with additional data
            if additional_data:
                processed_data.update(additional_data)
            
            # Create DOST Object
            dost_object = await self._build_dost_object(entity_id, processed_data)
            
            logger.info(f"Created DOST Object: {dost_object.metadata.id}")
            return dost_object
            
        except Exception as e:
            logger.error(f"Error creating DOST Object: {str(e)}")
            raise
    
    async def create_from_structured_data(
        self,
        entity_id: str,
        structured_data: Dict[str, Any]
    ) -> DOSTObject:
        """
        Create DOST Object from structured data.
        
        Args:
            entity_id: Entity providing this object
            structured_data: Complete structured data
            
        Returns:
            Complete DOST Object
        """
        try:
            # Build DOST Object directly from structured data
            dost_object = await self._build_dost_object(entity_id, structured_data)
            
            logger.info(f"Created DOST Object from structured data: {dost_object.metadata.id}")
            return dost_object
            
        except Exception as e:
            logger.error(f"Error creating DOST Object from structured data: {str(e)}")
            raise
    
    async def _process_natural_language(self, description: str) -> Dict[str, Any]:
        """Process natural language description using AI."""
        try:
            system_prompt = """
You are a DOST Object analyzer. Extract structured information from service/product descriptions.

Extract and return JSON with these fields:
{
  "title": "Clear, concise title",
  "shortDescription": "Brief 1-line summary",
  "category": "primary category (food_delivery, home_services, education, etc.)",
  "subcategory": "specific subcategory",
  "objectType": "service|product|experience|resource",
  "keywords": ["relevant", "search", "keywords"],
  "specifications": {
    "key": "value pairs of important specs"
  },
  "pricing": {
    "type": "purchase|rental|subscription",
    "basePrice": 0,
    "currency": "INR"
  },
  "availability": {
    "leadTime": "30m|2h|1d",
    "serviceAreas": ["area1", "area2"]
  },
  "actions": [
    {
      "verb": "primary action verb",
      "label": "Action Label"
    }
  ]
}
"""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Description: {description}"}
            ]
            
            response = await self.llm_client.generate_response(
                messages,
                {"response_format": {"type": "json_object"}}
            )
            
            import json
            return json.loads(response)
            
        except Exception as e:
            logger.error(f"Error processing natural language: {str(e)}")
            # Return basic structure if AI processing fails
            return {
                "title": "Service",
                "category": "general",
                "objectType": "service",
                "keywords": []
            }
    
    async def _build_dost_object(self, entity_id: str, data: Dict[str, Any]) -> DOSTObject:
        """Build complete DOST Object from processed data."""
        
        # Determine object type
        object_type_str = data.get("objectType", "service")
        object_type = ObjectType(object_type_str)
        
        # Build metadata
        metadata = DOSTObjectMetadata(
            entity_id=entity_id,
            object_type=object_type,
            status=ObjectStatus.ACTIVE
        )
        
        # Build identity
        identity = DOSTObjectIdentity(
            title=data.get("title", "Untitled"),
            description=data.get("description", ""),
            short_description=data.get("shortDescription"),
            keywords=data.get("keywords", []),
            category=data.get("category", "general"),
            subcategory=data.get("subcategory"),
            aliases=data.get("aliases", [])
        )
        
        # Build availability
        availability = self._build_availability(data.get("availability", {}))
        
        # Build financials
        financials = self._build_financials(data.get("pricing", {}))
        
        # Build actions
        actions = self._build_actions(data.get("actions", []), object_type)
        
        # Build relationships
        relationships = self._build_relationships(entity_id, data.get("relationships", {}))
        
        # Create DOST Object
        dost_object = DOSTObject(
            metadata=metadata,
            identity=identity,
            availability=availability,
            financials=financials,
            actions=actions,
            relationships=relationships
        )
        
        return dost_object
    
    def _build_availability(self, availability_data: Dict[str, Any]) -> Availability:
        """Build availability from data."""
        availability = Availability()
        
        # Set lead time if provided
        lead_time = availability_data.get("leadTime")
        if lead_time:
            availability.temporal.lead_time.typical = lead_time
        
        # Set service areas if provided
        service_areas = availability_data.get("serviceAreas", [])
        for area in service_areas:
            service_area = ServiceArea(
                type="city",
                name=area,
                specific_areas=[area]
            )
            availability.geographical.service_areas.append(service_area)
        
        return availability
    
    def _build_financials(self, pricing_data: Dict[str, Any]) -> Financials:
        """Build financials from data."""
        financials = Financials(
            currency=pricing_data.get("currency", "INR")
        )
        
        pricing_type = pricing_data.get("type", "purchase")
        base_price = pricing_data.get("basePrice", 0)
        
        if pricing_type == "purchase":
            financials.ownership.purchase = PurchaseOptions(
                base_price=base_price,
                instant_pay=base_price
            )
        elif pricing_type == "rental":
            financials.ownership.rental = RentalOptions(
                hourly=base_price
            )
        elif pricing_type == "subscription":
            financials.ownership.subscription = SubscriptionOptions(
                monthly=base_price
            )
        
        return financials
    
    def _build_actions(self, actions_data: List[Dict[str, Any]], object_type: ObjectType) -> List[DOSTAction]:
        """Build actions from data."""
        actions = []
        
        # Add custom actions from data
        for action_data in actions_data:
            action = DOSTAction(
                verb=action_data.get("verb", "interact"),
                label=action_data.get("label", "Interact"),
                description=action_data.get("description"),
                endpoint=action_data.get("endpoint", f"/api/v1/objects/{{id}}/{action_data.get('verb', 'interact')}"),
                method=ActionMethod(action_data.get("method", "POST")),
                authentication=action_data.get("authentication", "required")
            )
            actions.append(action)
        
        # Add default actions for object type
        default_actions = self.default_actions.get(object_type, [])
        for default_action in default_actions:
            # Check if action already exists
            if not any(a.verb == default_action["verb"] for a in actions):
                action = DOSTAction(
                    verb=default_action["verb"],
                    label=default_action["label"],
                    endpoint=default_action["endpoint"],
                    method=ActionMethod(default_action["method"]),
                    authentication="required"
                )
                actions.append(action)
        
        return actions
    
    def _build_relationships(self, entity_id: str, relationships_data: Dict[str, Any]) -> Relationships:
        """Build relationships from data."""
        relationships = Relationships()
        
        # Set provider info
        relationships.provider = {
            "entity_id": entity_id,
            "name": relationships_data.get("providerName", "Service Provider"),
            "type": "business",
            "verification_status": "pending"
        }
        
        return relationships


# Global factory instance
dost_object_factory = DOSTObjectFactory()
