# Vespa Cloud Setup for HappiDost

## 🚀 **Step-by-Step Vespa Cloud Deployment**

### **1. Create Application Package**

You need to create an application package with the following structure:

```
happidost-vespa/
├── services.xml
├── schemas/
│   └── service.sd
└── deployment.xml
```

### **2. Create the Files**

**services.xml**:
```xml
<?xml version="1.0" encoding="utf-8" ?>
<services version="1.0">
    <container id="default" version="1.0">
        <search />
        <document-api />
        <http>
            <server id="default" port="8080" />
        </http>
    </container>
    
    <content id="content" version="1.0">
        <documents>
            <document type="service" mode="index" />
        </documents>
        <nodes>
            <node hostalias="node1" />
        </nodes>
    </content>
</services>
```

**schemas/service.sd**:
```
schema service {
    document service {
        field service_id type string {
            indexing: attribute | summary
        }
        field title type string {
            indexing: attribute | summary | index
        }
        field description type string {
            indexing: index | summary
        }
        field category type string {
            indexing: attribute | summary | index
        }
        field subcategory type string {
            indexing: attribute | summary
        }
        field provider_id type string {
            indexing: attribute | summary
        }
        field provider_name type string {
            indexing: attribute | summary | index
        }
        field location_city type string {
            indexing: attribute | summary | index
        }
        field location_country type string {
            indexing: attribute | summary
        }
        field price_min type double {
            indexing: attribute | summary
        }
        field price_max type double {
            indexing: attribute | summary
        }
        field currency type string {
            indexing: attribute | summary
        }
        field rating type double {
            indexing: attribute | summary
        }
        field total_reviews type int {
            indexing: attribute | summary
        }
        field keywords type array<string> {
            indexing: attribute | summary | index
        }
        field availability type string {
            indexing: attribute | summary
        }
        field status type string {
            indexing: attribute | summary
        }
        field embedding type tensor<float>(x[1536]) {
            indexing: attribute | index
            attribute {
                distance-metric: cosine
            }
        }
        field created_at type long {
            indexing: attribute | summary
        }
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, description) + attribute(rating) * 0.1
        }
    }
    
    rank-profile semantic inherits default {
        first-phase {
            expression: closeness(field, embedding)
        }
    }
    
    rank-profile hybrid inherits default {
        first-phase {
            expression: nativeRank(title, description) + closeness(field, embedding) * 0.5 + attribute(rating) * 0.1
        }
    }
}
```

**deployment.xml**:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<deployment version="1.0">
    <prod>
        <region active="true">aws-us-east-1c</region>
    </prod>
</deployment>
```

### **3. Deploy to Vespa Cloud**

1. **Click "Deploy application"** in your Vespa Cloud console
2. **Create a new application** named `happidost`
3. **Upload the application package** (zip the happidost-vespa folder)
4. **Wait for deployment** to complete

### **4. Get Your Endpoint**

After deployment, your endpoint will be:
```
https://happidost.happidostdb.aws-us-east-1c.dev.vespa-cloud.com
```

### **5. Test Your Deployment**

Use this endpoint in your `.env` file and test with:
```bash
python test_vespa.py
```

## 🔧 **Alternative: Use Vespa CLI**

If you prefer command line:

1. **Install Vespa CLI**:
```bash
# On macOS
brew install vespa-cli

# On Linux/Windows
# Download from https://github.com/vespa-engine/vespa/releases
```

2. **Deploy**:
```bash
vespa config set target cloud
vespa config set application happidostdb.happidost
vespa auth login
vespa deploy --wait 300
```

## 📝 **Your Current Configuration**

Based on your screenshot:
- **Tenant**: `happidostdb`
- **Application**: `happidost` (you'll create this)
- **Region**: `aws-us-east-1c` (default)
- **Environment**: `dev` (development)

**Final Endpoint**: `https://happidost.happidostdb.aws-us-east-1c.dev.vespa-cloud.com`

## 🧪 **Test Integration**

Once deployed, run:
```bash
python test_full_integration.py
```

This should now show:
- ✅ Supabase integration working
- ✅ Vespa integration working
- ✅ Backend integration working

## 🎯 **Next Steps After Deployment**

1. **Test document indexing**
2. **Test search queries**
3. **Integrate with service registration**
4. **Switch backend to use Supabase**

Your Vespa Cloud setup is almost ready - just need to deploy the application package!
