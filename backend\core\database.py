"""
Database configuration and models for HappiDost platform
"""
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, Column, String, DateTime, Boolean, Text, Float, Integer, J<PERSON><PERSON>, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
# from sqlalchemy.dialects.postgresql import UUID  # Commented out for SQLite compatibility
from .config import settings

# Database engine
engine = create_engine(
    settings.database_url,
    echo=settings.database_echo,
    pool_pre_ping=True,
    pool_recycle=300
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()


def get_db() -> Session:
    """Dependency to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Core Models
class Entity(Base):
    """DOST Entity model - represents users and service providers."""
    __tablename__ = "entities"
    
    entity_id = Column(String, primary_key=True)  # rDNS format
    entity_type = Column(String, nullable=False)  # "human" or "business"
    name = Column(String, nullable=False)
    description = Column(Text)
    verification_status = Column(String, default="pending")  # pending, verified, rejected
    verification_documents = Column(JSON)
    contact_info = Column(JSON)
    location = Column(JSON)  # Geographic information
    additional_data = Column(JSON)  # Additional entity-specific data
    reputation_score = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    services = relationship("Service", back_populates="entity")
    sent_events = relationship("DOSTEvent", foreign_keys="DOSTEvent.source_entity_id", back_populates="source_entity")
    received_events = relationship("DOSTEvent", foreign_keys="DOSTEvent.target_entity_id", back_populates="target_entity")


class Service(Base):
    """Service model - represents services offered by entities."""
    __tablename__ = "services"
    
    service_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    service_name = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String, nullable=False)
    subcategory = Column(String)
    capabilities = Column(JSON)  # List of service capabilities
    pricing_model = Column(String)  # "fixed", "dynamic", "negotiable"
    pricing_info = Column(JSON)
    availability = Column(JSON)  # Operating hours, days
    location_info = Column(JSON)  # Service locations
    requirements = Column(JSON)  # Service requirements/prerequisites
    tags = Column(JSON)  # Service tags for discovery
    embedding_vector = Column(JSON)  # Cached embedding for search
    is_active = Column(Boolean, default=True)
    approval_status = Column(String, default="pending")  # pending, approved, rejected
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    entity = relationship("Entity", back_populates="services")


class DOSTSession(Base):
    """DOST Session model - represents conversation sessions."""
    __tablename__ = "dost_sessions"
    
    session_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    participants = Column(JSON, nullable=False)  # List of entity IDs
    session_type = Column(String, nullable=False)  # "user_service", "service_service", etc.
    context = Column(JSON)  # Session context and metadata
    status = Column(String, default="active")  # active, completed, expired
    created_at = Column(DateTime, default=datetime.utcnow)
    last_activity = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    
    # Relationships
    events = relationship("DOSTEvent", back_populates="session")


class DOSTEvent(Base):
    """DOST Event model - represents all communications in the system."""
    __tablename__ = "dost_events"
    
    event_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("dost_sessions.session_id"), nullable=False)
    semantic_version = Column(String, default="1.0.0")
    timestamp = Column(DateTime, default=datetime.utcnow)
    source_entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    target_entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    source_entity_endpoint = Column(String)
    target_entity_endpoint = Column(String)
    is_ai_generated = Column(Boolean, default=False)
    event_type = Column(String, nullable=False)  # "message", "request", "response", "notification"
    priority = Column(Integer, default=5)
    requires_response = Column(Boolean, default=True)
    expiry_timestamp = Column(DateTime)
    
    # Event content
    message_content = Column(JSON)  # DOSTEventMessage
    tags = Column(JSON)  # DOSTEventTag array
    event_metadata = Column(JSON)  # Additional metadata
    
    # Processing status
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    processed_at = Column(DateTime)
    response_event_id = Column(String)  # Link to response event
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    session = relationship("DOSTSession", back_populates="events")
    source_entity = relationship("Entity", foreign_keys=[source_entity_id], back_populates="sent_events")
    target_entity = relationship("Entity", foreign_keys=[target_entity_id], back_populates="received_events")


class UserProfile(Base):
    """User profile model - extends Entity for user-specific data."""
    __tablename__ = "user_profiles"
    
    user_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    preferences = Column(JSON)  # User preferences
    interaction_history = Column(JSON)  # Interaction patterns
    favorite_services = Column(JSON)  # Favorite service IDs
    blocked_services = Column(JSON)  # Blocked service IDs
    privacy_settings = Column(JSON)  # Privacy preferences
    notification_settings = Column(JSON)  # Notification preferences
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ServiceProvider(Base):
    """Service provider model - extends Entity for provider-specific data."""
    __tablename__ = "service_providers"
    
    provider_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    business_info = Column(JSON)  # Business registration details
    verification_documents = Column(JSON)  # Uploaded documents
    service_categories = Column(JSON)  # Categories they operate in
    operating_regions = Column(JSON)  # Geographic coverage
    business_hours = Column(JSON)  # Operating schedule
    payment_methods = Column(JSON)  # Accepted payment methods
    performance_metrics = Column(JSON)  # Success rates, response times
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Transaction(Base):
    """Transaction model - represents service transactions."""
    __tablename__ = "transactions"
    
    transaction_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, ForeignKey("dost_sessions.session_id"), nullable=False)
    user_entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    service_entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    service_id = Column(String, ForeignKey("services.service_id"), nullable=False)
    
    # Transaction details
    transaction_type = Column(String, nullable=False)  # "service_request", "payment", "completion"
    amount = Column(Float)
    currency = Column(String, default="INR")
    status = Column(String, default="initiated")  # initiated, processing, completed, failed, cancelled
    
    # Service details
    service_details = Column(JSON)  # What was requested
    completion_details = Column(JSON)  # How it was completed
    
    # Timestamps
    initiated_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class AIAssistant(Base):
    """AI Assistant model - represents different AI assistants in the system."""
    __tablename__ = "ai_assistants"
    
    assistant_id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    entity_id = Column(String, ForeignKey("entities.entity_id"), nullable=False)
    assistant_type = Column(String, nullable=False)  # "user_assistant", "service_assistant", etc.
    name = Column(String, nullable=False)
    description = Column(Text)
    capabilities = Column(JSON)  # What this assistant can do
    personality = Column(JSON)  # Personality traits and behavior
    system_prompt = Column(Text)  # Base system prompt
    model_config = Column(JSON)  # LLM configuration
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Create all tables
def create_tables():
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)


# Database initialization
async def init_database():
    """Initialize database with default data."""
    db = SessionLocal()
    try:
        # First create AI assistant entities
        ai_entities = [
            {
                "entity_id": "ai.happidost.user_assistant",
                "entity_type": "ai_assistant",
                "name": "Dost User Assistant",
                "description": "Primary assistant for user interactions",
                "is_active": True
            },
            {
                "entity_id": "ai.happidost.service_assistant",
                "entity_type": "ai_assistant",
                "name": "Dost Service Assistant",
                "description": "Assistant for service provider interactions",
                "is_active": True
            },
            {
                "entity_id": "ai.happidost.orchestration_assistant",
                "entity_type": "ai_assistant",
                "name": "Dost Orchestration Assistant",
                "description": "Coordinates multi-agent workflows",
                "is_active": True
            }
        ]

        # Create entities first
        for entity_data in ai_entities:
            existing_entity = db.query(Entity).filter(
                Entity.entity_id == entity_data["entity_id"]
            ).first()

            if not existing_entity:
                entity = Entity(**entity_data)
                db.add(entity)

        # Commit entities first
        db.commit()

        # Then create default AI assistants
        default_assistants = [
            {
                "entity_id": "ai.happidost.user_assistant",
                "assistant_type": "user_assistant",
                "name": "Dost User Assistant",
                "description": "Primary assistant for user interactions",
                "capabilities": ["intent_recognition", "service_discovery", "conversation_management"],
                "personality": {"tone": "friendly", "style": "helpful", "formality": "casual"}
            },
            {
                "entity_id": "ai.happidost.service_assistant",
                "assistant_type": "service_assistant",
                "name": "Dost Service Assistant",
                "description": "Assistant for service provider interactions",
                "capabilities": ["service_registration", "capability_extraction", "provider_support"],
                "personality": {"tone": "professional", "style": "efficient", "formality": "business"}
            },
            {
                "entity_id": "ai.happidost.orchestration_assistant",
                "assistant_type": "orchestration_assistant",
                "name": "Dost Orchestration Assistant",
                "description": "Coordinates multi-agent workflows",
                "capabilities": ["workflow_management", "agent_coordination", "decision_making"],
                "personality": {"tone": "neutral", "style": "systematic", "formality": "formal"}
            }
        ]

        for assistant_data in default_assistants:
            # Check if assistant already exists
            existing = db.query(AIAssistant).filter(
                AIAssistant.entity_id == assistant_data["entity_id"]
            ).first()

            if not existing:
                assistant = AIAssistant(**assistant_data)
                db.add(assistant)

        db.commit()

    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()


# Utility functions
def get_entity_by_id(entity_id: str, db: Session) -> Optional[Entity]:
    """Get entity by ID."""
    return db.query(Entity).filter(Entity.entity_id == entity_id).first()


def get_active_services(entity_id: str, db: Session) -> List[Service]:
    """Get active services for an entity."""
    return db.query(Service).filter(
        Service.entity_id == entity_id,
        Service.is_active == True,
        Service.approval_status == "approved"
    ).all()


def create_session(participants: List[str], session_type: str, db: Session) -> DOSTSession:
    """Create a new DOST session."""
    session = DOSTSession(
        participants=participants,
        session_type=session_type,
        expires_at=datetime.utcnow().timestamp() + settings.max_session_duration
    )
    db.add(session)
    db.commit()
    db.refresh(session)
    return session
