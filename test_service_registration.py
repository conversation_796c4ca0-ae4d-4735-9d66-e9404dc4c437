#!/usr/bin/env python3
"""
Test complete service registration flow with Supabase + Vespa + LLM extraction
"""
import requests
import json
import time
from supabase import create_client

# Configuration
BACKEND_URL = "http://localhost:8000"
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def register_user_and_get_token():
    """Register a service provider and get auth token"""
    print("🔐 Registering service provider...")
    
    register_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123',
        'full_name': 'Service Provider',
        'role': 'service_provider',
        'terms_accepted': True
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/api/v1/auth/register", json=register_data)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Provider registered: {data['user']['user_id']}")
            return data['token'], data['user']['user_id']
        else:
            print(f"❌ Registration failed: {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None, None

def register_service(token, service_description):
    """Register a service using natural language description"""
    print(f"\n📝 Registering service: {service_description[:50]}...")

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    service_data = {
        'natural_language_description': service_description,
        'additional_context': {}
    }

    try:
        response = requests.post(f"{BACKEND_URL}/api/v1/service-registration/register", json=service_data, headers=headers)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service registered successfully!")
            print(f"   Service ID: {data.get('service_id', 'N/A')}")
            print(f"   Supabase ID: {data.get('supabase_id', 'N/A')}")
            print(f"   Vespa Document ID: {data.get('vespa_document_id', 'N/A')}")

            # Show extracted data
            extracted = data.get('extracted_data', {})
            if extracted:
                print(f"   📊 LLM Extracted Data:")
                print(f"      Title: {extracted.get('title', 'N/A')}")
                print(f"      Category: {extracted.get('category', 'N/A')}")
                print(f"      Subcategory: {extracted.get('subcategory', 'N/A')}")
                print(f"      Price Range: ₹{extracted.get('price_min', 0)}-₹{extracted.get('price_max', 0)}")
                print(f"      Service Type: {extracted.get('service_type', 'N/A')}")
                print(f"      Keywords: {extracted.get('keywords', [])}")
                print(f"      Location: {extracted.get('location_text', 'N/A')}")

            print(f"   ⏱️ Processing Time: {data.get('processing_time_ms', 0)}ms")
            return data
        else:
            print(f"❌ Service registration failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Service registration error: {e}")
        return None

def check_supabase_data():
    """Check what data was stored in Supabase"""
    print("\n🔍 Checking Supabase data...")
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Check entities
        entities = supabase.table('entities').select('*').execute()
        print(f"📊 Entities in Supabase: {len(entities.data)}")
        
        # Check services
        services = supabase.table('services').select('*').execute()
        print(f"📊 Services in Supabase: {len(services.data)}")
        
        if services.data:
            print("📋 Service details:")
            for service in services.data:
                print(f"   - {service.get('title', 'No title')}")
                print(f"     Category: {service.get('category', 'No category')}")
                print(f"     Location: {service.get('location', 'No location')}")
                print(f"     Pricing: {service.get('pricing', 'No pricing')}")
                print(f"     Keywords: {service.get('keywords', 'No keywords')}")
        
        return len(services.data)
    except Exception as e:
        print(f"❌ Supabase check error: {e}")
        return 0

def test_vespa_integration(token):
    """Test if Vespa integration is working"""
    print("\n🔍 Testing Vespa integration...")

    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        # Test search endpoint
        search_data = {
            'query': 'plumbing services',
            'limit': 5
        }

        response = requests.post(f"{BACKEND_URL}/api/v1/service-registration/search", json=search_data, headers=headers)
        print(f"Search Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            print(f"✅ Vespa search working! Found {len(results)} results")
            if results:
                print("📋 Search results:")
                for i, result in enumerate(results[:3], 1):
                    print(f"   {i}. {result.get('title', 'No title')}")
                    print(f"      Category: {result.get('category', 'N/A')}")
                    print(f"      Score: {result.get('score', 'N/A')}")
            return True
        else:
            print(f"⚠️ Vespa search response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Vespa test error: {e}")
        return False

def main():
    print("🚀 Testing Complete Service Registration Flow")
    print("=" * 60)
    
    # Step 1: Register user and get token
    token, user_id = register_user_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Step 2: Register multiple services with different descriptions
    test_services = [
        "I provide professional plumbing services in Mumbai. I can fix leaky faucets, install new pipes, and handle emergency repairs. Available 24/7. Rates start from ₹500 per hour.",
        
        "Expert home cleaning services in Bangalore. Deep cleaning, regular maintenance, post-construction cleanup. Eco-friendly products used. ₹1500 for 2BHK apartment.",
        
        "Professional web development services. I create responsive websites, e-commerce platforms, and mobile apps using React, Node.js, and Python. Based in Delhi, also work remotely. Starting from ₹25,000 per project.",
        
        "Experienced math and science tutor for grades 8-12. IIT graduate with 5 years teaching experience. Available for home tuition in Pune. ₹800 per hour."
    ]
    
    registered_services = []
    
    for i, service_desc in enumerate(test_services, 1):
        print(f"\n{'='*20} SERVICE {i} {'='*20}")
        service = register_service(token, service_desc)
        if service:
            registered_services.append(service)
        time.sleep(2)  # Small delay between registrations
    
    # Step 3: Check Supabase data
    supabase_count = check_supabase_data()
    
    # Step 4: Test Vespa integration
    vespa_working = test_vespa_integration(token)
    
    # Step 5: Summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Services registered: {len(registered_services)}")
    print(f"✅ Services in Supabase: {supabase_count}")
    print(f"✅ Vespa integration: {'Working' if vespa_working else 'Needs setup'}")
    
    if len(registered_services) > 0 and supabase_count > 0:
        print("\n🎉 SUCCESS: Complete integration working!")
        print("   - LLM extraction: ✅ Working")
        print("   - Supabase storage: ✅ Working") 
        print("   - Service registration: ✅ Working")
        if vespa_working:
            print("   - Vespa search: ✅ Working")
        else:
            print("   - Vespa search: ⚠️ Needs Vespa Cloud deployment")
    else:
        print("\n❌ Some components need attention")

if __name__ == "__main__":
    main()
