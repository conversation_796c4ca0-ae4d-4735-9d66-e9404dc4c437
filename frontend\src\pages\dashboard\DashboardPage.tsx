import React from 'react';
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  TrendingUp as TrendingIcon,
  Visibility as ViewIcon,
  Star as StarIcon,
  Message as MessageIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

import { useAuth } from '../../hooks/useAuth';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const stats = [
    { label: 'Profile Views', value: '1,234', icon: <ViewIcon />, color: 'primary' },
    { label: 'Total Services', value: '5', icon: <TrendingIcon />, color: 'success' },
    { label: 'Average Rating', value: '4.8', icon: <StarIcon />, color: 'warning' },
    { label: 'Messages', value: '23', icon: <MessageIcon />, color: 'info' },
  ];

  const recentActivity = [
    { type: 'booking', message: 'New booking for Math Tutoring', time: '2 hours ago' },
    { type: 'review', message: 'Received 5-star review for Home Cleaning', time: '1 day ago' },
    { type: 'message', message: 'New message from customer', time: '2 days ago' },
    { type: 'view', message: 'Your profile was viewed 15 times', time: '3 days ago' },
  ];

  return (
    <>
      <Helmet>
        <title>Dashboard - HappiDost</title>
        <meta name="description" content="Manage your HappiDost account, services, and bookings from your dashboard." />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                src={user?.profile_image_url}
                sx={{ width: 64, height: 64, mr: 3 }}
              >
                {user?.full_name?.charAt(0)}
              </Avatar>
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Welcome back, {user?.full_name}!
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={user?.role?.replace('_', ' ').toUpperCase()}
                    color="primary"
                    size="small"
                  />
                  {user?.verification_status?.email_verified && (
                    <Chip label="Verified" color="success" size="small" />
                  )}
                </Box>
              </Box>
            </Box>
            
            {user?.role === 'service_provider' && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigate('/register-service')}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                }}
              >
                Register New Service
              </Button>
            )}
          </Box>

          {/* Stats Grid */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {stats.map((stat, index) => (
              <Grid item xs={12} sm={6} md={3} key={stat.label}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box
                          sx={{
                            p: 1,
                            borderRadius: 2,
                            backgroundColor: `${stat.color}.light`,
                            color: `${stat.color}.main`,
                            mr: 2,
                          }}
                        >
                          {stat.icon}
                        </Box>
                        <Box>
                          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                            {stat.value}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {stat.label}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          <Grid container spacing={3}>
            {/* Quick Actions */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Quick Actions
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      onClick={() => navigate('/search')}
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      🔍 Search Services
                    </Button>
                    {user?.role === 'service_provider' && (
                      <>
                        <Button
                          variant="outlined"
                          fullWidth
                          onClick={() => navigate('/my-services')}
                          sx={{ justifyContent: 'flex-start' }}
                        >
                          🛠️ Manage My Services
                        </Button>
                        <Button
                          variant="outlined"
                          fullWidth
                          onClick={() => navigate('/register-service')}
                          sx={{ justifyContent: 'flex-start' }}
                        >
                          ➕ Add New Service
                        </Button>
                      </>
                    )}
                    <Button
                      variant="outlined"
                      fullWidth
                      onClick={() => navigate('/profile')}
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      👤 Edit Profile
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Recent Activity */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Recent Activity
                  </Typography>
                  <Box>
                    {recentActivity.map((activity, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: 2,
                          pb: 2,
                          borderBottom: index < recentActivity.length - 1 ? '1px solid' : 'none',
                          borderColor: 'divider',
                        }}
                      >
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: 'primary.main',
                            mr: 2,
                          }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ mb: 0.5 }}>
                            {activity.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.time}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Profile Completion */}
          {user?.role === 'service_provider' && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Profile Completion
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <LinearProgress variant="determinate" value={75} sx={{ height: 8, borderRadius: 4 }} />
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  75% complete - Add more details to improve your visibility
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label="✓ Basic Info" color="success" size="small" />
                  <Chip label="✓ Contact Details" color="success" size="small" />
                  <Chip label="⚠ Profile Photo" color="warning" size="small" />
                  <Chip label="⚠ Verification" color="warning" size="small" />
                </Box>
              </CardContent>
            </Card>
          )}
        </motion.div>
      </Container>
    </>
  );
};

export default DashboardPage;
