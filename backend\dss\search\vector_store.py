"""
Vector Store Implementation
Handles vector database operations for semantic search
"""
import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple
import numpy as np

from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from qdrant_client.http.exceptions import UnexpectedResponse

from ...core.config import settings

logger = logging.getLogger(__name__)


class VectorStore:
    """
    Vector database client for storing and searching service embeddings.
    Supports Qdrant as the primary vector database.
    """
    
    def __init__(self):
        self.client = None
        self.collections = {}
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Qdrant client."""
        try:
            if settings.vector_db_type == "qdrant":
                self.client = QdrantClient(
                    host=settings.qdrant_host,
                    port=settings.qdrant_port,
                    api_key=settings.qdrant_api_key
                )
                logger.info(f"Qdrant client initialized: {settings.qdrant_host}:{settings.qdrant_port}")
            else:
                logger.error(f"Unsupported vector database type: {settings.vector_db_type}")
                
        except Exception as e:
            logger.error(f"Error initializing vector store client: {str(e)}")
    
    async def create_collection(self, collection_name: str, vector_size: int = 1536) -> bool:
        """Create a new collection in the vector database."""
        try:
            if not self.client:
                logger.error("Vector store client not initialized")
                return False
            
            # Check if collection already exists
            collections = self.client.get_collections()
            existing_names = [col.name for col in collections.collections]
            
            if collection_name in existing_names:
                logger.info(f"Collection {collection_name} already exists")
                return True
            
            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE
                )
            )
            
            self.collections[collection_name] = {
                "vector_size": vector_size,
                "distance": Distance.COSINE
            }
            
            logger.info(f"Created collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating collection {collection_name}: {str(e)}")
            return False
    
    async def upsert_service(
        self, 
        service_id: str, 
        embedding: List[float], 
        metadata: Dict[str, Any],
        collection_name: str = "services"
    ) -> bool:
        """
        Insert or update a service in the vector database.
        
        Args:
            service_id: Unique service identifier
            embedding: Service embedding vector
            metadata: Service metadata
            collection_name: Target collection name
            
        Returns:
            Success status
        """
        try:
            if not self.client:
                logger.error("Vector store client not initialized")
                return False
            
            # Ensure collection exists
            await self.create_collection(collection_name, len(embedding))
            
            # Create point
            point = PointStruct(
                id=service_id,
                vector=embedding,
                payload=metadata
            )
            
            # Upsert point
            self.client.upsert(
                collection_name=collection_name,
                points=[point]
            )
            
            logger.info(f"Upserted service {service_id} to collection {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error upserting service {service_id}: {str(e)}")
            return False
    
    async def search_similar(
        self,
        query_vector: List[float],
        collection_name: str = "services",
        limit: int = 10,
        score_threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors in the database.
        
        Args:
            query_vector: Query embedding vector
            collection_name: Collection to search in
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            filters: Additional filters to apply
            
        Returns:
            List of similar services with scores and metadata
        """
        try:
            if not self.client:
                logger.error("Vector store client not initialized")
                return []
            
            # Build filter conditions
            query_filter = None
            if filters:
                conditions = []
                for key, value in filters.items():
                    if isinstance(value, (str, int, float, bool)):
                        conditions.append(
                            FieldCondition(key=key, match=MatchValue(value=value))
                        )
                
                if conditions:
                    query_filter = Filter(must=conditions)
            
            # Search
            search_results = self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold,
                query_filter=query_filter,
                with_payload=True
            )
            
            # Format results
            formatted_results = []
            for result in search_results:
                formatted_result = {
                    "service_id": result.id,
                    "similarity_score": result.score,
                    **result.payload
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"Found {len(formatted_results)} similar services")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching similar vectors: {str(e)}")
            return []
    
    async def delete_service(self, service_id: str, collection_name: str = "services") -> bool:
        """Delete a service from the vector database."""
        try:
            if not self.client:
                logger.error("Vector store client not initialized")
                return False
            
            self.client.delete(
                collection_name=collection_name,
                points_selector=[service_id]
            )
            
            logger.info(f"Deleted service {service_id} from collection {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting service {service_id}: {str(e)}")
            return False
    
    async def get_service(self, service_id: str, collection_name: str = "services") -> Optional[Dict[str, Any]]:
        """Get a specific service from the vector database."""
        try:
            if not self.client:
                logger.error("Vector store client not initialized")
                return None
            
            result = self.client.retrieve(
                collection_name=collection_name,
                ids=[service_id],
                with_payload=True,
                with_vectors=True
            )
            
            if result:
                point = result[0]
                return {
                    "service_id": point.id,
                    "vector": point.vector,
                    **point.payload
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting service {service_id}: {str(e)}")
            return None
    
    async def update_service_metadata(
        self, 
        service_id: str, 
        metadata: Dict[str, Any],
        collection_name: str = "services"
    ) -> bool:
        """Update service metadata without changing the vector."""
        try:
            if not self.client:
                logger.error("Vector store client not initialized")
                return False
            
            # Get current point
            current_point = await self.get_service(service_id, collection_name)
            if not current_point:
                logger.error(f"Service {service_id} not found")
                return False
            
            # Update with new metadata
            updated_point = PointStruct(
                id=service_id,
                vector=current_point["vector"],
                payload={**current_point, **metadata}
            )
            
            # Upsert updated point
            self.client.upsert(
                collection_name=collection_name,
                points=[updated_point]
            )
            
            logger.info(f"Updated metadata for service {service_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating service metadata: {str(e)}")
            return False
    
    async def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a collection."""
        try:
            if not self.client:
                return None
            
            info = self.client.get_collection(collection_name)
            return {
                "name": collection_name,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "status": info.status
            }
            
        except Exception as e:
            logger.error(f"Error getting collection info: {str(e)}")
            return None
    
    async def health_check(self) -> bool:
        """Check if vector store is healthy."""
        try:
            if not self.client:
                return False
            
            # Try to get collections list
            collections = self.client.get_collections()
            return True
            
        except Exception as e:
            logger.error(f"Vector store health check failed: {str(e)}")
            return False


# Global vector store instance
vector_store = VectorStore()
