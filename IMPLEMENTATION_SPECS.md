# HappiDost Implementation Specifications

## 🔧 Core System Specifications

### 1. DOST Event System Implementation

#### Event Schema (Pydantic Models)
```python
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

class DOSTEventMessage(BaseModel):
    text: Optional[Dict[str, str]] = None  # {"chat": "message", "language": "en"}
    audio: Optional[Dict[str, str]] = None  # {"url": "audio_url", "format": "mp3"}
    video: Optional[Dict[str, str]] = None  # {"url": "video_url", "format": "mp4"}
    image: Optional[Dict[str, str]] = None  # {"url": "image_url", "format": "jpg"}

class DOSTEventTag(BaseModel):
    tag: str
    confidence: float = Field(ge=0.0, le=1.0)
    source_entity_id: str
    metadata: Optional[Dict[str, Any]] = None

class DOSTEvent(BaseModel):
    semantic_version: str = "1.0.0"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    source_entity_id: str
    is_ai_generated: bool = False
    source_entity_endpoint: Optional[str] = None
    target_entity_id: str
    target_entity_endpoint: Optional[str] = None
    dost_event_message: DOSTEventMessage
    dost_event_tag: List[DOSTEventTag] = []
    priority: int = Field(default=5, ge=1, le=10)
    requires_response: bool = True
    expiry_timestamp: Optional[datetime] = None
```

#### Event Processing Pipeline
```python
class EventProcessor:
    async def process_event(self, event: DOSTEvent) -> DOSTEvent:
        # 1. Validate event schema
        await self.validate_event(event)
        
        # 2. Authenticate source entity
        await self.authenticate_entity(event.source_entity_id)
        
        # 3. Route to appropriate handler
        handler = await self.get_event_handler(event)
        
        # 4. Process event
        response = await handler.handle(event)
        
        # 5. Store event in history
        await self.store_event(event)
        
        return response
```

### 2. Service Registration Architecture

#### Service Model
```python
class ServiceCapability(BaseModel):
    name: str
    description: str
    input_parameters: List[str]
    output_format: str
    estimated_time: Optional[str] = None
    cost_model: str  # "fixed", "dynamic", "negotiable"

class ServiceLocation(BaseModel):
    type: str  # "global", "country", "city", "address"
    value: str
    coordinates: Optional[Dict[str, float]] = None

class ServiceRegistration(BaseModel):
    entity_id: str
    service_name: str
    description: str  # Natural language description
    capabilities: List[ServiceCapability]
    locations: List[ServiceLocation]
    availability: Dict[str, Any]  # Operating hours, days
    pricing_model: str
    contact_info: Dict[str, str]
    verification_documents: List[str]
    tags: List[str] = []
```

#### NLP Processing Pipeline
```python
class ServiceDescriptionProcessor:
    def __init__(self):
        self.nlp_model = load_nlp_model()
        self.capability_extractor = CapabilityExtractor()
        self.location_extractor = LocationExtractor()
        
    async def process_description(self, description: str) -> ServiceRegistration:
        # 1. Extract service type and category
        service_type = await self.extract_service_type(description)
        
        # 2. Extract capabilities
        capabilities = await self.capability_extractor.extract(description)
        
        # 3. Extract location information
        locations = await self.location_extractor.extract(description)
        
        # 4. Extract pricing information
        pricing = await self.extract_pricing_model(description)
        
        # 5. Generate service tags
        tags = await self.generate_tags(description, service_type)
        
        return ServiceRegistration(...)
```

### 3. AI Assistant Architecture

#### Base Assistant Class
```python
class BaseAssistant:
    def __init__(self, entity_id: str, llm_client: LLMClient):
        self.entity_id = entity_id
        self.llm_client = llm_client
        self.context_manager = ContextManager()
        self.memory = ConversationMemory()
        
    async def process_message(self, event: DOSTEvent) -> DOSTEvent:
        # 1. Load conversation context
        context = await self.context_manager.get_context(event.session_id)
        
        # 2. Update context with new message
        await self.context_manager.update_context(event)
        
        # 3. Generate response
        response = await self.generate_response(event, context)
        
        # 4. Create response event
        return self.create_response_event(event, response)
        
    async def generate_response(self, event: DOSTEvent, context: Dict) -> str:
        prompt = self.build_prompt(event, context)
        return await self.llm_client.generate(prompt)
```

#### Specialized Assistants
```python
class UserAssistant(BaseAssistant):
    """Handles user-facing interactions"""
    
    async def understand_intent(self, message: str) -> Intent:
        # Use NLP to classify user intent
        pass
        
    async def find_services(self, intent: Intent) -> List[Service]:
        # Search for relevant services
        pass

class ServiceAssistant(BaseAssistant):
    """Represents service providers"""
    
    async def handle_service_request(self, request: DOSTEvent) -> DOSTEvent:
        # Process service-specific requests
        pass
        
    async def check_availability(self, parameters: Dict) -> bool:
        # Check if service can fulfill request
        pass

class OrchestrationAssistant(BaseAssistant):
    """Coordinates multi-agent workflows"""
    
    async def orchestrate_workflow(self, workflow: Workflow) -> WorkflowResult:
        # Coordinate multiple agents and services
        pass
```

### 4. Vector Search Implementation

#### Embedding Generation
```python
class EmbeddingService:
    def __init__(self):
        self.model = SentenceTransformer('all-MiniLM-L6-v2')
        
    async def generate_service_embedding(self, service: ServiceRegistration) -> List[float]:
        # Combine service description, capabilities, and metadata
        text = f"{service.service_name} {service.description}"
        for cap in service.capabilities:
            text += f" {cap.name} {cap.description}"
        
        return self.model.encode(text).tolist()
        
    async def generate_query_embedding(self, query: str, context: Dict) -> List[float]:
        # Include context for better matching
        enhanced_query = f"{query} {context.get('location', '')} {context.get('preferences', '')}"
        return self.model.encode(enhanced_query).tolist()
```

#### Vector Search Engine
```python
class SemanticSearchEngine:
    def __init__(self, vector_store: QdrantClient):
        self.vector_store = vector_store
        self.embedding_service = EmbeddingService()
        
    async def search_services(self, query: str, context: Dict, limit: int = 10) -> List[ServiceMatch]:
        # 1. Generate query embedding
        query_vector = await self.embedding_service.generate_query_embedding(query, context)
        
        # 2. Search vector database
        search_results = self.vector_store.search(
            collection_name="services",
            query_vector=query_vector,
            limit=limit,
            score_threshold=0.7
        )
        
        # 3. Apply contextual filters
        filtered_results = await self.apply_filters(search_results, context)
        
        # 4. Rank results
        ranked_results = await self.rank_results(filtered_results, context)
        
        return ranked_results
```

## 🔄 Workflow Examples

### Service Registration Workflow
```
1. Service Provider → Natural Language Description
2. NLP Processing → Extract Capabilities, Location, Pricing
3. AI Validation → Verify completeness and accuracy
4. Human Review → Manual approval for sensitive services
5. Vector Embedding → Generate searchable embeddings
6. Database Storage → Store in both vector and relational DBs
7. Notification → Confirm registration to provider
```

### User Service Discovery Workflow
```
1. User Query → "I need pizza delivery in Marathalli"
2. Intent Recognition → food_delivery + location_marathalli
3. Context Loading → User preferences, history, location
4. Vector Search → Find semantically similar services
5. Filtering → Apply location, availability, rating filters
6. Ranking → Score based on relevance, distance, ratings
7. AI Assistant → Present options with explanations
8. User Selection → Choose preferred service
9. Service Handoff → Connect to service provider assistant
```

### Multi-Agent Interaction Example
```
User: "Book a table for 2 at a good Italian restaurant for tonight"

UserAssistant → Intent: restaurant_booking
             → Entities: cuisine=italian, party_size=2, time=tonight
             
DiscoveryAssistant → Search: Italian restaurants
                  → Filter: Available tonight, party_size>=2
                  → Results: 5 restaurants found
                  
UserAssistant → Present options to user
User → Selects "Mario's Italian Bistro"

ServiceAssistant(Mario's) → Check availability for tonight
                         → Confirm table for 2 available at 7 PM
                         
TransactionAssistant → Handle booking confirmation
                    → Send confirmation to user
                    → Update restaurant's availability
```

## 🎯 Critical Technical Decisions

### 1. Event Storage Strategy
**Option A**: Event Sourcing (Store all events)
- **Pros**: Complete audit trail, replay capability, temporal queries
- **Cons**: Storage overhead, complexity

**Option B**: State-based (Store current state + recent events)
- **Pros**: Efficient storage, simpler queries
- **Cons**: Limited history, harder debugging

**Recommendation**: Hybrid approach - Event sourcing for critical events, state-based for routine interactions

### 2. AI Model Strategy
**Option A**: Cloud APIs (OpenAI, Anthropic)
- **Pros**: High quality, no infrastructure overhead
- **Cons**: Cost, latency, dependency

**Option B**: Self-hosted models
- **Pros**: Cost control, privacy, customization
- **Cons**: Infrastructure complexity, model quality

**Recommendation**: Start with cloud APIs, gradually move to hybrid approach

### 3. Real-time Communication
**Option A**: WebSockets
- **Pros**: True real-time, bidirectional
- **Cons**: Connection management complexity

**Option B**: Server-Sent Events + HTTP
- **Pros**: Simpler implementation, better scaling
- **Cons**: Unidirectional, less real-time

**Recommendation**: SSE for notifications, WebSockets for active conversations

## 🚀 Development Priorities

### Week 1-2: Foundation
1. Project structure setup
2. Basic FastAPI application
3. Database schema design
4. DOST event models

### Week 3-4: Core Systems
1. Event processing pipeline
2. Entity registration system
3. Basic AI assistant framework
4. Service registration API

### Week 5-6: Search & Discovery
1. Vector database integration
2. Embedding generation pipeline
3. Semantic search implementation
4. Service matching algorithms

### Week 7-8: AI Enhancement
1. Intent recognition
2. Context management
3. Multi-agent coordination
4. Conversation flow management

### Week 9-10: Frontend & Integration
1. Mobile app development
2. Web interface
3. API integration
4. End-to-end testing

## 🔍 Key Questions for Discussion

1. **MVP Scope**: Which service categories should we target first?
2. **AI Models**: Cloud APIs vs self-hosted models?
3. **Geographic Focus**: Start with specific cities or global?
4. **Monetization**: How will the platform generate revenue?
5. **Trust System**: How to verify service providers and prevent fraud?
6. **Scalability**: Expected user and service provider volumes?
7. **Compliance**: Regulatory requirements for different service types?
8. **Integration**: Which existing platforms/APIs to integrate with?

## 📈 Success Metrics

### Technical Metrics
- Event processing latency < 100ms
- Search response time < 500ms
- Service discovery accuracy > 85%
- System uptime > 99.9%

### Business Metrics
- Service provider onboarding rate
- User engagement and retention
- Successful service transactions
- Platform revenue growth
