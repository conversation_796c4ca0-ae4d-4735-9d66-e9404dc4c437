"""
Supabase client and utilities for HappiDost Platform
"""

import os
import asyncio
import httpx
import logging
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
from datetime import datetime, timezone

from .config import settings

# Configure logging
logger = logging.getLogger(__name__)

class SupabaseManager:
    """Centralized Supabase management"""
    
    def __init__(self):
        self._client: Optional[Client] = None
        self._initialized = False
    
    def initialize(self):
        """Initialize Supabase connection"""
        if self._initialized:
            return
        
        try:
            self._client = create_client(
                settings.supabase_url,
                settings.supabase_key
            )
            
            # Test connection
            self._test_connection()
            
            self._initialized = True
            logger.info("Supabase connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase connection: {e}")
            raise
    
    def _test_connection(self):
        """Test Supabase connection"""
        try:
            # Try to query profiles table
            result = self._client.table("profiles").select("id").limit(1).execute()
            logger.info("Supabase connection test successful")
        except Exception as e:
            logger.warning(f"Supabase connection test failed: {e}")
    
    def get_client(self) -> Client:
        """Get Supabase client"""
        if not self._client:
            self.initialize()
        return self._client

# Global Supabase manager instance
supabase_manager = SupabaseManager()

# Convenience function
def get_supabase_client() -> Client:
    """Get Supabase client instance"""
    return supabase_manager.get_client()

class VespaManager:
    """Centralized Vespa management"""
    
    def __init__(self):
        self._client: Optional[httpx.AsyncClient] = None
        self._initialized = False
        self.vespa_endpoint = "http://localhost:8080"  # Default Vespa endpoint
    
    async def initialize(self):
        """Initialize Vespa connection"""
        if self._initialized:
            return
        
        try:
            self._client = httpx.AsyncClient(
                base_url=self.vespa_endpoint,
                timeout=30.0,
                headers={
                    "Content-Type": "application/json"
                }
            )
            
            # Test connection
            await self._test_connection()
            
            self._initialized = True
            logger.info("Vespa connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Vespa connection: {e}")
            # Don't raise - Vespa might not be available in development
    
    async def _test_connection(self):
        """Test Vespa connection"""
        try:
            if self._client:
                response = await self._client.get("/ApplicationStatus")
                if response.status_code == 200:
                    logger.info("Vespa connection test successful")
                else:
                    logger.warning(f"Vespa connection test returned: {response.status_code}")
        except Exception as e:
            logger.warning(f"Vespa connection test failed: {e}")
    
    def get_client(self) -> Optional[httpx.AsyncClient]:
        """Get Vespa client"""
        return self._client
    
    async def close(self):
        """Close Vespa connection"""
        if self._client:
            await self._client.aclose()
        self._initialized = False

# Global Vespa manager instance
vespa_manager = VespaManager()

# Convenience function
def get_vespa_client() -> Optional[httpx.AsyncClient]:
    """Get Vespa client instance"""
    return vespa_manager.get_client()

# Database utilities

class SupabaseUtils:
    """Utility functions for Supabase operations"""
    
    @staticmethod
    def create_user_profile(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new user profile"""
        try:
            supabase = get_supabase_client()
            
            profile_data = {
                "user_id": user_data["id"],
                "email": user_data["email"],
                "full_name": user_data.get("full_name", ""),
                "role": user_data.get("role", "user"),
                "vespa_user_id": f"user::{user_data['id']}",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "is_active": True
            }
            
            result = supabase.table("profiles").insert(profile_data).execute()
            
            if result.data:
                logger.info(f"User profile created: {user_data['id']}")
                return result.data[0]
            else:
                raise ValueError("No data returned from profile creation")
                
        except Exception as e:
            logger.error(f"Failed to create user profile: {e}")
            raise
    
    @staticmethod
    def get_user_profile(user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile by user_id"""
        try:
            supabase = get_supabase_client()
            
            result = supabase.table("profiles").select("*").eq(
                "user_id", user_id
            ).single().execute()
            
            return result.data if result.data else None
            
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return None
    
    @staticmethod
    def update_user_profile(user_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update user profile"""
        try:
            supabase = get_supabase_client()
            
            updates["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            result = supabase.table("profiles").update(updates).eq(
                "user_id", user_id
            ).execute()
            
            if result.data:
                logger.info(f"User profile updated: {user_id}")
                return result.data[0]
            else:
                raise ValueError("No data returned from profile update")
                
        except Exception as e:
            logger.error(f"Failed to update user profile: {e}")
            raise

class VespaUtils:
    """Utility functions for Vespa operations"""
    
    @staticmethod
    async def index_document(
        schema: str, 
        document_id: str, 
        document: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Index a document in Vespa"""
        try:
            vespa_client = get_vespa_client()
            if not vespa_client:
                return {"success": False, "error": "Vespa client not available"}
            
            url = f"/document/v1/{schema}/{schema}/docid/{document_id}"
            
            response = await vespa_client.post(url, json={"fields": document})
            
            if response.status_code in [200, 201]:
                logger.info(f"Document indexed in Vespa: {document_id}")
                return {"success": True, "response": response.json()}
            else:
                logger.error(f"Vespa indexing failed: {response.status_code} - {response.text}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            logger.error(f"Vespa indexing error: {e}")
            return {"success": False, "error": str(e)}
    
    @staticmethod
    async def search_documents(
        schema: str,
        query: str,
        rank_profile: str = "hybrid_search",
        hits: int = 20,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Search documents in Vespa"""
        try:
            vespa_client = get_vespa_client()
            if not vespa_client:
                return {"success": False, "error": "Vespa client not available"}
            
            # Build search parameters
            params = {
                "yql": f"select * from sources {schema} where userQuery()",
                "query": query,
                "ranking": rank_profile,
                "hits": hits,
                "offset": offset
            }
            
            # Add filters if provided
            if filters:
                filter_conditions = []
                for key, value in filters.items():
                    if isinstance(value, str):
                        filter_conditions.append(f'{key} contains "{value}"')
                    else:
                        filter_conditions.append(f'{key} = {value}')
                
                if filter_conditions:
                    params["yql"] = f"select * from sources {schema} where userQuery() and {' and '.join(filter_conditions)}"
            
            response = await vespa_client.get("/search/", params=params)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Vespa search completed: {result.get('root', {}).get('fields', {}).get('totalCount', 0)} results")
                return {"success": True, "result": result}
            else:
                logger.error(f"Vespa search failed: {response.status_code} - {response.text}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            logger.error(f"Vespa search error: {e}")
            return {"success": False, "error": str(e)}

# Sync utilities

class VespaSync:
    """Utilities for keeping Supabase and Vespa in sync"""
    
    @staticmethod
    async def sync_service_to_vespa(service_id: str) -> Dict[str, Any]:
        """Sync a service from Supabase to Vespa"""
        try:
            supabase = get_supabase_client()
            
            # Get service data from Supabase
            result = supabase.table("services").select(
                "*, profiles!services_provider_id_fkey(full_name, reputation_score, verification_status)"
            ).eq("service_id", service_id).single().execute()
            
            if not result.data:
                return {"success": False, "error": "Service not found"}
            
            service = result.data
            provider = service.get("profiles", {})
            
            # Prepare Vespa document
            vespa_doc = {
                "service_id": service["service_id"],
                "provider_id": service["provider_id"],
                "vespa_document_id": service["vespa_document_id"],
                "title": service["title"],
                "description": service["description"],
                "category": service["category"],
                "subcategory": service.get("subcategory", ""),
                "keywords": service.get("keywords", []),
                "price_min": service.get("price_min", 0.0),
                "price_max": service.get("price_max", 0.0),
                "currency": service.get("currency", "INR"),
                "pricing_model": service.get("pricing_model", "negotiable"),
                "is_negotiable": service.get("is_negotiable", True),
                "service_radius_km": service.get("service_radius_km", 0),
                "service_type": service.get("service_type", "on_location"),
                "verification_status": service.get("verification_status", "pending"),
                "quality_score": service.get("quality_score", 0.0),
                "total_bookings": service.get("total_bookings", 0),
                "total_reviews": service.get("total_reviews", 0),
                "average_rating": service.get("average_rating", 0.0),
                "search_tags": service.get("search_tags", []),
                "status": service.get("status", "active"),
                "featured": service.get("featured", False),
                "created_at": int(datetime.fromisoformat(service["created_at"].replace("Z", "+00:00")).timestamp()),
                "updated_at": int(datetime.fromisoformat(service["updated_at"].replace("Z", "+00:00")).timestamp()),
                "provider_name": provider.get("full_name", "Unknown"),
                "provider_rating": provider.get("reputation_score", 0.0),
                "provider_verification_status": provider.get("verification_status", "unverified")
            }
            
            # Add location if available
            if service.get("location"):
                # Parse PostGIS point format
                location_str = service["location"]
                # Extract coordinates from POINT(lon lat) format
                coords = location_str.replace("POINT(", "").replace(")", "").split()
                if len(coords) == 2:
                    vespa_doc["location"] = {
                        "lat": float(coords[1]),
                        "lng": float(coords[0])
                    }
            
            # Index in Vespa
            result = await VespaUtils.index_document(
                "services",
                service["vespa_document_id"],
                vespa_doc
            )
            
            if result["success"]:
                # Update sync status
                supabase.table("vespa_sync").upsert({
                    "entity_type": "service",
                    "entity_id": service["id"],
                    "vespa_document_id": service["vespa_document_id"],
                    "sync_status": "synced",
                    "last_sync_at": datetime.now(timezone.utc).isoformat(),
                    "document_version": 1
                }).execute()
            
            return result
            
        except Exception as e:
            logger.error(f"Service sync to Vespa failed: {e}")
            return {"success": False, "error": str(e)}

# Initialize connections
async def init_connections():
    """Initialize all database connections"""
    supabase_manager.initialize()
    await vespa_manager.initialize()

# Cleanup function
async def cleanup_connections():
    """Cleanup all database connections"""
    await vespa_manager.close()
