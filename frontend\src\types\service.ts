// Service types
export interface Service {
  service_id: string;
  provider_id: string;
  title: string;
  description: string;
  category: string;
  subcategory: string;
  keywords: string[];
  
  // Pricing
  price_min: number;
  price_max: number;
  currency: string;
  pricing_model: 'hourly' | 'fixed' | 'daily' | 'weekly' | 'monthly' | 'per_item' | 'per_sqft' | 'negotiable';
  is_negotiable: boolean;
  
  // Service details
  service_type: 'on_location' | 'remote' | 'hybrid' | 'at_provider';
  service_radius_km?: number;
  duration_minutes?: number;
  
  // Location
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude: number;
    longitude: number;
  };
  
  // Availability
  availability: Array<{
    day: 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun';
    from_time: string; // HH:MM format
    to_time: string;   // HH:MM format
  }>;
  
  // Provider info
  qualifications: string[];
  experience_years: number;
  languages: string[];
  
  // Media
  images: string[];
  videos: string[];
  documents: string[];
  
  // Status and metrics
  is_active: boolean;
  is_featured: boolean;
  is_verified: boolean;
  rating: number;
  total_reviews: number;
  total_bookings: number;
  response_time_hours: number;
  
  // AI metadata
  ai_metadata: {
    embedding_vector?: number[];
    extracted_keywords: string[];
    quality_score: number;
    completeness_score: number;
    last_updated: string;
  };
  
  // Timestamps
  created_at: string;
  updated_at: string;
  
  // Provider details (populated when needed)
  provider?: {
    user_id: string;
    full_name: string;
    profile_image_url?: string;
    reputation_score: number;
    verification_status: {
      email_verified: boolean;
      phone_verified: boolean;
      identity_verified: boolean;
      business_verified: boolean;
    };
  };
}

// Service registration data
export interface ServiceRegistrationData {
  natural_language_description: string;
  provider_location?: string;
  additional_context?: {
    images?: string[];
    videos?: string[];
    documents?: string[];
    contact_preferences?: {
      preferred_contact_method: 'phone' | 'email' | 'chat';
      response_time_expectation: string;
    };
    special_requirements?: string;
    target_audience?: string;
    competitive_advantages?: string;
  };
}

// Service registration response
export interface ServiceRegistrationResponse {
  success: boolean;
  service_id: string;
  supabase_id: string;
  vespa_document_id: string;
  data: Service;
  extracted_data: {
    title: string;
    description: string;
    category: string;
    subcategory: string;
    keywords: string[];
    price_min: number;
    price_max: number;
    pricing_model: string;
    is_negotiable: boolean;
    service_type: string;
    availability: Array<{
      day: string;
      from_time: string;
      to_time: string;
    }>;
    qualifications: string[];
    experience_years: number;
    languages: string[];
  };
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude: number;
    longitude: number;
  };
  processing_time_ms: number;
}

// Service search parameters
export interface ServiceSearchParams {
  query?: string;
  category?: string;
  subcategory?: string;
  location?: {
    lat: number;
    lon: number;
  };
  max_distance_km?: number;
  price_range?: {
    min: number;
    max: number;
  };
  service_type?: 'on_location' | 'remote' | 'hybrid' | 'at_provider';
  availability?: {
    day: string;
    time: string;
  };
  rating_min?: number;
  verified_only?: boolean;
  featured_only?: boolean;
  sort_by?: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'distance' | 'newest';
  limit?: number;
  offset?: number;
}

// Service search response
export interface ServiceSearchResponse {
  results: Service[];
  total_results: number;
  search_time_ms: number;
  filters_applied: ServiceSearchParams;
  suggestions?: string[];
  facets?: {
    categories: Array<{ name: string; count: number }>;
    price_ranges: Array<{ range: string; count: number }>;
    locations: Array<{ city: string; count: number }>;
    ratings: Array<{ rating: number; count: number }>;
  };
}

// Service update data
export interface ServiceUpdateData {
  title?: string;
  description?: string;
  category?: string;
  subcategory?: string;
  keywords?: string[];
  price_min?: number;
  price_max?: number;
  pricing_model?: string;
  is_negotiable?: boolean;
  service_type?: string;
  service_radius_km?: number;
  duration_minutes?: number;
  availability?: Array<{
    day: string;
    from_time: string;
    to_time: string;
  }>;
  qualifications?: string[];
  experience_years?: number;
  languages?: string[];
  images?: string[];
  videos?: string[];
  documents?: string[];
  is_active?: boolean;
}

// Service analytics
export interface ServiceAnalytics {
  service_id: string;
  views: {
    total: number;
    this_week: number;
    this_month: number;
  };
  inquiries: {
    total: number;
    this_week: number;
    this_month: number;
  };
  bookings: {
    total: number;
    this_week: number;
    this_month: number;
  };
  revenue: {
    total: number;
    this_week: number;
    this_month: number;
  };
  rating_breakdown: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
  top_keywords: Array<{ keyword: string; count: number }>;
  geographic_distribution: Array<{ city: string; count: number }>;
  performance_score: number;
  recommendations: string[];
}

// Service categories
export interface ServiceCategory {
  name: string;
  display_name: string;
  description: string;
  icon: string;
  subcategories: Array<{
    name: string;
    display_name: string;
    description: string;
  }>;
  service_count: number;
  is_popular: boolean;
}

// Service review
export interface ServiceReview {
  review_id: string;
  service_id: string;
  user_id: string;
  rating: number;
  title: string;
  comment: string;
  aspects: {
    quality: number;
    communication: number;
    timeliness: number;
    value: number;
  };
  images?: string[];
  is_verified: boolean;
  helpful_count: number;
  created_at: string;
  updated_at: string;
  
  // User details (populated when needed)
  user?: {
    full_name: string;
    profile_image_url?: string;
    verification_status: boolean;
  };
}

// Service booking
export interface ServiceBooking {
  booking_id: string;
  service_id: string;
  user_id: string;
  provider_id: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  scheduled_date: string;
  scheduled_time: string;
  duration_minutes: number;
  location: {
    type: 'user_location' | 'provider_location' | 'custom';
    address?: string;
    coordinates?: { lat: number; lon: number };
  };
  pricing: {
    base_price: number;
    additional_charges: Array<{ name: string; amount: number }>;
    discount: number;
    total_amount: number;
    currency: string;
  };
  special_instructions?: string;
  created_at: string;
  updated_at: string;
}
