"""
HappiDost Platform Configuration
"""
import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    app_name: str = "HappiDost AI Backend"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_prefix: str = Field(default="/api/v1", env="API_PREFIX")
    
    # Database Configuration
    database_url: str = Field(env="DATABASE_URL")
    database_echo: bool = Field(default=False, env="DATABASE_ECHO")

    # Supabase Configuration
    supabase_url: str = Field(env="SUPABASE_URL")
    supabase_key: str = Field(env="SUPABASE_KEY")
    supabase_service_key: Optional[str] = Field(default=None, env="SUPABASE_SERVICE_KEY")

    # Vespa Configuration
    vespa_endpoint: str = Field(default="http://localhost:8080", env="VESPA_ENDPOINT")
    vespa_api_key: Optional[str] = Field(default=None, env="VESPA_API_KEY")
    
    # Vector Database Configuration
    vector_db_type: str = Field(default="qdrant", env="VECTOR_DB_TYPE")  # qdrant, pinecone, faiss
    qdrant_host: str = Field(default="localhost", env="QDRANT_HOST")
    qdrant_port: int = Field(default=6333, env="QDRANT_PORT")
    qdrant_api_key: Optional[str] = Field(default=None, env="QDRANT_API_KEY")
    pinecone_api_key: Optional[str] = Field(default=None, env="PINECONE_API_KEY")
    pinecone_environment: Optional[str] = Field(default=None, env="PINECONE_ENVIRONMENT")
    
    # AI Configuration
    openai_api_key: str = Field(env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    default_llm_model: str = Field(default="gpt-4o-2024-11-20", env="DEFAULT_LLM_MODEL")
    embedding_model: str = Field(default="text-embedding-3-small", env="EMBEDDING_MODEL")
    max_tokens: int = Field(default=1000, env="MAX_TOKENS")
    temperature: float = Field(default=0.1, env="TEMPERATURE")
    
    # DOST Configuration
    dost_semantic_version: str = Field(default="1.0.0", env="DOST_SEMANTIC_VERSION")
    max_session_duration: int = Field(default=3600, env="MAX_SESSION_DURATION")  # seconds
    event_retention_days: int = Field(default=30, env="EVENT_RETENTION_DAYS")
    
    # Security
    secret_key: str = Field(env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    # Rate Limiting
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, env="RATE_LIMIT_WINDOW")
    
    # Service Discovery
    service_similarity_threshold: float = Field(default=0.7, env="SERVICE_SIMILARITY_THRESHOLD")
    max_search_results: int = Field(default=10, env="MAX_SEARCH_RESULTS")
    
    # WebSocket Configuration
    websocket_timeout: int = Field(default=300, env="WEBSOCKET_TIMEOUT")
    max_connections_per_user: int = Field(default=3, env="MAX_CONNECTIONS_PER_USER")
    
    # File Upload
    max_file_size: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    allowed_file_types: List[str] = Field(
        default=["image/jpeg", "image/png", "audio/mpeg", "audio/wav"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # External APIs
    payment_gateway_url: Optional[str] = Field(default=None, env="PAYMENT_GATEWAY_URL")
    identity_verification_url: Optional[str] = Field(default=None, env="IDENTITY_VERIFICATION_URL")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=8001, env="METRICS_PORT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Environment-specific configurations
class DevelopmentConfig(Settings):
    debug: bool = True
    database_echo: bool = True
    log_level: str = "DEBUG"


class ProductionConfig(Settings):
    debug: bool = False
    database_echo: bool = False
    log_level: str = "INFO"
    

class TestingConfig(Settings):
    debug: bool = True
    database_url: str = "sqlite:///./test.db"
    

def get_settings() -> Settings:
    """Get settings based on environment."""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    elif env == "testing":
        return TestingConfig()
    else:
        return DevelopmentConfig()


# Export the configured settings
settings = get_settings()
