"""
Base AI Assistant Class
Foundation for all AI assistants in the HappiDost platform
"""
import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session

from ...dost.events.models import DOSTEvent, DOSTEventMessage, DOSTEventTag, EventType
from ...ai.models.llm_client import LLMClient
from ...ai.nlp.context_manager import ContextManager
from ...core.config import settings

logger = logging.getLogger(__name__)


class BaseAssistant(ABC):
    """
    Base class for all AI assistants in the DOST ecosystem.
    Provides common functionality for conversation management, context handling, and response generation.
    """
    
    def __init__(self, entity_id: str, assistant_type: str, name: str):
        self.entity_id = entity_id
        self.assistant_type = assistant_type
        self.name = name
        self.llm_client = LLMClient()
        self.context_manager = ContextManager()
        
        # Assistant configuration
        self.model_config = {
            "model": settings.default_llm_model,
            "temperature": settings.temperature,
            "max_tokens": settings.max_tokens,
            "response_format": {"type": "json_object"}
        }
        
        # Conversation memory
        self.conversation_memory: Dict[str, List[Dict]] = {}
    
    @abstractmethod
    async def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get the system prompt for this assistant."""
        pass
    
    @abstractmethod
    async def process_event(self, event: DOSTEvent, db: Session) -> Optional[DOSTEvent]:
        """Process an incoming DOST event and generate a response."""
        pass
    
    async def generate_response(
        self, 
        event: DOSTEvent, 
        context: Dict[str, Any], 
        db: Session
    ) -> Dict[str, Any]:
        """
        Generate AI response using LLM.
        
        Args:
            event: Incoming DOST event
            context: Conversation context
            db: Database session
            
        Returns:
            Parsed AI response as dictionary
        """
        try:
            # 1. Get system prompt
            system_prompt = await self.get_system_prompt(context)
            
            # 2. Prepare conversation history
            conversation_history = await self._get_conversation_history(event.session_id, db)
            
            # 3. Build messages for LLM
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Add context information
            if context:
                context_message = self._format_context_for_llm(context)
                messages.append({"role": "system", "content": context_message})
            
            # Add conversation history
            messages.extend(conversation_history[-10:])  # Last 10 messages
            
            # Add current user input
            user_message = self._extract_user_message(event)
            if user_message:
                messages.append({"role": "user", "content": user_message})
            
            # 4. Generate response
            response = await self.llm_client.generate_response(messages, self.model_config)
            
            # 5. Parse and validate response
            parsed_response = self._parse_ai_response(response)
            
            logger.info(f"Generated response for event {event.event_id}")
            return parsed_response
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                "response": "I apologize, but I encountered an error processing your request. Please try again.",
                "extracted_keywords": ["error"],
                "confidence": 0.0
            }
    
    def _extract_user_message(self, event: DOSTEvent) -> Optional[str]:
        """Extract user message from DOST event."""
        if event.dost_event_message.text:
            return event.dost_event_message.text.get("chat", "")
        return None
    
    def _format_context_for_llm(self, context: Dict[str, Any]) -> str:
        """Format context information for LLM consumption."""
        formatted_context = "CONTEXT INFORMATION:\n"
        
        # User information
        if "user_info" in context:
            formatted_context += f"User: {json.dumps(context['user_info'], indent=2)}\n\n"
        
        # Service information
        if "service_info" in context:
            formatted_context += f"Service: {json.dumps(context['service_info'], indent=2)}\n\n"
        
        # Session information
        if "session_info" in context:
            formatted_context += f"Session: {json.dumps(context['session_info'], indent=2)}\n\n"
        
        # Additional context
        for key, value in context.items():
            if key not in ["user_info", "service_info", "session_info"]:
                formatted_context += f"{key}: {json.dumps(value, indent=2)}\n\n"
        
        return formatted_context
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response and ensure required fields."""
        try:
            # Clean up response
            cleaned_response = response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]
            
            parsed = json.loads(cleaned_response)
            
            # Ensure required fields
            if "response" not in parsed:
                parsed["response"] = "I'm here to help you."
            
            if "extracted_keywords" not in parsed:
                parsed["extracted_keywords"] = []
            
            if "confidence" not in parsed:
                parsed["confidence"] = 0.8
            
            return parsed
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response: {str(e)}")
            logger.error(f"Raw response: {response}")
            
            return {
                "response": "I apologize, but I encountered an error processing your request.",
                "extracted_keywords": ["error"],
                "confidence": 0.0
            }
    
    async def _get_conversation_history(self, session_id: str, db: Session) -> List[Dict[str, str]]:
        """Get conversation history for a session."""
        try:
            from ...core.database import DOSTEvent as DOSTEventDB
            
            # Get recent events from this session
            events = db.query(DOSTEventDB).filter(
                DOSTEventDB.session_id == session_id,
                DOSTEventDB.event_type == EventType.MESSAGE.value
            ).order_by(DOSTEventDB.timestamp.desc()).limit(20).all()
            
            # Convert to conversation format
            conversation = []
            for event in reversed(events):  # Chronological order
                if event.message_content and event.message_content.get("text"):
                    role = "user" if not event.is_ai_generated else "assistant"
                    content = event.message_content["text"].get("chat", "")
                    if content:
                        conversation.append({"role": role, "content": content})
            
            return conversation
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}")
            return []
    
    async def create_response_event(
        self, 
        original_event: DOSTEvent, 
        response_data: Dict[str, Any]
    ) -> DOSTEvent:
        """Create a response DOST event."""
        try:
            # Extract response text
            response_text = response_data.get("response", "")
            keywords = response_data.get("extracted_keywords", [])
            
            # Create response message
            response_message = DOSTEventMessage(
                text={"chat": response_text, "language": "en"}
            )
            
            # Create response tags
            response_tags = []
            for keyword in keywords:
                response_tags.append(DOSTEventTag(
                    tag=keyword,
                    confidence=response_data.get("confidence", 0.8),
                    source_entity_id=self.entity_id
                ))
            
            # Create response event
            response_event = DOSTEvent(
                session_id=original_event.session_id,
                source_entity_id=self.entity_id,
                target_entity_id=original_event.source_entity_id,
                event_type=EventType.RESPONSE,
                is_ai_generated=True,
                dost_event_message=response_message,
                dost_event_tag=response_tags,
                context=response_data,
                correlation_id=original_event.event_id,
                parent_event_id=original_event.event_id
            )
            
            return response_event
            
        except Exception as e:
            logger.error(f"Error creating response event: {str(e)}")
            raise
    
    async def update_conversation_memory(self, session_id: str, event: DOSTEvent):
        """Update conversation memory for this session."""
        try:
            if session_id not in self.conversation_memory:
                self.conversation_memory[session_id] = []
            
            # Add event to memory
            memory_entry = {
                "timestamp": event.timestamp.isoformat(),
                "source": event.source_entity_id,
                "target": event.target_entity_id,
                "content": event.dost_event_message.dict(),
                "tags": [tag.dict() for tag in event.dost_event_tag]
            }
            
            self.conversation_memory[session_id].append(memory_entry)
            
            # Keep only last 50 entries per session
            if len(self.conversation_memory[session_id]) > 50:
                self.conversation_memory[session_id] = self.conversation_memory[session_id][-50:]
                
        except Exception as e:
            logger.error(f"Error updating conversation memory: {str(e)}")
    
    def get_assistant_info(self) -> Dict[str, Any]:
        """Get assistant information."""
        return {
            "entity_id": self.entity_id,
            "assistant_type": self.assistant_type,
            "name": self.name,
            "capabilities": getattr(self, 'capabilities', []),
            "status": "active"
        }
    
    async def handle_error(self, error: Exception, event: DOSTEvent) -> DOSTEvent:
        """Handle errors and create appropriate error response."""
        logger.error(f"Assistant {self.entity_id} error: {str(error)}")
        
        error_response = {
            "response": "I apologize, but I encountered an error. Please try again or contact support.",
            "extracted_keywords": ["error", "system_error"],
            "confidence": 0.0,
            "error_type": type(error).__name__
        }
        
        return await self.create_response_event(event, error_response)
