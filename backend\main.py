"""
HappiDost AI Backend - Main Application
FastAPI application with WebSocket support for real-time AI interactions
"""
import os
import json
import logging
import asyncio
from datetime import datetime
from contextlib import asynccontextmanager
from typing import Dict, List, Any
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from sqlalchemy.orm import Session

# Import core modules
from backend.core.config import settings
from backend.core.database import get_db, create_tables, init_database

# Import API routers
from backend.api.v1.chat import chat_router
from backend.api.v1.services import services_router
from backend.api.v1.auth import auth_router
from backend.api.v1.events import events_router
from backend.api.v1.objects import objects_router
# from backend.api.v1.service_registration import router as service_registration_router

# Import middleware
from backend.api.middleware.auth_middleware import AuthMiddleware
from backend.api.middleware.logging_middleware import LoggingMiddleware

# Import AI components
from backend.ai.assistants.user_assistant import user_assistant
from backend.ai.orchestration.agent_manager import agent_manager
from backend.dost.events.processor import event_processor

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting HappiDost AI Backend...")
    
    # Create database tables
    create_tables()
    
    # Initialize database with default data
    await init_database()
    
    # Initialize AI components
    await agent_manager.initialize()
    
    logger.info("HappiDost AI Backend started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down HappiDost AI Backend...")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI-powered backend for the HappiDost platform",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(LoggingMiddleware)

# Include API routers
app.include_router(auth_router, prefix=f"{settings.api_prefix}/auth", tags=["authentication"])
app.include_router(chat_router, prefix=f"{settings.api_prefix}/chat", tags=["chat"])
app.include_router(services_router, prefix=f"{settings.api_prefix}/services", tags=["services"])
app.include_router(events_router, prefix=f"{settings.api_prefix}/events", tags=["events"])
app.include_router(objects_router, prefix=f"{settings.api_prefix}/objects", tags=["dost-objects"])
# app.include_router(service_registration_router, prefix=f"{settings.api_prefix}", tags=["service-registration"])


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.app_version,
        "environment": settings.environment
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to HappiDost AI Backend",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health"
    }


# WebSocket connection manager
class ConnectionManager:
    """Manages WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.user_sessions: Dict[str, str] = {}  # user_id -> session_id
    
    async def connect(self, websocket: WebSocket, user_id: str, session_id: str):
        """Accept WebSocket connection."""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        
        self.active_connections[user_id].append(websocket)
        self.user_sessions[user_id] = session_id
        
        logger.info(f"User {user_id} connected to session {session_id}")
    
    def disconnect(self, websocket: WebSocket, user_id: str):
        """Remove WebSocket connection."""
        if user_id in self.active_connections:
            if websocket in self.active_connections[user_id]:
                self.active_connections[user_id].remove(websocket)
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
                if user_id in self.user_sessions:
                    del self.user_sessions[user_id]
        
        logger.info(f"User {user_id} disconnected")
    
    async def send_personal_message(self, message: str, user_id: str):
        """Send message to specific user."""
        if user_id in self.active_connections:
            for connection in self.active_connections[user_id]:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending message to {user_id}: {str(e)}")
    
    async def broadcast(self, message: str):
        """Broadcast message to all connected users."""
        for user_id, connections in self.active_connections.items():
            for connection in connections:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to {user_id}: {str(e)}")


# Global connection manager
manager = ConnectionManager()


# Main WebSocket endpoint for HappiDost chat
@app.websocket("/ws/chat/{user_id}")
async def websocket_chat_endpoint(websocket: WebSocket, user_id: str, db: Session = Depends(get_db)):
    """
    Main WebSocket endpoint for HappiDost AI chat.
    Handles real-time conversations between users and AI assistants.
    """
    session_id = None
    
    try:
        # Create or get session
        from backend.core.database import create_session
        session = create_session([user_id, "ai.happidost.user_assistant"], "user_ai_chat", db)
        session_id = str(session.session_id)
        
        # Connect user
        await manager.connect(websocket, user_id, session_id)
        
        # Send welcome message
        welcome_response = {
            "type": "welcome",
            "message": "Welcome to HappiDost! I'm your AI assistant. How can I help you today?",
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_response))
        
        # Main message loop
        while True:
            # Receive message from user
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process different message types
            if message_data.get("type") == "chat":
                await handle_chat_message(websocket, user_id, session_id, message_data, db)
            elif message_data.get("type") == "audio":
                await handle_audio_message(websocket, user_id, session_id, message_data, db)
            elif message_data.get("type") == "service_request":
                await handle_service_request(websocket, user_id, session_id, message_data, db)
            else:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Unknown message type",
                    "timestamp": datetime.utcnow().isoformat()
                }))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        logger.info(f"User {user_id} disconnected from session {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {str(e)}")
        try:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "An error occurred. Please try again.",
                "timestamp": datetime.utcnow().isoformat()
            }))
        except:
            pass
        manager.disconnect(websocket, user_id)


async def handle_chat_message(
    websocket: WebSocket, 
    user_id: str, 
    session_id: str, 
    message_data: Dict[str, Any], 
    db: Session
):
    """Handle text chat messages."""
    try:
        from backend.dost.events.models import create_text_event, EventType
        
        # Create DOST event
        event = create_text_event(
            session_id=session_id,
            source_entity_id=user_id,
            target_entity_id="ai.happidost.user_assistant",
            text_content=message_data.get("message", ""),
            event_type=EventType.MESSAGE
        )
        
        # Process event
        response_event = await user_assistant.process_event(event, db)
        
        if response_event:
            # Extract response data
            response_text = ""
            if response_event.dost_event_message.text:
                response_text = response_event.dost_event_message.text.get("chat", "")
            
            # Send response
            response = {
                "type": "chat_response",
                "message": response_text,
                "event_id": response_event.event_id,
                "timestamp": response_event.timestamp.isoformat(),
                "metadata": response_event.context or {}
            }
            
            await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        logger.error(f"Error handling chat message: {str(e)}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Failed to process message",
            "timestamp": datetime.utcnow().isoformat()
        }))


async def handle_audio_message(
    websocket: WebSocket, 
    user_id: str, 
    session_id: str, 
    message_data: Dict[str, Any], 
    db: Session
):
    """Handle audio messages."""
    try:
        # Transcribe audio to text
        audio_data = message_data.get("audio_data", "")
        if audio_data:
            # Decode base64 audio
            import base64
            audio_bytes = base64.b64decode(audio_data)
            
            # Transcribe using LLM client
            from backend.ai.models.llm_client import llm_client
            transcribed_text = await llm_client.transcribe_audio(audio_bytes)
            
            if transcribed_text:
                # Process as text message
                text_message_data = {
                    "type": "chat",
                    "message": transcribed_text
                }
                await handle_chat_message(websocket, user_id, session_id, text_message_data, db)
            else:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Could not transcribe audio",
                    "timestamp": datetime.utcnow().isoformat()
                }))
        
    except Exception as e:
        logger.error(f"Error handling audio message: {str(e)}")


async def handle_service_request(
    websocket: WebSocket, 
    user_id: str, 
    session_id: str, 
    message_data: Dict[str, Any], 
    db: Session
):
    """Handle service requests."""
    try:
        # Implementation for service requests
        service_id = message_data.get("service_id")
        request_details = message_data.get("details", {})
        
        # Create service request event
        # Implementation will be added in service assistant
        
        response = {
            "type": "service_response",
            "message": f"Processing service request for service {service_id}",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        logger.error(f"Error handling service request: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
