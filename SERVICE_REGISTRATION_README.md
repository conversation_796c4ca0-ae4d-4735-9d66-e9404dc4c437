# HappiDost Service Registration System

## 🎯 Overview

The HappiDost Service Registration System is a comprehensive AI-powered platform that allows service providers to register their services using natural language descriptions. The system uses GPT-4 to extract structured data, stores it in Supabase, and indexes it in Vespa for intelligent search and discovery.

## 🏗️ Architecture

### Components

1. **Supabase Database** - Primary data storage with PostGIS for location data
2. **Vespa Search Engine** - Vector and hybrid search with HNSW indexing
3. **GPT-4 Data Extraction** - Natural language to structured data conversion
4. **FastAPI Backend** - RESTful API endpoints
5. **Real-time Sync** - Automatic synchronization between Supabase and Vespa

### Data Flow

```
Natural Language Description
         ↓
    GPT-4 Extraction
         ↓
    Structured Data
         ↓
   Supabase Storage ←→ Vespa Indexing
         ↓
    Search & Discovery
```

## 📋 Database Schema

### Supabase Tables

#### `profiles`
- User profiles with role-based access
- Supports users, service providers, businesses, admins
- Includes reputation scoring and verification status

#### `services`
- Complete service information with pricing, location, availability
- Supports multiple pricing models and service types
- Includes AI metadata for search optimization

#### `transactions`
- Service bookings and financial transactions
- Complete audit trail with status tracking

#### `chat_sessions` & `chat_messages`
- DOST event-based communication system
- Supports multimodal messages (text, audio, video, images)

#### `reviews`
- Service reviews and ratings with aspect-based scoring

#### `vespa_sync`
- Synchronization tracking between Supabase and Vespa

## 🔍 Vespa Search Configuration

### Schema Features

- **Hybrid Search**: BM25 + Vector similarity + Quality boosting
- **HNSW Indexing**: Optimized vector search with 1536 dimensions
- **Multiple Rank Profiles**: 
  - `hybrid_search` - Combined text and semantic search
  - `semantic_search` - Pure vector similarity
  - `location_search` - Geographic proximity
  - `popular` - Trending/featured services
  - `price_search` - Price-optimized ranking

### Search Capabilities

- **Text Search**: BM25 on title, description, keywords
- **Semantic Search**: Vector similarity using OpenAI embeddings
- **Location Search**: Geographic proximity with radius filtering
- **Metadata Filtering**: Category, price range, ratings, verification status
- **Reranking**: Second-phase ranking with quality signals

## 🤖 AI-Powered Data Extraction

### LLM Prompt Engineering

The system uses a carefully crafted prompt to extract structured data from natural language:

```python
extraction_prompt = f"""
You are an expert service data extractor for the HappiDost platform. 
Extract structured information from the following service description.

Service Description:
{description}

Extract the following information and return as JSON:
{{
  "title": "Short, descriptive title for the service",
  "description": "Clean, professional description",
  "category": "Main category (Education, Healthcare, etc.)",
  "subcategory": "Specific subcategory",
  "keywords": ["array", "of", "relevant", "keywords"],
  "price_min": 0.0,
  "price_max": 0.0,
  "pricing_model": "hourly|fixed|daily|negotiable",
  "is_negotiable": true/false,
  "service_type": "on_location|remote|hybrid|at_provider",
  "availability": [
    {{"day": "mon", "from_time": "09:00", "to_time": "17:00"}}
  ],
  "qualifications": ["list", "of", "qualifications"],
  "experience_years": 0,
  "languages": ["English", "Hindi"]
}}
"""
```

### Extraction Capabilities

- **Service Information**: Title, description, category classification
- **Pricing**: Min/max prices, pricing models, negotiability
- **Location**: Geographic information with geocoding
- **Availability**: Schedule parsing with timezone support
- **Qualifications**: Experience and certification extraction
- **Keywords**: SEO-optimized keyword generation

## 🚀 API Endpoints

### Service Registration

#### `POST /api/v1/service-registration/register`

Register a new service using natural language description.

**Request:**
```json
{
  "natural_language_description": "I am a math tutor with 5 years of experience...",
  "provider_location": "Marathahalli, Bangalore",
  "additional_context": {}
}
```

**Response:**
```json
{
  "success": true,
  "service_id": "svc_48321abc",
  "supabase_id": "uuid-here",
  "vespa_document_id": "service::svc_48321abc",
  "extracted_data": { /* structured data */ },
  "location": { /* geocoded location */ },
  "processing_time_ms": 1250
}
```

### Service Management

#### `GET /api/v1/service-registration/my-services`
Get all services for the current user

#### `GET /api/v1/service-registration/service/{service_id}`
Get detailed service information

#### `PUT /api/v1/service-registration/service/{service_id}`
Update an existing service

#### `DELETE /api/v1/service-registration/service/{service_id}`
Delete/deactivate a service

### Search

#### `POST /api/v1/service-registration/search`

Search services using hybrid search.

**Request:**
```json
{
  "query": "math tutor near me",
  "location": {"lat": 12.9716, "lon": 77.5946},
  "max_distance_km": 10,
  "category": "Education",
  "price_range": {"min": 100, "max": 1000},
  "limit": 20
}
```

## 🛠️ Setup Instructions

### 1. Environment Configuration

Create a `.env` file with:

```env
# OpenAI
OPENAI_API_KEY=your_openai_key

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key

# Vespa
VESPA_ENDPOINT=http://localhost:8080
VESPA_DB=your_vespa_key
```

### 2. Database Setup

```bash
# Run the database setup script
python setup_database.py
```

This will:
- Initialize Supabase connection
- Provide SQL schema for manual execution
- Setup Vespa schema files
- Create sample data
- Test the system

### 3. Manual Steps

#### Supabase Setup
1. Go to your Supabase project SQL editor
2. Run the SQL from `database/supabase_schema.sql`
3. Enable Row Level Security policies as needed

#### Vespa Setup
1. Deploy the schema from `vespa/services.sd` to your Vespa instance
2. Configure the application package
3. Start the Vespa services

### 4. Start the Server

```bash
# Install dependencies
pip install -r requirements.txt

# Start the FastAPI server
python -m backend.main
```

## 📊 Example Usage

### Natural Language Input

```text
I am a professional home cleaning service provider in Bangalore. I have been 
providing cleaning services for the past 3 years with a team of trained staff. 
We offer deep cleaning, regular maintenance, and move-in/move-out cleaning services. 
Our rates start from ₹15 per square foot for regular cleaning and ₹25 per square 
foot for deep cleaning. We are available Monday to Saturday from 8 AM to 6 PM. 
We serve all areas within 15 km of Koramangala. We use eco-friendly products 
and provide our own equipment. Advance booking required.
```

### Extracted Structured Data

```json
{
  "title": "Professional Home Cleaning Services",
  "description": "Experienced home cleaning service with trained staff offering deep cleaning, regular maintenance, and move-in/move-out services using eco-friendly products.",
  "category": "Home Services",
  "subcategory": "Cleaning",
  "keywords": ["home cleaning", "deep cleaning", "eco-friendly", "koramangala", "bangalore"],
  "price_min": 15.0,
  "price_max": 25.0,
  "currency": "INR",
  "pricing_model": "per_sqft",
  "is_negotiable": false,
  "service_type": "on_location",
  "service_radius_km": 15,
  "availability": [
    {"day": "mon", "from_time": "08:00", "to_time": "18:00"},
    {"day": "tue", "from_time": "08:00", "to_time": "18:00"},
    {"day": "wed", "from_time": "08:00", "to_time": "18:00"},
    {"day": "thu", "from_time": "08:00", "to_time": "18:00"},
    {"day": "fri", "from_time": "08:00", "to_time": "18:00"},
    {"day": "sat", "from_time": "08:00", "to_time": "18:00"}
  ],
  "qualifications": ["3 years experience", "trained staff"],
  "experience_years": 3,
  "languages": ["English"]
}
```

## 🔍 Search Examples

### Text Search
```
Query: "math tutor"
Results: Services with "math", "tutor", "mathematics", "teaching" in title/description
```

### Semantic Search
```
Query: "help with calculus homework"
Results: Math tutoring services, even if they don't explicitly mention "calculus"
```

### Location Search
```
Query: "cleaning service"
Location: Koramangala, Bangalore
Radius: 10km
Results: Cleaning services within 10km of Koramangala
```

### Hybrid Search
```
Query: "experienced yoga instructor near marathahalli"
Results: Combines text matching + semantic understanding + location proximity
```

## 🎯 Key Features

### ✅ Completed Features

1. **Natural Language Processing**: GPT-4 powered data extraction
2. **Database Integration**: Complete Supabase schema with relationships
3. **Vector Search**: Vespa integration with HNSW indexing
4. **Geocoding**: Automatic location resolution
5. **API Endpoints**: Full CRUD operations for services
6. **Authentication**: JWT-based user authentication
7. **Real-time Sync**: Supabase ↔ Vespa synchronization
8. **Multi-modal Support**: Text, location, pricing, availability
9. **Quality Scoring**: Reputation and verification systems
10. **Search Optimization**: Multiple ranking profiles

### 🚧 Future Enhancements

1. **Image Processing**: Extract information from service images
2. **Voice Input**: Speech-to-text service registration
3. **Auto-categorization**: ML-based category prediction
4. **Price Optimization**: Dynamic pricing suggestions
5. **Quality Assurance**: Automated service verification
6. **Analytics Dashboard**: Provider performance metrics
7. **Mobile App**: React Native mobile application
8. **Multi-language**: Support for regional languages

## 🎪 Benefits

### For Service Providers
- **Easy Registration**: Natural language input, no complex forms
- **Intelligent Categorization**: AI-powered service classification
- **Optimized Visibility**: SEO-optimized keywords and descriptions
- **Location Intelligence**: Automatic geocoding and radius management
- **Performance Tracking**: Analytics and reputation management

### For Customers
- **Smart Discovery**: Find services using natural language
- **Location-aware**: Services near your location
- **Quality Assurance**: Verified providers with ratings
- **Comprehensive Information**: Complete service details
- **Real-time Communication**: DOST event-based messaging

### For Platform
- **Scalable Architecture**: Handle millions of services
- **AI-powered**: Intelligent matching and recommendations
- **Data Quality**: Structured, consistent service data
- **Search Performance**: Sub-second search responses
- **Business Intelligence**: Rich analytics and insights

This Service Registration System forms the foundation of the HappiDost DSS (DOST Service Store) and enables the platform's vision of AI-powered service discovery and interaction! 🚀
