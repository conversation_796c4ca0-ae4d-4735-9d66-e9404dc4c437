#!/usr/bin/env python3
"""
Test Vespa connection and search functionality
"""
import os
import sys
import asyncio
import httpx
import json
from datetime import datetime

# Add backend to path
sys.path.append('backend')

# Vespa configuration
VESPA_API_KEY = "MIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQg3CTfQFimRGNDcqqAzOP283Xqk4FlDwH+IGP9/0rACGuhRANCAASlyak/jMBAk6PaQZ0azR+GMo4azmGv5F8F2pk0K5puWYvDK30NnLWVr6czuqHm+1xNQEw7KmSAbEa8wh+qQAgj"
VESPA_ENDPOINT = "https://happidost.happidostdb.aws-us-east-1c.dev.vespa-cloud.com"

async def test_vespa_connection():
    """Test Vespa connection"""
    try:
        print("🔗 Testing Vespa connection...")
        
        # Create HTTP client with API key
        headers = {
            "Authorization": f"Bearer {VESPA_API_KEY}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            # Test connection with a simple query
            response = await client.get(
                f"{VESPA_ENDPOINT}/status",
                headers=headers,
                timeout=10.0
            )
            
            if response.status_code == 200:
                print("✅ Vespa connection successful!")
                return True
            else:
                print(f"❌ Vespa connection failed: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Vespa connection failed: {str(e)}")
        print("   Note: You need to provide the correct Vespa endpoint and API key")
        return False

async def test_vespa_document_operations():
    """Test Vespa document operations"""
    try:
        print("\n📄 Testing Vespa document operations...")
        
        headers = {
            "Authorization": f"Bearer {VESPA_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Test document to index
        test_document = {
            "fields": {
                "service_id": "srv.test.service.1234",
                "title": "Test Web Development Service",
                "description": "Professional web development services including React, Node.js, and database design",
                "category": "Technology",
                "subcategory": "Web Development",
                "keywords": ["web", "development", "react", "nodejs", "database"],
                "price_min": 50.0,
                "price_max": 150.0,
                "location": {
                    "city": "San Francisco",
                    "state": "CA",
                    "country": "USA",
                    "coordinates": [37.7749, -122.4194]
                },
                "provider_id": "hum.test.provider.1234",
                "rating": 4.8,
                "total_reviews": 25,
                "embedding": [0.1] * 1536  # Mock embedding vector
            }
        }
        
        async with httpx.AsyncClient() as client:
            # Try to index a document
            document_id = "test_service_1234"
            response = await client.put(
                f"{VESPA_ENDPOINT}/document/v1/happidost/service/docid/{document_id}",
                headers=headers,
                json=test_document,
                timeout=10.0
            )
            
            if response.status_code in [200, 201]:
                print("✅ Document indexed successfully!")
                return True
            else:
                print(f"❌ Document indexing failed: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Vespa document operations failed: {str(e)}")
        return False

async def test_vespa_search():
    """Test Vespa search functionality"""
    try:
        print("\n🔍 Testing Vespa search...")
        
        headers = {
            "Authorization": f"Bearer {VESPA_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Test search query
        search_query = {
            "yql": "select * from sources service where userQuery()",
            "query": "web development",
            "hits": 10,
            "ranking": "default"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{VESPA_ENDPOINT}/search/",
                headers=headers,
                json=search_query,
                timeout=10.0
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ Search successful! Found {results.get('root', {}).get('fields', {}).get('totalCount', 0)} results")
                return True
            else:
                print(f"❌ Search failed: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Vespa search failed: {str(e)}")
        return False

async def test_vespa_vector_search():
    """Test Vespa vector search functionality"""
    try:
        print("\n🎯 Testing Vespa vector search...")
        
        headers = {
            "Authorization": f"Bearer {VESPA_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Test vector search query
        query_vector = [0.1] * 1536  # Mock query vector
        
        vector_search_query = {
            "yql": "select * from sources service where ({targetHits:10}nearestNeighbor(embedding,query_vector))",
            "input.query(query_vector)": query_vector,
            "hits": 10,
            "ranking": "semantic"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{VESPA_ENDPOINT}/search/",
                headers=headers,
                json=vector_search_query,
                timeout=10.0
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ Vector search successful! Found {results.get('root', {}).get('fields', {}).get('totalCount', 0)} results")
                return True
            else:
                print(f"❌ Vector search failed: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Vespa vector search failed: {str(e)}")
        return False

def print_vespa_setup_instructions():
    """Print instructions for setting up Vespa"""
    print("\n📋 Vespa Setup Instructions:")
    print("=" * 50)
    print("1. Sign up for Vespa Cloud at https://cloud.vespa.ai/")
    print("2. Create a new application")
    print("3. Deploy your application schema")
    print("4. Get your API endpoint and key")
    print("5. Update the VESPA_API_KEY and VESPA_ENDPOINT in this test file")
    print("6. Update your .env file with the correct Vespa configuration")
    print("\nExample Vespa schema for HappiDost services:")
    print("""
schema service {
    document service {
        field service_id type string {
            indexing: summary | attribute
        }
        field title type string {
            indexing: summary | index
        }
        field description type string {
            indexing: summary | index
        }
        field category type string {
            indexing: summary | attribute
        }
        field keywords type array<string> {
            indexing: summary | attribute
        }
        field price_min type double {
            indexing: summary | attribute
        }
        field price_max type double {
            indexing: summary | attribute
        }
        field location type map<string,string> {
            indexing: summary
        }
        field embedding type tensor<float>(x[1536]) {
            indexing: summary | attribute
        }
        field rating type double {
            indexing: summary | attribute
        }
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, description) + attribute(rating)
        }
    }
    
    rank-profile semantic inherits default {
        first-phase {
            expression: closeness(field, embedding)
        }
    }
}
    """)

async def main():
    """Main test function"""
    print("🚀 Starting Vespa Integration Test\n")
    
    # Test connection
    connection_success = await test_vespa_connection()
    
    if connection_success:
        # Test document operations
        doc_success = await test_vespa_document_operations()
        
        # Test search
        search_success = await test_vespa_search()
        
        # Test vector search
        vector_success = await test_vespa_vector_search()
        
        if doc_success and search_success and vector_success:
            print("\n🎉 Vespa integration test completed successfully!")
        else:
            print("\n⚠️  Some Vespa operations failed. Check the configuration.")
    else:
        print_vespa_setup_instructions()
        print("\n❌ Vespa connection failed. Please set up Vespa Cloud first.")

if __name__ == "__main__":
    asyncio.run(main())
