import React from 'react';
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Link,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  Email,
  Phone,
  LocationOn,
} from '@mui/icons-material';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    'Platform': [
      { label: 'How it Works', href: '/how-it-works' },
      { label: 'For Service Providers', href: '/providers' },
      { label: 'For Customers', href: '/customers' },
      { label: 'Pricing', href: '/pricing' },
    ],
    'Support': [
      { label: 'Help Center', href: '/help' },
      { label: 'Contact Us', href: '/contact' },
      { label: 'Safety Guidelines', href: '/safety' },
      { label: 'Community Guidelines', href: '/community' },
    ],
    'Company': [
      { label: 'About Us', href: '/about' },
      { label: 'Careers', href: '/careers' },
      { label: 'Press', href: '/press' },
      { label: 'Blog', href: '/blog' },
    ],
    'Legal': [
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Cookie Policy', href: '/cookies' },
      { label: 'Refund Policy', href: '/refunds' },
    ],
  };

  const socialLinks = [
    { icon: <Facebook />, href: 'https://facebook.com/happidost', label: 'Facebook' },
    { icon: <Twitter />, href: 'https://twitter.com/happidost', label: 'Twitter' },
    { icon: <Instagram />, href: 'https://instagram.com/happidost', label: 'Instagram' },
    { icon: <LinkedIn />, href: 'https://linkedin.com/company/happidost', label: 'LinkedIn' },
  ];

  return (
    <Box
      component="footer"
      sx={{
        background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)',
        color: 'white',
        pt: 6,
        pb: 3,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Brand Section */}
          <Grid item xs={12} md={4}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              HappiDost
            </Typography>
            <Typography variant="body2" sx={{ mb: 3, color: 'grey.300' }}>
              AI-powered service discovery platform connecting customers with 
              verified service providers. Find and offer services with intelligent 
              matching and seamless communication.
            </Typography>
            
            {/* Contact Info */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Email sx={{ mr: 1, fontSize: 18 }} />
                <Typography variant="body2"><EMAIL></Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Phone sx={{ mr: 1, fontSize: 18 }} />
                <Typography variant="body2">+91 80 1234 5678</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocationOn sx={{ mr: 1, fontSize: 18 }} />
                <Typography variant="body2">Bangalore, India</Typography>
              </Box>
            </Box>

            {/* Social Links */}
            <Box>
              {socialLinks.map((social) => (
                <IconButton
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: 'grey.300',
                    mr: 1,
                    '&:hover': {
                      color: 'primary.main',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                  aria-label={social.label}
                >
                  {social.icon}
                </IconButton>
              ))}
            </Box>
          </Grid>

          {/* Links Sections */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <Grid item xs={6} sm={3} md={2} key={category}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 2,
                  fontSize: '1rem',
                }}
              >
                {category}
              </Typography>
              <Box>
                {links.map((link) => (
                  <Link
                    key={link.label}
                    href={link.href}
                    sx={{
                      display: 'block',
                      color: 'grey.300',
                      textDecoration: 'none',
                      mb: 1,
                      fontSize: '0.875rem',
                      '&:hover': {
                        color: 'primary.main',
                        textDecoration: 'underline',
                      },
                      transition: 'color 0.2s ease',
                    }}
                  >
                    {link.label}
                  </Link>
                ))}
              </Box>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 4, borderColor: 'grey.700' }} />

        {/* Bottom Section */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Typography variant="body2" color="grey.400">
            © {currentYear} HappiDost. All rights reserved.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Typography
              variant="body2"
              sx={{
                color: 'grey.400',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              🚀 Powered by AI
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: 'grey.400',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              🔒 Secure & Trusted
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: 'grey.400',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              🌍 Made in India
            </Typography>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
