"""
DOST Entity Registry
Manages entity registration, authentication, and verification
"""
import re
import uuid
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session

from ...core.database import Entity, UserProfile, ServiceProvider
from .models import EntityRegistration, EntityType, VerificationStatus

logger = logging.getLogger(__name__)


class EntityRegistry:
    """
    Manages DOST entities (humans, businesses, AI assistants).
    Handles registration, verification, and authentication.
    """
    
    def __init__(self):
        self.rdns_patterns = {
            EntityType.HUMAN: r"^hum\.[a-zA-Z]+\.[a-zA-Z]+\.\d{4}$",
            EntityType.BUSINESS: r"^com\.[a-zA-Z0-9]+\.[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*$",
            EntityType.AI_ASSISTANT: r"^ai\.[a-zA-Z0-9]+\.[a-zA-Z0-9_]+$"
        }
    
    async def register_entity(self, registration: EntityRegistration, db: Session) -> Dict[str, Any]:
        """
        Register a new entity in the DOST ecosystem.
        
        Args:
            registration: Entity registration data
            db: Database session
            
        Returns:
            Registration result with entity_id and status
        """
        try:
            # 1. Validate entity ID format
            if not self._validate_entity_id(registration.entity_id, registration.entity_type):
                return {
                    "success": False,
                    "message": f"Invalid entity ID format for {registration.entity_type}",
                    "entity_id": registration.entity_id
                }
            
            # 2. Check if entity already exists
            existing_entity = await self.get_entity(registration.entity_id, db)
            if existing_entity:
                return {
                    "success": False,
                    "message": "Entity already exists",
                    "entity_id": registration.entity_id
                }
            
            # 3. Create entity record
            # Convert Pydantic objects to dictionaries for JSON storage
            contact_info_dict = registration.contact_info.dict() if registration.contact_info else {}
            location_dict = registration.location.dict() if registration.location else {}

            entity = Entity(
                entity_id=registration.entity_id,
                entity_type=registration.entity_type.value,
                name=registration.name,
                description=registration.description,
                verification_status="pending",
                verification_documents=registration.verification_documents or {},
                contact_info=contact_info_dict,
                location=location_dict,
                additional_data=registration.additional_data or {},
                is_active=True
            )
            
            db.add(entity)
            
            # 4. Create type-specific profile
            if registration.entity_type == EntityType.HUMAN:
                user_profile = UserProfile(
                    entity_id=registration.entity_id,
                    preferences=registration.additional_data.get("preferences", {}),
                    privacy_settings=registration.additional_data.get("privacy_settings", {}),
                    notification_settings=registration.additional_data.get("notification_settings", {})
                )
                db.add(user_profile)
                
            elif registration.entity_type == EntityType.BUSINESS:
                service_provider = ServiceProvider(
                    entity_id=registration.entity_id,
                    business_info=registration.additional_data.get("business_info", {}),
                    verification_documents=registration.verification_documents or {},
                    service_categories=registration.additional_data.get("service_categories", []),
                    operating_regions=registration.additional_data.get("operating_regions", []),
                    business_hours=registration.additional_data.get("business_hours", {}),
                    payment_methods=registration.additional_data.get("payment_methods", [])
                )
                db.add(service_provider)
            
            db.commit()
            
            # 5. Trigger verification process
            await self._initiate_verification(entity, db)
            
            logger.info(f"Successfully registered entity: {registration.entity_id}")
            
            return {
                "success": True,
                "message": "Entity registered successfully",
                "entity_id": registration.entity_id,
                "verification_status": "pending"
            }
            
        except Exception as e:
            logger.error(f"Error registering entity: {str(e)}")
            db.rollback()
            return {
                "success": False,
                "message": f"Registration failed: {str(e)}",
                "entity_id": registration.entity_id
            }
    
    def _validate_entity_id(self, entity_id: str, entity_type: EntityType) -> bool:
        """Validate entity ID follows rDNS format."""
        pattern = self.rdns_patterns.get(entity_type)
        if not pattern:
            return False
        
        return bool(re.match(pattern, entity_id))
    
    async def get_entity(self, entity_id: str, db: Session) -> Optional[Entity]:
        """Get entity by ID."""
        return db.query(Entity).filter(Entity.entity_id == entity_id).first()

    async def get_entity_by_email(self, email: str, db: Session) -> Optional[Entity]:
        """Get entity by email address."""
        # Search in Entity table's contact_info JSON field
        entities = db.query(Entity).all()
        for entity in entities:
            if entity.contact_info and isinstance(entity.contact_info, dict):
                if entity.contact_info.get("email") == email:
                    return entity

        return None
    
    async def authenticate_entity(self, entity_id: str, credentials: Dict[str, Any], db: Session) -> bool:
        """Authenticate entity."""
        try:
            entity = await self.get_entity(entity_id, db)
            if not entity or not entity.is_active:
                return False

            # Get password from credentials
            password = credentials.get("password")
            if not password:
                return False

            # For MVP, get stored password from Entity's additional_data
            # In production, implement proper password hashing
            if entity.additional_data and isinstance(entity.additional_data, dict):
                stored_password = entity.additional_data.get("password")
                return stored_password == password

            return False

        except Exception as e:
            logger.error(f"Error authenticating entity: {str(e)}")
            return False
    
    async def update_entity_status(self, entity_id: str, status: str, db: Session) -> bool:
        """Update entity verification status."""
        try:
            entity = await self.get_entity(entity_id, db)
            if not entity:
                return False
            
            entity.verification_status = status
            entity.updated_at = datetime.utcnow()
            db.commit()
            
            logger.info(f"Updated entity {entity_id} status to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating entity status: {str(e)}")
            db.rollback()
            return False
    
    async def _initiate_verification(self, entity: Entity, db: Session):
        """Initiate entity verification process."""
        try:
            # For MVP, auto-approve AI assistants and mark others for manual review
            if entity.entity_type == "ai_assistant":
                entity.verification_status = "verified"
                db.commit()
            else:
                # In production, integrate with identity verification services
                logger.info(f"Entity {entity.entity_id} queued for verification")
                
        except Exception as e:
            logger.error(f"Error initiating verification: {str(e)}")
    
    async def get_entities_by_type(self, entity_type: EntityType, db: Session) -> List[Entity]:
        """Get all entities of a specific type."""
        return db.query(Entity).filter(
            Entity.entity_type == entity_type.value,
            Entity.is_active == True
        ).all()
    
    async def search_entities(self, query: str, entity_type: Optional[EntityType], db: Session) -> List[Entity]:
        """Search entities by name or description."""
        query_filter = Entity.name.ilike(f"%{query}%") | Entity.description.ilike(f"%{query}%")
        
        if entity_type:
            query_filter = query_filter & (Entity.entity_type == entity_type.value)
        
        return db.query(Entity).filter(
            query_filter,
            Entity.is_active == True,
            Entity.verification_status == "verified"
        ).all()
    
    def generate_entity_id(self, entity_type: EntityType, **kwargs) -> str:
        """Generate a valid entity ID based on type."""
        if entity_type == EntityType.HUMAN:
            first_name = kwargs.get("first_name", "user").lower()
            last_name = kwargs.get("last_name", "unknown").lower()
            unique_id = str(uuid.uuid4().int)[:4]
            return f"hum.{first_name}.{last_name}.{unique_id}"
        
        elif entity_type == EntityType.BUSINESS:
            brand = kwargs.get("brand", "business").lower()
            service = kwargs.get("service", "general").lower()
            location = kwargs.get("location", "global").lower()
            return f"com.{brand}.{service}.{location}"
        
        elif entity_type == EntityType.AI_ASSISTANT:
            platform = kwargs.get("platform", "happidost").lower()
            assistant_name = kwargs.get("assistant_name", "assistant").lower()
            return f"ai.{platform}.{assistant_name}"
        
        else:
            raise ValueError(f"Unknown entity type: {entity_type}")


# Global entity registry instance
entity_registry = EntityRegistry()
