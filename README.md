# HappiDost AI Backend

🚀 **AI-powered backend for the HappiDost platform** - A revolutionary ecosystem that connects users with services through intelligent AI assistants.

## 🎯 Overview

HappiDost is building the future of service discovery and interaction through:

- **DOST OS**: Distributed Orchestration of Services & Transactions Operating System
- **AI Assistants**: Multi-modal agents that understand natural language and facilitate service interactions
- **Semantic Search**: Vector-based service discovery that understands intent, not just keywords
- **Trust Framework**: Government-verified identities and fraud prevention
- **Real-time Communication**: WebSocket-based chat with voice and text support

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    HappiDost Ecosystem                      │
├─────────────────────────────────────────────────────────────┤
│  AI Assistant Layer (User, Service, Orchestration)         │
├─────────────────────────────────────────────────────────────┤
│  DOST Event System (Standardized Communication)            │
├─────────────────────────────────────────────────────────────┤
│  Service Store (Semantic Search & Discovery)               │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (PostgreSQL + Vector Database)                 │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ Technology Stack

- **Backend**: FastAPI (Python 3.8+)
- **Database**: PostgreSQL + Qdrant (Vector DB)
- **AI/ML**: OpenAI GPT-4, An<PERSON><PERSON>, <PERSON><PERSON>ce Transformers
- **Real-time**: WebSockets
- **Authentication**: JWT with bcrypt
- **Deployment**: <PERSON><PERSON>, <PERSON><PERSON><PERSON>

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- PostgreSQL
- Qdrant (Vector Database)
- OpenAI API Key

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd happidost-platform

# Create virtual environment
python -m venv env

# Activate virtual environment
# Windows:
env\Scripts\activate
# Linux/Mac:
source env/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Required: OPENAI_API_KEY, DATABASE_URL, SECRET_KEY
```

### 3. Database Setup

```bash
# Start PostgreSQL and Qdrant
# PostgreSQL: Default port 5432
# Qdrant: Default port 6333

# Update DATABASE_URL in .env file
DATABASE_URL=postgresql://username:password@localhost:5432/happidost
```

### 4. Run Tests

```bash
# Test the setup
python test_setup.py
```

### 5. Start Server

```bash
# Start the development server
python start_server.py

# Or manually:
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 6. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **WebSocket Chat**: ws://localhost:8000/ws/chat/{user_id}

## 📡 API Endpoints

### Authentication
- `POST /api/v1/auth/register/human` - Register human entity
- `POST /api/v1/auth/register/business` - Register business entity
- `POST /api/v1/auth/login` - Login and get access token
- `GET /api/v1/auth/me` - Get current user info

### Chat & AI
- `WS /ws/chat/{user_id}` - WebSocket chat with AI assistant
- `POST /api/v1/chat/send-message` - Send message via REST
- `GET /api/v1/chat/history/{session_id}` - Get chat history

### Services
- `POST /api/v1/services/register` - Register a new service
- `GET /api/v1/services/search` - Search services semantically
- `GET /api/v1/services/my-services` - Get user's services
- `GET /api/v1/services/{service_id}` - Get service details

### Events (DOST)
- `POST /api/v1/events/send` - Send DOST event
- `GET /api/v1/events/session/{session_id}` - Get session events
- `POST /api/v1/events/session/create` - Create new session

## 🤖 AI Assistant Usage

### WebSocket Chat Example

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/chat/hum.john.doe.1234');

// Send text message
ws.send(JSON.stringify({
    type: "chat",
    content: "I need pizza delivery in Marathalli",
    include_audio: false
}));

// Send audio message
ws.send(JSON.stringify({
    type: "audio",
    audio_data: "base64_encoded_audio_data"
}));

// Handle responses
ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    console.log('AI Response:', response.content);
};
```

### Service Registration Example

```python
import requests

# Register a service
service_data = {
    "service_name": "Mario's Pizza Delivery",
    "description": "Fast pizza delivery in Marathalli area. We deliver hot, fresh pizzas within 30 minutes. Specializing in Italian cuisine with vegetarian and non-vegetarian options.",
    "pricing_model": "fixed",
    "pricing_info": {"base_price": 200, "currency": "INR"},
    "availability": {
        "monday": {"open": "10:00", "close": "23:00"},
        "tuesday": {"open": "10:00", "close": "23:00"}
    },
    "location_info": {"cities": ["bangalore"], "areas": ["marathalli", "whitefield"]}
}

response = requests.post(
    "http://localhost:8000/api/v1/services/register",
    json=service_data,
    headers={"Authorization": "Bearer YOUR_JWT_TOKEN"}
)
```

## 🔧 Development

### Project Structure

```
backend/
├── core/                   # Core configuration and database
├── dost/                   # DOST event system and entities
├── dss/                    # Service store and search
├── ai/                     # AI assistants and models
├── api/                    # REST API endpoints
├── security/               # Authentication and security
├── integrations/           # External service integrations
└── utils/                  # Utility functions
```

### Key Components

1. **DOST Event System**: Standardized JSON events for all communications
2. **AI Assistants**: Specialized agents for different roles (user, service, orchestration)
3. **Semantic Search**: Vector-based service discovery using embeddings
4. **Entity Registry**: rDNS-based identity system for users and services
5. **WebSocket Manager**: Real-time communication handling

### Adding New Features

1. **New AI Assistant**: Extend `BaseAssistant` class
2. **New API Endpoint**: Add to appropriate router in `api/v1/`
3. **New Event Type**: Add to `EventType` enum and create handler
4. **New Service Category**: Update service categorization in registration

## 🧪 Testing

```bash
# Run setup tests
python test_setup.py

# Run unit tests (when implemented)
pytest backend/tests/unit/

# Run integration tests (when implemented)
pytest backend/tests/integration/
```

## 🚀 Deployment

### Docker Deployment

```bash
# Build image
docker build -t happidost-backend .

# Run container
docker run -p 8000:8000 --env-file .env happidost-backend
```

### Production Considerations

- Set up proper PostgreSQL and Qdrant instances
- Configure environment variables for production
- Set up reverse proxy (nginx)
- Enable HTTPS
- Configure monitoring and logging
- Set up backup strategies

## 🔐 Security

- JWT-based authentication
- bcrypt password hashing
- Input validation with Pydantic
- Rate limiting middleware
- CORS configuration
- Environment-based secrets

## 📊 Monitoring

- Health check endpoint: `/health`
- Structured logging
- Metrics endpoint (when enabled): `:8001/metrics`
- Request/response logging middleware

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📝 License

This project is proprietary software developed for HappiDost.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Run `python test_setup.py` to diagnose issues
- Review logs for error details

---

**Built with ❤️ for the HappiDost ecosystem**
