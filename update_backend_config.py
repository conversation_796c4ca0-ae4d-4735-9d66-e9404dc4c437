#!/usr/bin/env python3
"""
Update backend configuration to use Supabase instead of SQLite
"""
import os
import sys

def update_database_config():
    """Update database configuration in backend/core/database.py"""
    print("🔧 Updating database configuration...")
    
    database_file = "backend/core/database.py"
    
    # Read current file
    with open(database_file, 'r') as f:
        content = f.read()
    
    # Replace SQLite configuration with Supabase
    old_config = '''# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./happidost.db")

# Create engine
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})'''
    
    new_config = '''# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./happidost.db")

# Create engine with appropriate configuration
if "postgresql" in DATABASE_URL:
    # PostgreSQL/Supabase configuration
    engine = create_engine(
        DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=300
    )
else:
    # SQLite configuration (fallback)
    engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})'''
    
    if old_config in content:
        content = content.replace(old_config, new_config)
        
        with open(database_file, 'w') as f:
            f.write(content)
        
        print("✅ Database configuration updated!")
        return True
    else:
        print("⚠️  Database configuration not found or already updated")
        return False

def update_service_registration():
    """Update service registration to use Supabase"""
    print("\n🔧 Updating service registration system...")
    
    service_file = "backend/services/service_registration.py"
    
    try:
        with open(service_file, 'r') as f:
            content = f.read()
        
        # Check if already using Supabase
        if "get_supabase_client" in content:
            print("✅ Service registration already configured for Supabase!")
            return True
        else:
            print("⚠️  Service registration needs manual update to use Supabase")
            return False
            
    except FileNotFoundError:
        print("❌ Service registration file not found")
        return False

def create_supabase_migration_script():
    """Create a script to migrate data from SQLite to Supabase"""
    print("\n📝 Creating data migration script...")
    
    migration_script = """#!/usr/bin/env python3
'''
Migrate data from SQLite to Supabase
'''
import sqlite3
import sys
sys.path.append('backend')

from supabase import create_client
import json
from datetime import datetime

# Configuration
SQLITE_DB = "./happidost.db"
SUPABASE_URL = "https://aerrspknmocqsohbjkze.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk"

def migrate_entities_to_profiles():
    '''Migrate entities from SQLite to Supabase profiles table'''
    try:
        # Connect to SQLite
        sqlite_conn = sqlite3.connect(SQLITE_DB)
        cursor = sqlite_conn.cursor()
        
        # Connect to Supabase
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Get entities from SQLite
        cursor.execute("SELECT * FROM entities")
        entities = cursor.fetchall()
        
        # Get column names
        cursor.execute("PRAGMA table_info(entities)")
        columns = [col[1] for col in cursor.fetchall()]
        
        print(f"Found {len(entities)} entities to migrate")
        
        for entity_row in entities:
            entity = dict(zip(columns, entity_row))
            
            # Parse JSON fields
            contact_info = json.loads(entity.get('contact_info', '{}')) if entity.get('contact_info') else {}
            additional_data = json.loads(entity.get('additional_data', '{}')) if entity.get('additional_data') else {}
            location = json.loads(entity.get('location', '{}')) if entity.get('location') else {}
            
            # Create profile record
            profile = {
                "user_id": entity['entity_id'],
                "full_name": entity['name'],
                "email": contact_info.get('email', ''),
                "phone": contact_info.get('phone', ''),
                "location": location,
                "preferences": additional_data.get('preferences', {}),
                "reputation_score": float(entity.get('reputation_score', 0.0)),
                "is_active": bool(entity.get('is_active', True))
            }
            
            # Insert into Supabase
            result = supabase.table("profiles").insert(profile).execute()
            print(f"Migrated entity: {entity['entity_id']}")
        
        sqlite_conn.close()
        print(f"✅ Successfully migrated {len(entities)} entities to profiles")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting data migration from SQLite to Supabase")
    migrate_entities_to_profiles()
"""
    
    with open("migrate_to_supabase.py", 'w') as f:
        f.write(migration_script)
    
    print("✅ Migration script created: migrate_to_supabase.py")
    return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n📋 Next Steps to Complete Supabase Integration:")
    print("=" * 60)
    print("1. 🗄️  Set up Supabase Database:")
    print("   - Go to your Supabase dashboard")
    print("   - Run the SQL schema from test_supabase.py in the SQL Editor")
    print("   - Get your database password from Settings > Database")
    print("   - Update DATABASE_URL in .env with your password")
    print()
    print("2. 🔧 Update Backend Dependencies:")
    print("   - Install required packages: pip install psycopg2-binary asyncpg")
    print("   - Restart the backend server")
    print()
    print("3. 📊 Migrate Existing Data (Optional):")
    print("   - Run: python migrate_to_supabase.py")
    print("   - This will copy your existing SQLite data to Supabase")
    print()
    print("4. 🔍 Set up Vespa Search:")
    print("   - Sign up at https://cloud.vespa.ai/")
    print("   - Create an application with the service schema")
    print("   - Update VESPA_ENDPOINT in .env with your app URL")
    print()
    print("5. 🧪 Test the Integration:")
    print("   - Register a new user")
    print("   - Try to register a service")
    print("   - Check that data appears in Supabase")
    print()
    print("6. 🚀 Deploy and Monitor:")
    print("   - Monitor logs for any database connection issues")
    print("   - Test all functionality end-to-end")

def main():
    """Main function"""
    print("🚀 Updating HappiDost Backend Configuration for Supabase\n")
    
    # Update configurations
    update_database_config()
    update_service_registration()
    create_supabase_migration_script()
    
    # Print next steps
    print_next_steps()
    
    print("\n🎉 Backend configuration update completed!")
    print("   Follow the next steps above to complete the Supabase integration.")

if __name__ == "__main__":
    main()
