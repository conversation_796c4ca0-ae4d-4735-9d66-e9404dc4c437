<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="HappiDost - AI-powered service discovery and registration platform. Find and offer services with intelligent matching."
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Meta tags for SEO -->
    <meta property="og:title" content="HappiDost - AI-Powered Service Platform" />
    <meta property="og:description" content="Discover and offer services with intelligent AI matching. Register your services in natural language." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://happidost.com" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="HappiDost - AI-Powered Service Platform" />
    <meta name="twitter:description" content="Discover and offer services with intelligent AI matching." />
    <meta name="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />
    
    <title>HappiDost - AI-Powered Service Discovery</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      #loading-screen.fade-out {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        color: white;
        font-family: 'Poppins', sans-serif;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 400;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-top: 20px;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Global styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f8fafc;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="50" cy="50" r="45" stroke="white" stroke-width="3" fill="none" opacity="0.3"/>
          <circle cx="50" cy="50" r="35" stroke="white" stroke-width="2" fill="none" opacity="0.6"/>
          <circle cx="50" cy="50" r="25" fill="white" opacity="0.9"/>
          <path d="M40 45 L48 53 L60 41" stroke="#667eea" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="loading-text">HappiDost</div>
      <div class="loading-subtitle">AI-Powered Service Platform</div>
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
