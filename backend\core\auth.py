"""
Authentication utilities and dependencies
"""
import logging
from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError, jwt

from .config import settings
from .database import get_db
from ..dost.entities.registry import entity_registry

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()


def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Verify JWT token and return entity_id."""
    try:
        payload = jwt.decode(credentials.credentials, settings.secret_key, algorithms=[settings.algorithm])
        entity_id: str = payload.get("sub")
        if entity_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return entity_id
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get current authenticated user information."""
    try:
        entity = await entity_registry.get_entity(entity_id, db)
        
        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        if not entity.is_active:
            raise HTTPException(status_code=403, detail="Account is inactive")
        
        # Extract user information
        user_info = {
            "id": entity.entity_id,
            "entity_id": entity.entity_id,
            "name": entity.name,
            "full_name": entity.name,
            "entity_type": entity.entity_type,
            "verification_status": entity.verification_status,
            "is_active": entity.is_active,
            "created_at": entity.created_at.isoformat() if entity.created_at else None,
            "updated_at": entity.updated_at.isoformat() if entity.updated_at else None
        }
        
        # Add contact info if available
        if entity.contact_info and isinstance(entity.contact_info, dict):
            user_info["email"] = entity.contact_info.get("email")
            user_info["phone"] = entity.contact_info.get("phone")
        
        # Add location info if available
        if entity.location and isinstance(entity.location, dict):
            user_info["location"] = entity.location

        # Extract role from additional_data if available, otherwise determine from entity type
        if entity.additional_data and isinstance(entity.additional_data, dict):
            user_info["role"] = entity.additional_data.get("role", "user")
        else:
            # Fallback to entity type-based role determination
            if entity.entity_type == "human":
                user_info["role"] = "user"
            elif entity.entity_type == "business":
                user_info["role"] = "service_provider"
            else:
                user_info["role"] = "user"
        
        return user_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get user information")
