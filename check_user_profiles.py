#!/usr/bin/env python3
"""
Check what's in the user_profiles table
"""
from supabase import create_client

SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def main():
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        print('🔍 Checking user_profiles table:')
        print('=' * 50)
        
        # Get all user_profiles
        result = supabase.table("user_profiles").select("*").execute()
        
        if result.data:
            print(f'✅ Found {len(result.data)} user profiles:')
            for profile in result.data:
                print(f'  - user_id: {profile.get("user_id")}')
                print(f'    entity_id: {profile.get("entity_id")}')
                print(f'    created_at: {profile.get("created_at")}')
                print()
        else:
            print('❌ No user profiles found')
            
        # Also check entities table
        print('🔍 Checking entities table:')
        print('=' * 30)
        
        entities_result = supabase.table("entities").select("entity_id, name, created_at").limit(5).execute()
        
        if entities_result.data:
            print(f'✅ Found {len(entities_result.data)} entities (showing first 5):')
            for entity in entities_result.data:
                print(f'  - entity_id: {entity.get("entity_id")}')
                print(f'    name: {entity.get("name")}')
                print(f'    created_at: {entity.get("created_at")}')
                print()
        else:
            print('❌ No entities found')
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    main()
