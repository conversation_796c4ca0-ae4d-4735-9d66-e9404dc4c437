"""
Services API Router
Handles service registration, discovery, and management
"""
import logging
import uuid
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...core.database import get_db, Service, Entity
from ...core.config import settings
from ...dss.search.semantic_search import semantic_search_engine
from ...dss.services.registration import service_registration_manager
from ...api.v1.auth import verify_token

logger = logging.getLogger(__name__)

# Create router
services_router = APIRouter()


@services_router.post("/register")
async def register_service(
    service_data: Dict[str, Any],
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Register a new service."""
    try:
        # Add entity_id to service data
        service_data["entity_id"] = entity_id
        
        # Process service registration
        result = await service_registration_manager.register_service(service_data, db)
        
        if result["success"]:
            return {
                "success": True,
                "message": "Service registered successfully",
                "service_id": result["service_id"],
                "approval_status": result.get("approval_status", "pending")
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.get("/search")
async def search_services(
    query: str = Query(..., description="Search query"),
    category: Optional[str] = Query(None, description="Service category filter"),
    location: Optional[str] = Query(None, description="Location filter"),
    limit: int = Query(10, ge=1, le=50, description="Maximum results"),
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Search for services using semantic search."""
    try:
        # Build search context
        context = {
            "user_info": {
                "entity_id": entity_id,
                "location": {"city": location} if location else {}
            }
        }
        
        # Build filters
        filters = {}
        if category:
            filters["category"] = category
        
        # Search services
        results = await semantic_search_engine.search_services(
            query=query,
            context=context,
            limit=limit,
            filters=filters
        )
        
        return {
            "success": True,
            "query": query,
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error searching services: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.get("/my-services")
async def get_my_services(
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get services owned by the authenticated entity."""
    try:
        services = db.query(Service).filter(
            Service.entity_id == entity_id,
            Service.is_active == True
        ).all()
        
        service_list = []
        for service in services:
            service_list.append({
                "service_id": str(service.service_id),
                "name": service.service_name,
                "description": service.description,
                "category": service.category,
                "approval_status": service.approval_status,
                "is_active": service.is_active,
                "created_at": service.created_at.isoformat()
            })
        
        return {
            "success": True,
            "services": service_list,
            "count": len(service_list)
        }
        
    except Exception as e:
        logger.error(f"Error getting user services: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.get("/{service_id}")
async def get_service_details(
    service_id: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific service."""
    try:
        service = db.query(Service).filter(
            Service.service_id == uuid.UUID(service_id),
            Service.is_active == True,
            Service.approval_status == "approved"
        ).first()
        
        if not service:
            raise HTTPException(status_code=404, detail="Service not found")
        
        # Get service provider entity
        provider = db.query(Entity).filter(
            Entity.entity_id == service.entity_id
        ).first()
        
        return {
            "success": True,
            "service": {
                "service_id": str(service.service_id),
                "name": service.service_name,
                "description": service.description,
                "category": service.category,
                "subcategory": service.subcategory,
                "capabilities": service.capabilities,
                "pricing_model": service.pricing_model,
                "pricing_info": service.pricing_info,
                "availability": service.availability,
                "location_info": service.location_info,
                "requirements": service.requirements,
                "tags": service.tags,
                "provider": {
                    "entity_id": provider.entity_id,
                    "name": provider.name,
                    "verification_status": provider.verification_status,
                    "reputation_score": provider.reputation_score
                } if provider else None,
                "created_at": service.created_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting service details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.put("/{service_id}")
async def update_service(
    service_id: str,
    update_data: Dict[str, Any],
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Update a service (only by owner)."""
    try:
        service = db.query(Service).filter(
            Service.service_id == uuid.UUID(service_id),
            Service.entity_id == entity_id  # Only owner can update
        ).first()
        
        if not service:
            raise HTTPException(status_code=404, detail="Service not found or not owned by you")
        
        # Update allowed fields
        allowed_fields = [
            "service_name", "description", "capabilities", "pricing_info",
            "availability", "location_info", "requirements", "tags"
        ]
        
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(service, field):
                setattr(service, field, value)
        
        service.updated_at = datetime.utcnow()
        db.commit()
        
        # Update vector store if description changed
        if "description" in update_data or "capabilities" in update_data:
            await service_registration_manager.update_service_embedding(service, db)
        
        return {
            "success": True,
            "message": "Service updated successfully",
            "service_id": service_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.delete("/{service_id}")
async def delete_service(
    service_id: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Delete a service (only by owner)."""
    try:
        service = db.query(Service).filter(
            Service.service_id == uuid.UUID(service_id),
            Service.entity_id == entity_id  # Only owner can delete
        ).first()
        
        if not service:
            raise HTTPException(status_code=404, detail="Service not found or not owned by you")
        
        # Soft delete
        service.is_active = False
        service.updated_at = datetime.utcnow()
        db.commit()
        
        # Remove from vector store
        from ...dss.search.vector_store import vector_store
        await vector_store.delete_service(service_id)
        
        return {
            "success": True,
            "message": "Service deleted successfully",
            "service_id": service_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.get("/categories/list")
async def get_service_categories(db: Session = Depends(get_db)):
    """Get list of available service categories."""
    try:
        # Get unique categories from database
        categories = db.query(Service.category).filter(
            Service.is_active == True,
            Service.approval_status == "approved"
        ).distinct().all()
        
        category_list = [cat[0] for cat in categories if cat[0]]
        
        return {
            "success": True,
            "categories": sorted(category_list),
            "count": len(category_list)
        }
        
    except Exception as e:
        logger.error(f"Error getting categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@services_router.get("/popular")
async def get_popular_services(
    limit: int = Query(10, ge=1, le=50),
    category: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get popular services based on usage and ratings."""
    try:
        query = db.query(Service).filter(
            Service.is_active == True,
            Service.approval_status == "approved"
        )
        
        if category:
            query = query.filter(Service.category == category)
        
        # Order by reputation score (placeholder for popularity)
        services = query.join(Entity).order_by(
            Entity.reputation_score.desc()
        ).limit(limit).all()
        
        service_list = []
        for service in services:
            service_list.append({
                "service_id": str(service.service_id),
                "name": service.service_name,
                "description": service.description[:200] + "..." if len(service.description) > 200 else service.description,
                "category": service.category,
                "provider_name": service.entity.name,
                "reputation_score": service.entity.reputation_score
            })
        
        return {
            "success": True,
            "services": service_list,
            "count": len(service_list)
        }
        
    except Exception as e:
        logger.error(f"Error getting popular services: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
