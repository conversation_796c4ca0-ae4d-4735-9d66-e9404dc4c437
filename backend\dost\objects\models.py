"""
DOST Object Models
Universal representation of any entity, service, product, or resource
"""
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class ObjectType(str, Enum):
    """Types of DOST Objects."""
    SERVICE = "service"
    PRODUCT = "product"
    EXPERIENCE = "experience"
    RESOURCE = "resource"


class ObjectStatus(str, Enum):
    """Status of DOST Objects."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"


class ActionMethod(str, Enum):
    """HTTP methods for actions."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


# Metadata Models
class DOSTObjectMetadata(BaseModel):
    """Metadata for DOST Object."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    version: str = Field(default="1.0.0")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    entity_id: str = Field(..., description="rDNS format entity ID")
    object_type: ObjectType
    status: ObjectStatus = ObjectStatus.ACTIVE


class DOSTObjectIdentity(BaseModel):
    """Identity information for DOST Object."""
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=10, max_length=2000)
    short_description: Optional[str] = Field(None, max_length=150)
    keywords: List[str] = Field(default=[])
    category: str = Field(..., min_length=1)
    subcategory: Optional[str] = None
    aliases: List[str] = Field(default=[])


# Multimodal Attribute Models
class MediaItem(BaseModel):
    """Base model for media items."""
    url: str
    type: str
    metadata: Dict[str, Any] = Field(default={})


class ImageItem(MediaItem):
    """Image media item."""
    alt_text: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    format: Optional[str] = None


class VideoItem(MediaItem):
    """Video media item."""
    duration: Optional[int] = None  # seconds
    thumbnail: Optional[str] = None
    resolution: Optional[str] = None
    format: Optional[str] = None


class AudioItem(MediaItem):
    """Audio media item."""
    duration: Optional[int] = None  # seconds
    transcript: Optional[str] = None
    format: Optional[str] = None
    quality: Optional[str] = None


class RatingDistribution(BaseModel):
    """Rating distribution model."""
    five: int = Field(default=0, alias="5")
    four: int = Field(default=0, alias="4")
    three: int = Field(default=0, alias="3")
    two: int = Field(default=0, alias="2")
    one: int = Field(default=0, alias="1")


class Ratings(BaseModel):
    """Ratings and reviews model."""
    overall: float = Field(default=0.0, ge=0.0, le=5.0)
    aspects: Dict[str, float] = Field(default={})
    distribution: RatingDistribution = Field(default=RatingDistribution())
    total_reviews: int = Field(default=0)


class TextualAttributes(BaseModel):
    """Textual attributes model."""
    specifications: Dict[str, Any] = Field(default={})
    reviews: Dict[str, Any] = Field(default={})


class VisualAttributes(BaseModel):
    """Visual attributes model."""
    images: List[ImageItem] = Field(default=[])
    videos: List[VideoItem] = Field(default=[])


class MultimodalAttributes(BaseModel):
    """Multimodal attributes container."""
    textual: TextualAttributes = Field(default=TextualAttributes())
    visual: VisualAttributes = Field(default=VisualAttributes())
    audio: List[AudioItem] = Field(default=[])
    ratings: Ratings = Field(default=Ratings())


# Availability Models
class DaySchedule(BaseModel):
    """Schedule for a single day."""
    open: Optional[str] = None  # "09:00"
    close: Optional[str] = None  # "18:00"
    closed: bool = False


class WeeklySchedule(BaseModel):
    """Weekly schedule model."""
    monday: DaySchedule = Field(default=DaySchedule())
    tuesday: DaySchedule = Field(default=DaySchedule())
    wednesday: DaySchedule = Field(default=DaySchedule())
    thursday: DaySchedule = Field(default=DaySchedule())
    friday: DaySchedule = Field(default=DaySchedule())
    saturday: DaySchedule = Field(default=DaySchedule())
    sunday: DaySchedule = Field(default=DaySchedule())


class ScheduleException(BaseModel):
    """Schedule exception model."""
    date: str  # "2024-12-25"
    status: str  # "closed", "modified"
    reason: Optional[str] = None
    modified_hours: Optional[DaySchedule] = None


class LeadTime(BaseModel):
    """Lead time information."""
    minimum: str = "0m"  # "30m", "2h", "1d"
    maximum: str = "30d"
    typical: str = "1h"


class TemporalAvailability(BaseModel):
    """Temporal availability model."""
    schedule: WeeklySchedule = Field(default=WeeklySchedule())
    timezone: str = "UTC"
    exceptions: List[ScheduleException] = Field(default=[])
    lead_time: LeadTime = Field(default=LeadTime())


class Coordinates(BaseModel):
    """Geographic coordinates."""
    lat: float = Field(..., ge=-90, le=90)
    lng: float = Field(..., ge=-180, le=180)


class ServiceArea(BaseModel):
    """Service area definition."""
    type: str  # "city", "region", "country", "global"
    name: str
    coordinates: Optional[Dict[str, Any]] = None
    specific_areas: List[str] = Field(default=[])


class DeliveryOptions(BaseModel):
    """Delivery options model."""
    pickup: bool = False
    delivery: bool = False
    virtual: bool = False
    onsite: bool = False


class GeographicalAvailability(BaseModel):
    """Geographical availability model."""
    service_areas: List[ServiceArea] = Field(default=[])
    delivery_options: DeliveryOptions = Field(default=DeliveryOptions())


class Capacity(BaseModel):
    """Capacity information."""
    current: int = 0
    maximum: int = 1
    unit: str = "slots"  # "concurrent_orders", "seats", "slots"


class Availability(BaseModel):
    """Complete availability model."""
    temporal: TemporalAvailability = Field(default=TemporalAvailability())
    geographical: GeographicalAvailability = Field(default=GeographicalAvailability())
    capacity: Capacity = Field(default=Capacity())


# Financial Models
class PurchaseOptions(BaseModel):
    """Purchase options model."""
    base_price: float = 0.0
    instant_pay: float = 0.0
    cash_on_delivery: Optional[float] = None
    installments: Dict[str, Any] = Field(default={})


class RentalOptions(BaseModel):
    """Rental options model."""
    hourly: Optional[float] = None
    daily: Optional[float] = None
    weekly: Optional[float] = None
    monthly: Optional[float] = None
    yearly: Optional[float] = None
    deposit: Optional[float] = None
    minimum_period: str = "1h"


class SubscriptionOptions(BaseModel):
    """Subscription options model."""
    daily: Optional[float] = None
    weekly: Optional[float] = None
    monthly: Optional[float] = None
    quarterly: Optional[float] = None
    yearly: Optional[float] = None
    free_trial: Dict[str, Any] = Field(default={})


class OwnershipOptions(BaseModel):
    """Ownership options container."""
    purchase: Optional[PurchaseOptions] = None
    rental: Optional[RentalOptions] = None
    subscription: Optional[SubscriptionOptions] = None


class Discount(BaseModel):
    """Discount model."""
    type: str  # "percentage", "fixed", "bogo"
    value: float
    condition: Optional[str] = None
    valid_until: Optional[datetime] = None
    minimum_order: Optional[float] = None


class Financials(BaseModel):
    """Financial information model."""
    currency: str = "INR"
    tax_inclusive: bool = True
    ownership: OwnershipOptions = Field(default=OwnershipOptions())
    additional_costs: Dict[str, float] = Field(default={})
    discounts: List[Discount] = Field(default=[])


# Action Models
class DOSTAction(BaseModel):
    """DOST Action model."""
    verb: str = Field(..., min_length=1)
    label: str = Field(..., min_length=1)
    description: Optional[str] = None
    endpoint: str = Field(..., min_length=1)
    method: ActionMethod = ActionMethod.POST
    authentication: str = "required"  # "required", "optional", "none"
    parameters: Dict[str, Any] = Field(default={})
    response: Optional[str] = None


# Relationship Models
class ProviderInfo(BaseModel):
    """Provider information model."""
    entity_id: str
    name: str
    type: str
    verification_status: str
    contact_info: Dict[str, str] = Field(default={})


class RelatedObject(BaseModel):
    """Related object reference."""
    object_id: str
    similarity: Optional[float] = None
    reason: Optional[str] = None
    discount: Optional[float] = None
    description: Optional[str] = None
    type: Optional[str] = None


class Relationships(BaseModel):
    """Object relationships model."""
    provider: Optional[ProviderInfo] = None
    alternatives: List[RelatedObject] = Field(default=[])
    bundles: List[RelatedObject] = Field(default=[])
    dependencies: List[RelatedObject] = Field(default=[])


# AI Metadata Models
class EmbeddingInfo(BaseModel):
    """Embedding information."""
    vector: List[float] = Field(default=[])
    model: str = "text-embedding-3-small"
    version: str = "1.0"


class AIMetadata(BaseModel):
    """AI-specific metadata."""
    embedding: EmbeddingInfo = Field(default=EmbeddingInfo())
    search_tags: List[str] = Field(default=[])
    intent_mapping: Dict[str, float] = Field(default={})
    last_indexed: Optional[datetime] = None


# Compliance Models
class DataPrivacy(BaseModel):
    """Data privacy compliance."""
    gdpr_compliant: bool = True
    data_retention: str = "2y"
    consent_required: List[str] = Field(default=[])


class Accessibility(BaseModel):
    """Accessibility compliance."""
    wcag_level: str = "AA"
    screen_reader_friendly: bool = True
    alternative_formats: List[str] = Field(default=[])


class Legal(BaseModel):
    """Legal compliance."""
    terms_of_service: Optional[str] = None
    privacy_policy: Optional[str] = None
    licenses: List[str] = Field(default=[])


class Compliance(BaseModel):
    """Compliance information."""
    data_privacy: DataPrivacy = Field(default=DataPrivacy())
    accessibility: Accessibility = Field(default=Accessibility())
    legal: Legal = Field(default=Legal())


# Main DOST Object Model
class DOSTObject(BaseModel):
    """Complete DOST Object model."""
    metadata: DOSTObjectMetadata
    identity: DOSTObjectIdentity
    multimodal_attributes: MultimodalAttributes = Field(default=MultimodalAttributes())
    availability: Availability = Field(default=Availability())
    financials: Financials = Field(default=Financials())
    actions: List[DOSTAction] = Field(default=[])
    relationships: Relationships = Field(default=Relationships())
    ai_metadata: AIMetadata = Field(default=AIMetadata())
    compliance: Compliance = Field(default=Compliance())

    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"

    @validator('metadata')
    def validate_metadata(cls, v):
        """Validate metadata."""
        if not v.entity_id:
            raise ValueError('entity_id is required')
        return v

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {"dostObject": self.dict(by_alias=True, exclude_none=True)}

    def get_primary_action(self) -> Optional[DOSTAction]:
        """Get the primary action for this object."""
        if not self.actions:
            return None
        
        # Priority order for primary actions
        primary_verbs = ["purchase", "book", "subscribe", "order", "buy"]
        
        for verb in primary_verbs:
            for action in self.actions:
                if action.verb == verb:
                    return action
        
        # Return first action if no primary found
        return self.actions[0]

    def is_available_now(self) -> bool:
        """Check if object is available right now."""
        if self.metadata.status != ObjectStatus.ACTIVE:
            return False
        
        # Check capacity
        if self.availability.capacity.current >= self.availability.capacity.maximum:
            return False
        
        # TODO: Add temporal availability check
        return True

    def get_base_price(self) -> Optional[float]:
        """Get the base price for this object."""
        if self.financials.ownership.purchase:
            return self.financials.ownership.purchase.base_price
        elif self.financials.ownership.rental:
            return (self.financials.ownership.rental.hourly or 
                   self.financials.ownership.rental.daily)
        elif self.financials.ownership.subscription:
            return (self.financials.ownership.subscription.monthly or
                   self.financials.ownership.subscription.daily)
        return None
