#!/usr/bin/env python3
"""
Test full integration: Supabase + Vespa + Backend
"""
import os
import sys
import asyncio
import json
import time
import requests
from datetime import datetime

# Add backend to path
sys.path.append('backend')

# Import after path setup
try:
    from supabase import create_client, Client
    from vespa.application import Vespa
except ImportError as e:
    print(f"❌ Missing dependencies: {e}")
    print("   Install with: pip install supabase pyvespa")
    sys.exit(1)

# Configuration
SUPABASE_URL = "https://aerrspknmocqsohbjkze.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk"
VESPA_URL = "http://localhost:8080"
BACKEND_URL = "http://localhost:8000"

def test_supabase_integration():
    """Test Supabase integration"""
    try:
        print("🗄️  Testing Supabase integration...")
        
        # Connect to Supabase
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Test connection
        supabase.auth.get_session()
        print("✅ Supabase connection successful!")
        
        # Test profiles table
        try:
            result = supabase.table("profiles").select("*").limit(1).execute()
            print(f"✅ Profiles table accessible! Found {len(result.data)} records")
        except Exception as e:
            print(f"⚠️  Profiles table not accessible: {str(e)}")
            print("   Make sure you've run the SQL schema in Supabase dashboard")
        
        # Test services table
        try:
            result = supabase.table("services").select("*").limit(1).execute()
            print(f"✅ Services table accessible! Found {len(result.data)} records")
        except Exception as e:
            print(f"⚠️  Services table not accessible: {str(e)}")
        
        return supabase
        
    except Exception as e:
        print(f"❌ Supabase integration failed: {str(e)}")
        return None

def test_vespa_integration():
    """Test Vespa integration"""
    try:
        print("\n🔍 Testing Vespa integration...")
        
        # Test connection
        response = requests.get(f"{VESPA_URL}/ApplicationStatus", timeout=5)
        if response.status_code != 200:
            print(f"❌ Vespa not accessible at {VESPA_URL}")
            print("   Run: python setup_vespa_local.py")
            return None
        
        print("✅ Vespa connection successful!")
        
        # Connect with pyvespa
        try:
            vespa_app = Vespa(url="http://localhost", port=8080)
            print("✅ pyvespa client connected!")
            return vespa_app
        except Exception as e:
            print(f"⚠️  pyvespa connection failed: {str(e)}")
            print("   Install with: pip install pyvespa")
            return None
        
    except Exception as e:
        print(f"❌ Vespa integration failed: {str(e)}")
        return None

def test_backend_integration():
    """Test backend integration"""
    try:
        print("\n🖥️  Testing backend integration...")
        
        # Test backend health
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running!")
        else:
            print(f"⚠️  Backend health check failed: {response.status_code}")
        
        # Test auth endpoints
        response = requests.get(f"{BACKEND_URL}/api/v1/auth/me", timeout=5)
        print(f"✅ Auth endpoint accessible (status: {response.status_code})")
        
        # Test service registration endpoints
        response = requests.get(f"{BACKEND_URL}/api/v1/service-registration/categories", timeout=5)
        if response.status_code == 200:
            print("✅ Service registration endpoints accessible!")
            return True
        else:
            print(f"⚠️  Service registration endpoints: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Backend integration failed: {str(e)}")
        return False

def test_end_to_end_flow(supabase, vespa_app):
    """Test end-to-end service registration flow"""
    try:
        print("\n🔄 Testing end-to-end flow...")
        
        if not supabase or not vespa_app:
            print("⚠️  Skipping end-to-end test (missing dependencies)")
            return False
        
        # Create test profile
        test_profile = {
            "user_id": "hum.test.integration.001",
            "full_name": "Integration Test User",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "location": {"city": "Test City", "country": "Test Country"},
            "preferences": {"language": "en", "currency": "USD"},
            "reputation_score": 4.5,
            "is_active": True
        }
        
        # Insert into Supabase
        try:
            result = supabase.table("profiles").upsert(test_profile).execute()
            print("✅ Test profile created in Supabase!")
        except Exception as e:
            print(f"⚠️  Profile creation failed: {str(e)}")
        
        # Create test service
        test_service = {
            "service_id": "srv.test.integration.001",
            "provider_id": "hum.test.integration.001",
            "title": "Integration Test Service",
            "description": "This is a test service for integration testing",
            "category": "Technology",
            "subcategory": "Testing",
            "keywords": ["test", "integration", "automation"],
            "price_min": 50.0,
            "price_max": 100.0,
            "currency": "USD",
            "location": {"city": "Test City", "country": "Test Country"},
            "status": "active",
            "average_rating": 4.8,
            "total_reviews": 5
        }
        
        # Insert into Supabase
        try:
            result = supabase.table("services").upsert(test_service).execute()
            print("✅ Test service created in Supabase!")
        except Exception as e:
            print(f"⚠️  Service creation failed: {str(e)}")
        
        # Index in Vespa
        vespa_doc = {
            "fields": {
                "service_id": test_service["service_id"],
                "title": test_service["title"],
                "description": test_service["description"],
                "category": test_service["category"],
                "subcategory": test_service["subcategory"],
                "provider_id": test_service["provider_id"],
                "provider_name": test_profile["full_name"],
                "location_city": test_service["location"]["city"],
                "location_country": test_service["location"]["country"],
                "price_min": test_service["price_min"],
                "price_max": test_service["price_max"],
                "currency": test_service["currency"],
                "rating": test_service["average_rating"],
                "total_reviews": test_service["total_reviews"],
                "keywords": test_service["keywords"],
                "status": test_service["status"],
                "embedding": [0.1] * 1536,  # Mock embedding
                "created_at": int(time.time())
            }
        }
        
        try:
            response = vespa_app.feed_data(
                schema="service",
                data_id="test-integration-001",
                fields=vespa_doc["fields"]
            )
            print("✅ Test service indexed in Vespa!")
        except Exception as e:
            print(f"⚠️  Vespa indexing failed: {str(e)}")
        
        # Test search
        try:
            time.sleep(2)  # Wait for indexing
            query = vespa_app.query(body={
                "yql": "select * from sources * where title contains 'Integration'",
                "hits": 5
            })
            
            results = query.json()
            hit_count = results.get("root", {}).get("fields", {}).get("totalCount", 0)
            print(f"✅ Search test successful! Found {hit_count} results")
            
            if hit_count > 0:
                print("   Found our test service in search results!")
            
        except Exception as e:
            print(f"⚠️  Search test failed: {str(e)}")
        
        print("✅ End-to-end flow completed!")
        return True
        
    except Exception as e:
        print(f"❌ End-to-end flow failed: {str(e)}")
        return False

def print_integration_status():
    """Print integration status and next steps"""
    print("\n📊 Integration Status Summary:")
    print("=" * 50)
    print("✅ = Working")
    print("⚠️  = Needs attention")
    print("❌ = Not working")
    print()
    print("Next Steps:")
    print("1. If Supabase tables are missing:")
    print("   - Run the SQL schema from test_supabase.py in Supabase dashboard")
    print()
    print("2. If Vespa is not running:")
    print("   - Run: python setup_vespa_local.py")
    print()
    print("3. If backend is not using Supabase:")
    print("   - Update DATABASE_URL in .env")
    print("   - Install: pip install psycopg2-binary")
    print("   - Restart backend server")
    print()
    print("4. Test service registration:")
    print("   - Register a new user")
    print("   - Register a service")
    print("   - Check data in both Supabase and Vespa")

def main():
    """Main test function"""
    print("🚀 Testing Full HappiDost Integration\n")
    print("Testing: Supabase + Vespa + Backend")
    print("=" * 50)
    
    # Test individual components
    supabase = test_supabase_integration()
    vespa_app = test_vespa_integration()
    backend_ok = test_backend_integration()
    
    # Test end-to-end flow
    if supabase and vespa_app:
        test_end_to_end_flow(supabase, vespa_app)
    
    # Print status
    print_integration_status()
    
    print("\n🎉 Integration test completed!")

if __name__ == "__main__":
    main()
