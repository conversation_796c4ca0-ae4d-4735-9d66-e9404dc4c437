"""
DOST Entity Models
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from enum import Enum


class EntityType(str, Enum):
    """Types of entities in DOST ecosystem."""
    HUMAN = "human"
    BUSINESS = "business"
    AI_ASSISTANT = "ai_assistant"


class VerificationStatus(str, Enum):
    """Entity verification status."""
    PENDING = "pending"
    VERIFIED = "verified"
    REJECTED = "rejected"
    SUSPENDED = "suspended"


class ContactInfo(BaseModel):
    """Contact information model."""
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    website: Optional[str] = None
    social_media: Optional[Dict[str, str]] = None


class LocationInfo(BaseModel):
    """Location information model."""
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    address: Optional[str] = None
    coordinates: Optional[Dict[str, float]] = None  # {"lat": 12.9716, "lng": 77.5946}
    timezone: Optional[str] = None


class EntityRegistration(BaseModel):
    """Entity registration request model."""
    entity_id: str = Field(..., description="rDNS format entity identifier")
    entity_type: EntityType = Field(..., description="Type of entity")
    name: str = Field(..., min_length=1, max_length=255, description="Entity name")
    description: Optional[str] = Field(None, max_length=1000, description="Entity description")
    contact_info: Optional[ContactInfo] = None
    location: Optional[LocationInfo] = None
    verification_documents: Optional[Dict[str, str]] = None  # Document URLs or base64
    additional_data: Optional[Dict[str, Any]] = None  # Type-specific data
    
    @validator('entity_id')
    def validate_entity_id_format(cls, v):
        """Basic validation of entity ID format."""
        if not v or len(v.split('.')) < 2:
            raise ValueError('Entity ID must follow rDNS format')
        return v.lower()


class HumanRegistration(EntityRegistration):
    """Human entity registration."""
    entity_type: EntityType = EntityType.HUMAN
    
    def __init__(self, **data):
        # Ensure additional_data contains human-specific fields
        if 'additional_data' not in data:
            data['additional_data'] = {}
        
        # Add human-specific defaults
        data['additional_data'].setdefault('preferences', {})
        data['additional_data'].setdefault('privacy_settings', {
            'share_location': False,
            'share_contact': False,
            'allow_marketing': False
        })
        data['additional_data'].setdefault('notification_settings', {
            'email_notifications': True,
            'push_notifications': True,
            'sms_notifications': False
        })
        
        super().__init__(**data)


class BusinessRegistration(EntityRegistration):
    """Business entity registration."""
    entity_type: EntityType = EntityType.BUSINESS
    
    def __init__(self, **data):
        # Ensure additional_data contains business-specific fields
        if 'additional_data' not in data:
            data['additional_data'] = {}
        
        # Add business-specific defaults
        data['additional_data'].setdefault('business_info', {})
        data['additional_data'].setdefault('service_categories', [])
        data['additional_data'].setdefault('operating_regions', [])
        data['additional_data'].setdefault('business_hours', {
            'monday': {'open': '09:00', 'close': '18:00'},
            'tuesday': {'open': '09:00', 'close': '18:00'},
            'wednesday': {'open': '09:00', 'close': '18:00'},
            'thursday': {'open': '09:00', 'close': '18:00'},
            'friday': {'open': '09:00', 'close': '18:00'},
            'saturday': {'open': '10:00', 'close': '16:00'},
            'sunday': {'closed': True}
        })
        data['additional_data'].setdefault('payment_methods', ['cash', 'digital'])
        
        super().__init__(**data)


class AIAssistantRegistration(EntityRegistration):
    """AI Assistant entity registration."""
    entity_type: EntityType = EntityType.AI_ASSISTANT
    
    def __init__(self, **data):
        # Ensure additional_data contains AI-specific fields
        if 'additional_data' not in data:
            data['additional_data'] = {}
        
        # Add AI-specific defaults
        data['additional_data'].setdefault('capabilities', [])
        data['additional_data'].setdefault('personality', {})
        data['additional_data'].setdefault('model_config', {})
        
        super().__init__(**data)


class EntityProfile(BaseModel):
    """Complete entity profile with all related data."""
    entity_id: str
    entity_type: EntityType
    name: str
    description: Optional[str]
    verification_status: VerificationStatus
    contact_info: Optional[ContactInfo]
    location: Optional[LocationInfo]
    reputation_score: float
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    # Type-specific data
    user_profile: Optional[Dict[str, Any]] = None
    service_provider_profile: Optional[Dict[str, Any]] = None
    ai_assistant_profile: Optional[Dict[str, Any]] = None


class EntitySearchRequest(BaseModel):
    """Entity search request model."""
    query: str = Field(..., min_length=1, description="Search query")
    entity_type: Optional[EntityType] = None
    location: Optional[LocationInfo] = None
    verification_status: Optional[VerificationStatus] = None
    limit: int = Field(default=10, ge=1, le=100)
    offset: int = Field(default=0, ge=0)


class EntitySearchResponse(BaseModel):
    """Entity search response model."""
    entities: List[EntityProfile]
    total_count: int
    has_more: bool
    query: str


class EntityUpdateRequest(BaseModel):
    """Entity update request model."""
    name: Optional[str] = None
    description: Optional[str] = None
    contact_info: Optional[ContactInfo] = None
    location: Optional[LocationInfo] = None
    additional_data: Optional[Dict[str, Any]] = None


class VerificationRequest(BaseModel):
    """Entity verification request model."""
    entity_id: str
    verification_type: str  # "identity", "business", "address"
    documents: Dict[str, str]  # Document type -> URL/base64
    additional_info: Optional[Dict[str, Any]] = None


class VerificationResponse(BaseModel):
    """Entity verification response model."""
    success: bool
    entity_id: str
    verification_status: VerificationStatus
    message: str
    verification_details: Optional[Dict[str, Any]] = None


# Utility functions for entity management
def parse_entity_id(entity_id: str) -> Dict[str, str]:
    """Parse entity ID to extract components."""
    parts = entity_id.split('.')
    
    if entity_id.startswith('hum.'):
        return {
            "type": "human",
            "first_name": parts[1] if len(parts) > 1 else "",
            "last_name": parts[2] if len(parts) > 2 else "",
            "unique_id": parts[3] if len(parts) > 3 else ""
        }
    elif entity_id.startswith('com.'):
        return {
            "type": "business",
            "brand": parts[1] if len(parts) > 1 else "",
            "service": parts[2] if len(parts) > 2 else "",
            "location": parts[3] if len(parts) > 3 else ""
        }
    elif entity_id.startswith('ai.'):
        return {
            "type": "ai_assistant",
            "platform": parts[1] if len(parts) > 1 else "",
            "assistant_name": parts[2] if len(parts) > 2 else ""
        }
    else:
        return {"type": "unknown"}


def is_human_entity(entity_id: str) -> bool:
    """Check if entity ID represents a human."""
    return entity_id.startswith('hum.')


def is_business_entity(entity_id: str) -> bool:
    """Check if entity ID represents a business."""
    return entity_id.startswith('com.')


def is_ai_entity(entity_id: str) -> bool:
    """Check if entity ID represents an AI assistant."""
    return entity_id.startswith('ai.')
