"""
Events API Router
Handles DOST event operations and management
"""
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...core.database import get_db, DOSTEvent as DOSTEventDB, DOSTSession
from ...dost.events.models import DOSTEvent, EventResponse
from ...dost.events.processor import event_processor
from ...api.v1.auth import verify_token

logger = logging.getLogger(__name__)

# Create router
events_router = APIRouter()


@events_router.post("/send")
async def send_event(
    event: DOSTEvent,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Send a DOST event."""
    try:
        # Verify sender is authenticated entity
        if event.source_entity_id != entity_id:
            raise HTTPException(status_code=403, detail="Cannot send events on behalf of other entities")
        
        # Process event
        result = await event_processor.process_event(event, db)
        
        return result.dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending event: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@events_router.get("/session/{session_id}")
async def get_session_events(
    session_id: str,
    limit: int = Query(50, ge=1, le=200),
    offset: int = Query(0, ge=0),
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get events for a specific session."""
    try:
        # Verify entity is participant in session
        session = db.query(DOSTSession).filter(
            DOSTSession.session_id == uuid.UUID(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if entity_id not in session.participants:
            raise HTTPException(status_code=403, detail="Not a participant in this session")
        
        # Get events
        events = db.query(DOSTEventDB).filter(
            DOSTEventDB.session_id == uuid.UUID(session_id)
        ).order_by(DOSTEventDB.timestamp.desc()).offset(offset).limit(limit).all()
        
        event_list = []
        for event in reversed(events):  # Chronological order
            event_list.append({
                "event_id": str(event.event_id),
                "timestamp": event.timestamp.isoformat(),
                "source_entity_id": event.source_entity_id,
                "target_entity_id": event.target_entity_id,
                "event_type": event.event_type,
                "is_ai_generated": event.is_ai_generated,
                "message_content": event.message_content,
                "tags": event.tags,
                "processing_status": event.processing_status
            })
        
        return {
            "success": True,
            "session_id": session_id,
            "events": event_list,
            "count": len(event_list),
            "has_more": len(events) == limit
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session events: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@events_router.get("/my-events")
async def get_my_events(
    limit: int = Query(50, ge=1, le=200),
    offset: int = Query(0, ge=0),
    event_type: Optional[str] = Query(None),
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get events for the authenticated entity."""
    try:
        query = db.query(DOSTEventDB).filter(
            (DOSTEventDB.source_entity_id == entity_id) | 
            (DOSTEventDB.target_entity_id == entity_id)
        )
        
        if event_type:
            query = query.filter(DOSTEventDB.event_type == event_type)
        
        events = query.order_by(DOSTEventDB.timestamp.desc()).offset(offset).limit(limit).all()
        
        event_list = []
        for event in events:
            event_list.append({
                "event_id": str(event.event_id),
                "session_id": str(event.session_id),
                "timestamp": event.timestamp.isoformat(),
                "source_entity_id": event.source_entity_id,
                "target_entity_id": event.target_entity_id,
                "event_type": event.event_type,
                "is_ai_generated": event.is_ai_generated,
                "message_content": event.message_content,
                "processing_status": event.processing_status,
                "direction": "sent" if event.source_entity_id == entity_id else "received"
            })
        
        return {
            "success": True,
            "events": event_list,
            "count": len(event_list),
            "has_more": len(events) == limit
        }
        
    except Exception as e:
        logger.error(f"Error getting entity events: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@events_router.post("/session/create")
async def create_session(
    participants: List[str],
    session_type: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Create a new DOST session."""
    try:
        # Verify requesting entity is in participants
        if entity_id not in participants:
            raise HTTPException(status_code=403, detail="Requesting entity must be a participant")
        
        # Create session
        from ...core.database import create_session
        session = create_session(participants, session_type, db)
        
        return {
            "success": True,
            "session_id": str(session.session_id),
            "participants": session.participants,
            "session_type": session.session_type,
            "expires_at": session.expires_at.isoformat() if session.expires_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@events_router.get("/session/{session_id}/info")
async def get_session_info(
    session_id: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get session information."""
    try:
        session = db.query(DOSTSession).filter(
            DOSTSession.session_id == uuid.UUID(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if entity_id not in session.participants:
            raise HTTPException(status_code=403, detail="Not a participant in this session")
        
        return {
            "success": True,
            "session_id": str(session.session_id),
            "participants": session.participants,
            "session_type": session.session_type,
            "status": session.status,
            "context": session.context,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "expires_at": session.expires_at.isoformat() if session.expires_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@events_router.post("/session/{session_id}/close")
async def close_session(
    session_id: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Close a DOST session."""
    try:
        session = db.query(DOSTSession).filter(
            DOSTSession.session_id == uuid.UUID(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if entity_id not in session.participants:
            raise HTTPException(status_code=403, detail="Not a participant in this session")
        
        # Close session
        session.status = "completed"
        session.last_activity = datetime.utcnow()
        db.commit()
        
        return {
            "success": True,
            "message": "Session closed successfully",
            "session_id": session_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error closing session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
