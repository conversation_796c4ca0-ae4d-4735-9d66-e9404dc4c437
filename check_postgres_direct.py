#!/usr/bin/env python3
"""
Check what's in the database using direct PostgreSQL connection
"""
import psycopg2
from psycopg2.extras import RealDictCursor

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

def main():
    try:
        # Connect to PostgreSQL directly
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        print('🔍 Checking user_profiles table (PostgreSQL):')
        print('=' * 50)
        
        # Get all user_profiles
        cursor.execute("SELECT * FROM user_profiles ORDER BY created_at DESC LIMIT 5")
        profiles = cursor.fetchall()
        
        if profiles:
            print(f'✅ Found {len(profiles)} user profiles:')
            for profile in profiles:
                print(f'  - user_id: {profile["user_id"]}')
                print(f'    entity_id: {profile["entity_id"]}')
                print(f'    created_at: {profile["created_at"]}')
                print()
        else:
            print('❌ No user profiles found')
            
        # Also check entities table
        print('🔍 Checking entities table (PostgreSQL):')
        print('=' * 40)
        
        cursor.execute("SELECT entity_id, name, created_at FROM entities WHERE entity_type = 'human' ORDER BY created_at DESC LIMIT 5")
        entities = cursor.fetchall()
        
        if entities:
            print(f'✅ Found {len(entities)} entities:')
            for entity in entities:
                print(f'  - entity_id: {entity["entity_id"]}')
                print(f'    name: {entity["name"]}')
                print(f'    created_at: {entity["created_at"]}')
                print()
        else:
            print('❌ No entities found')
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    main()
