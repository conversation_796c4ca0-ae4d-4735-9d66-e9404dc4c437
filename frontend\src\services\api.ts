import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  User, 
  LoginCredentials, 
  RegisterData, 
  AuthResponse 
} from '../types/auth';
import { 
  Service, 
  ServiceRegistrationData, 
  ServiceSearchParams, 
  ServiceSearchResponse,
  ServiceRegistrationResponse 
} from '../types/service';

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token and Vespa API key
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Add Vespa API key for search requests
      const vespaApiKey = process.env.REACT_APP_VESPA_API_KEY;
      if (vespaApiKey && config.url?.includes('/search')) {
        config.headers['X-Vespa-API-Key'] = vespaApiKey;
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor to handle errors
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

const api = createApiInstance();

// Auth API
export const authAPI = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const formData = new FormData();
    formData.append('username', credentials.email);
    formData.append('password', credentials.password);
    
    const response: AxiosResponse<AuthResponse> = await api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return response.data;
  },

  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response: AxiosResponse<AuthResponse> = await api.post('/auth/register', data);
    return response.data;
  },

  getCurrentUser: async (): Promise<User> => {
    const response: AxiosResponse<{ user: User }> = await api.get('/auth/me');
    return response.data.user;
  },

  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response: AxiosResponse<{ user: User }> = await api.put('/auth/profile', data);
    return response.data.user;
  },

  changePassword: async (data: { current_password: string; new_password: string }): Promise<void> => {
    await api.post('/auth/change-password', data);
  },

  requestPasswordReset: async (email: string): Promise<void> => {
    await api.post('/auth/forgot-password', { email });
  },

  resetPassword: async (data: { token: string; new_password: string }): Promise<void> => {
    await api.post('/auth/reset-password', data);
  },
};

// Service API
export const serviceAPI = {
  registerService: async (data: ServiceRegistrationData): Promise<ServiceRegistrationResponse> => {
    const response: AxiosResponse<ServiceRegistrationResponse> = await api.post(
      '/service-registration/register', 
      data
    );
    return response.data;
  },

  getMyServices: async (): Promise<{ services: Service[] }> => {
    const response: AxiosResponse<{ services: Service[] }> = await api.get(
      '/service-registration/my-services'
    );
    return response.data;
  },

  getService: async (id: string): Promise<{ service: Service }> => {
    const response: AxiosResponse<{ service: Service }> = await api.get(
      `/service-registration/service/${id}`
    );
    return response.data;
  },

  updateService: async (id: string, data: Partial<Service>): Promise<{ updated_service: Service }> => {
    const response: AxiosResponse<{ updated_service: Service }> = await api.put(
      `/service-registration/service/${id}`, 
      data
    );
    return response.data;
  },

  deleteService: async (id: string): Promise<void> => {
    await api.delete(`/service-registration/service/${id}`);
  },

  searchServices: async (params: ServiceSearchParams): Promise<ServiceSearchResponse> => {
    const response: AxiosResponse<ServiceSearchResponse> = await api.post(
      '/service-registration/search', 
      params
    );
    return response.data;
  },

  getCategories: async (): Promise<{ categories: string[] }> => {
    const response: AxiosResponse<{ categories: string[] }> = await api.get(
      '/service-registration/categories'
    );
    return response.data;
  },

  getFeaturedServices: async (limit: number = 10): Promise<{ services: Service[] }> => {
    const response: AxiosResponse<{ services: Service[] }> = await api.get(
      `/service-registration/featured?limit=${limit}`
    );
    return response.data;
  },
};

// Upload API
export const uploadAPI = {
  uploadFile: async (file: File, type: 'image' | 'document' | 'audio' | 'video'): Promise<{ url: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response: AxiosResponse<{ url: string }> = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  uploadMultipleFiles: async (files: File[], type: 'image' | 'document' | 'audio' | 'video'): Promise<{ urls: string[] }> => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files`, file);
    });
    formData.append('type', type);

    const response: AxiosResponse<{ urls: string[] }> = await api.post('/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// Analytics API
export const analyticsAPI = {
  getServiceAnalytics: async (serviceId: string): Promise<any> => {
    const response = await api.get(`/analytics/service/${serviceId}`);
    return response.data;
  },

  getDashboardStats: async (): Promise<any> => {
    const response = await api.get('/analytics/dashboard');
    return response.data;
  },
};

// Chat API
export const chatAPI = {
  getChatSessions: async (): Promise<any> => {
    const response = await api.get('/chat/sessions');
    return response.data;
  },

  getChatMessages: async (sessionId: string): Promise<any> => {
    const response = await api.get(`/chat/sessions/${sessionId}/messages`);
    return response.data;
  },

  sendMessage: async (sessionId: string, message: any): Promise<any> => {
    const response = await api.post(`/chat/sessions/${sessionId}/messages`, message);
    return response.data;
  },
};

// Health check
export const healthAPI = {
  check: async (): Promise<{ status: string; timestamp: string }> => {
    const response: AxiosResponse<{ status: string; timestamp: string }> = await api.get('/health');
    return response.data;
  },
};

export default api;
