import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Link,
  Alert,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate, Link as RouterLink, useSearchParams } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Helmet } from 'react-helmet-async';

import { useAuth } from '../../hooks/useAuth';
import { RegisterData } from '../../types/auth';
import LoadingSpinner from '../../components/common/LoadingSpinner';

// Form data type that includes confirm_password
type RegisterFormData = RegisterData & {
  confirm_password: string;
};

// Validation schema
const registerSchema = yup.object({
  full_name: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .required('Full name is required'),
  email: yup
    .string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    )
    .required('Password is required'),
  confirm_password: yup
    .string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
  role: yup
    .string()
    .oneOf(['user', 'service_provider', 'business'])
    .required('Please select your role'),
  phone_number: yup.string().optional(),
  terms_accepted: yup
    .boolean()
    .oneOf([true], 'You must accept the terms and conditions'),
  marketing_consent: yup.boolean(),
});

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { register, loading, error, clearError } = useAuth();
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [activeStep, setActiveStep] = useState(0);

  const defaultRole = searchParams.get('role') as 'user' | 'service_provider' | 'business' || 'user';

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<RegisterFormData>({
    resolver: yupResolver(registerSchema) as any,
    defaultValues: {
      full_name: '',
      email: '',
      password: '',
      confirm_password: '',
      role: defaultRole,
      phone_number: '',
      terms_accepted: false,
      marketing_consent: false,
    },
  });

  const watchedRole = watch('role');

  const onSubmit = async (data: RegisterFormData) => {
    try {
      clearError();
      const { confirm_password, ...registerData } = data;
      await register(registerData);
      navigate('/dashboard');
    } catch (error) {
      // Error is handled by the auth context
    }
  };

  const steps = ['Account Details', 'Role Selection', 'Verification'];

  const roleDescriptions = {
    user: 'I want to find and book services',
    service_provider: 'I want to offer my services',
    business: 'I represent a business or organization',
  };

  return (
    <>
      <Helmet>
        <title>Sign Up - HappiDost</title>
        <meta name="description" content="Create your HappiDost account to start finding services or offering your expertise." />
      </Helmet>

      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          py: 4,
        }}
      >
        <Container maxWidth="md">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Paper
              elevation={24}
              sx={{
                p: { xs: 3, sm: 4 },
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
              }}
            >
              {/* Header */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 'bold',
                    mb: 1,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  Join HappiDost
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Create your account and start your journey
                </Typography>
              </Box>

              {/* Progress Stepper */}
              <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Error Alert */}
              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {/* Registration Form */}
              <Box component="form" onSubmit={handleSubmit(onSubmit as any)}>
                {/* Step 1: Basic Information */}
                <Box sx={{ display: activeStep === 0 ? 'block' : 'none' }}>
                  <Controller
                    name="full_name"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Full Name"
                        error={!!errors.full_name}
                        helperText={errors.full_name?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                        sx={{ mb: 3 }}
                      />
                    )}
                  />

                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Email Address"
                        type="email"
                        error={!!errors.email}
                        helperText={errors.email?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <EmailIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                        sx={{ mb: 3 }}
                      />
                    )}
                  />

                  <Controller
                    name="phone_number"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Phone Number (Optional)"
                        type="tel"
                        error={!!errors.phone_number}
                        helperText={errors.phone_number?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PhoneIcon color="action" />
                            </InputAdornment>
                          ),
                        }}
                        sx={{ mb: 3 }}
                      />
                    )}
                  />

                  <Controller
                    name="password"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Password"
                        type={showPassword ? 'text' : 'password'}
                        error={!!errors.password}
                        helperText={errors.password?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon color="action" />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                        sx={{ mb: 3 }}
                      />
                    )}
                  />

                  <Controller
                    name="confirm_password"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        label="Confirm Password"
                        type={showConfirmPassword ? 'text' : 'password'}
                        error={!!errors.confirm_password}
                        helperText={errors.confirm_password?.message}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon color="action" />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                edge="end"
                              >
                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                        sx={{ mb: 3 }}
                      />
                    )}
                  />
                </Box>

                {/* Step 2: Role Selection */}
                <Box sx={{ display: activeStep === 1 ? 'block' : 'none' }}>
                  <Controller
                    name="role"
                    control={control}
                    render={({ field }) => (
                      <FormControl component="fieldset" sx={{ mb: 3 }}>
                        <FormLabel component="legend" sx={{ mb: 2, fontWeight: 600 }}>
                          How do you plan to use HappiDost?
                        </FormLabel>
                        <RadioGroup {...field}>
                          {Object.entries(roleDescriptions).map(([role, description]) => (
                            <FormControlLabel
                              key={role}
                              value={role}
                              control={<Radio />}
                              label={
                                <Box>
                                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                    {role === 'user' && '🔍 Customer'}
                                    {role === 'service_provider' && '🛠️ Service Provider'}
                                    {role === 'business' && '🏢 Business'}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    {description}
                                  </Typography>
                                </Box>
                              }
                              sx={{
                                mb: 2,
                                p: 2,
                                border: '1px solid',
                                borderColor: field.value === role ? 'primary.main' : 'divider',
                                borderRadius: 2,
                                backgroundColor: field.value === role ? 'rgba(102, 126, 234, 0.04)' : 'transparent',
                              }}
                            />
                          ))}
                        </RadioGroup>
                      </FormControl>
                    )}
                  />
                </Box>

                {/* Step 3: Terms and Conditions */}
                <Box sx={{ display: activeStep === 2 ? 'block' : 'none' }}>
                  <Controller
                    name="terms_accepted"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            color="primary"
                            sx={{ alignSelf: 'flex-start', mt: -1 }}
                          />
                        }
                        label={
                          <Typography variant="body2">
                            I agree to the{' '}
                            <Link href="/terms" target="_blank" color="primary.main">
                              Terms of Service
                            </Link>{' '}
                            and{' '}
                            <Link href="/privacy" target="_blank" color="primary.main">
                              Privacy Policy
                            </Link>
                          </Typography>
                        }
                        sx={{ mb: 2, alignItems: 'flex-start' }}
                      />
                    )}
                  />
                  {errors.terms_accepted && (
                    <Typography variant="body2" color="error" sx={{ mb: 2 }}>
                      {errors.terms_accepted.message}
                    </Typography>
                  )}

                  <Controller
                    name="marketing_consent"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Checkbox {...field} color="primary" />}
                        label={
                          <Typography variant="body2">
                            I would like to receive marketing communications and updates
                          </Typography>
                        }
                        sx={{ mb: 3 }}
                      />
                    )}
                  />
                </Box>

                {/* Navigation Buttons */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Button
                    onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
                    disabled={activeStep === 0}
                  >
                    Back
                  </Button>
                  
                  {activeStep < steps.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={() => setActiveStep(activeStep + 1)}
                      sx={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      }}
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={isSubmitting || loading}
                      sx={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        px: 4,
                      }}
                    >
                      {isSubmitting || loading ? (
                        <LoadingSpinner size={24} color="white" />
                      ) : (
                        'Create Account'
                      )}
                    </Button>
                  )}
                </Box>

                {/* Sign In Link */}
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Already have an account?{' '}
                    <Link
                      component={RouterLink}
                      to="/login"
                      sx={{
                        textDecoration: 'none',
                        color: 'primary.main',
                        fontWeight: 600,
                      }}
                    >
                      Sign in here
                    </Link>
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </motion.div>
        </Container>
      </Box>
    </>
  );
};

export default RegisterPage;
