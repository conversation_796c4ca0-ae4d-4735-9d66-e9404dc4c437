#!/usr/bin/env python3
"""
Set up Vespa locally with <PERSON><PERSON> and test the integration
"""
import os
import sys
import subprocess
import time
import json
import requests
from pathlib import Path

def check_docker():
    """Check if Docker is installed and running"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Docker not found")
            return False
    except FileNotFoundError:
        print("❌ Docker not installed")
        return False

def start_vespa_container():
    """Start Vespa container"""
    try:
        print("🚀 Starting Vespa container...")
        
        # Check if container already exists
        result = subprocess.run(['docker', 'ps', '-a', '--filter', 'name=vespa'], 
                              capture_output=True, text=True)
        
        if 'vespa' in result.stdout:
            print("📦 Vespa container already exists, removing old one...")
            subprocess.run(['docker', 'rm', '-f', 'vespa'], capture_output=True)
        
        # Start new container
        cmd = [
            'docker', 'run', '--detach', '--name', 'vespa', 
            '--hostname', 'vespa-container',
            '--publish', '8080:8080', '--publish', '19071:19071',
            'vespaengine/vespa'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Vespa container started successfully!")
            print("   Waiting for Vespa to initialize...")
            time.sleep(30)  # Wait for Vespa to start
            return True
        else:
            print(f"❌ Failed to start Vespa container: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Vespa: {str(e)}")
        return False

def create_vespa_application():
    """Create Vespa application package"""
    try:
        print("📝 Creating Vespa application package...")
        
        # Create application directory structure
        app_dir = Path("vespa_app")
        app_dir.mkdir(exist_ok=True)
        
        schemas_dir = app_dir / "schemas"
        schemas_dir.mkdir(exist_ok=True)
        
        # Create service schema
        service_schema = """
schema service {
    document service {
        field service_id type string {
            indexing: attribute | summary
        }
        field title type string {
            indexing: attribute | summary | index
        }
        field description type string {
            indexing: index | summary
        }
        field category type string {
            indexing: attribute | summary | index
        }
        field subcategory type string {
            indexing: attribute | summary
        }
        field provider_id type string {
            indexing: attribute | summary
        }
        field provider_name type string {
            indexing: attribute | summary | index
        }
        field location_city type string {
            indexing: attribute | summary | index
        }
        field location_country type string {
            indexing: attribute | summary
        }
        field price_min type double {
            indexing: attribute | summary
        }
        field price_max type double {
            indexing: attribute | summary
        }
        field currency type string {
            indexing: attribute | summary
        }
        field rating type double {
            indexing: attribute | summary
        }
        field total_reviews type int {
            indexing: attribute | summary
        }
        field keywords type array<string> {
            indexing: attribute | summary | index
        }
        field availability type string {
            indexing: attribute | summary
        }
        field status type string {
            indexing: attribute | summary
        }
        field embedding type tensor<float>(x[1536]) {
            indexing: attribute | index
            attribute {
                distance-metric: cosine
            }
        }
        field created_at type long {
            indexing: attribute | summary
        }
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, description) + attribute(rating) * 0.1
        }
    }
    
    rank-profile semantic inherits default {
        first-phase {
            expression: closeness(field, embedding)
        }
    }
    
    rank-profile hybrid inherits default {
        first-phase {
            expression: nativeRank(title, description) + closeness(field, embedding) * 0.5 + attribute(rating) * 0.1
        }
    }
}
"""
        
        with open(schemas_dir / "service.sd", 'w') as f:
            f.write(service_schema)
        
        # Create services.xml
        services_xml = """
<services version="1.0">
    <container id="default" version="1.0">
        <search />
        <document-api />
        <http>
            <server id="default" port="8080" />
        </http>
    </container>
    
    <content id="content" version="1.0">
        <documents>
            <document type="service" mode="index" />
        </documents>
        <nodes>
            <node hostalias="node1" />
        </nodes>
    </content>
</services>
"""
        
        with open(app_dir / "services.xml", 'w') as f:
            f.write(services_xml)
        
        # Create hosts.xml
        hosts_xml = """
<hosts>
    <host name="localhost">
        <alias>node1</alias>
    </host>
</hosts>
"""
        
        with open(app_dir / "hosts.xml", 'w') as f:
            f.write(hosts_xml)
        
        print("✅ Vespa application package created!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating Vespa application: {str(e)}")
        return False

def deploy_vespa_application():
    """Deploy application to Vespa"""
    try:
        print("🚀 Deploying Vespa application...")
        
        # Copy application to container
        result = subprocess.run([
            'docker', 'cp', 'vespa_app', 'vespa:/opt/vespa/var/vespa/application'
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Failed to copy application: {result.stderr}")
            return False
        
        # Deploy application
        result = subprocess.run([
            'docker', 'exec', 'vespa', 'bash', '-c',
            'vespa-deploy prepare /opt/vespa/var/vespa/application && vespa-deploy activate'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Vespa application deployed successfully!")
            time.sleep(10)  # Wait for deployment to complete
            return True
        else:
            print(f"❌ Failed to deploy application: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error deploying Vespa application: {str(e)}")
        return False

def test_vespa_connection():
    """Test Vespa connection"""
    try:
        print("🔗 Testing Vespa connection...")
        
        # Test status endpoint
        response = requests.get("http://localhost:8080/ApplicationStatus", timeout=10)
        
        if response.status_code == 200:
            print("✅ Vespa is running and accessible!")
            return True
        else:
            print(f"❌ Vespa connection failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Vespa connection: {str(e)}")
        return False

def insert_test_document():
    """Insert a test document"""
    try:
        print("📄 Inserting test document...")
        
        test_doc = {
            "fields": {
                "service_id": "srv.test.service.001",
                "title": "Professional Web Development Services",
                "description": "Expert web development using React, Node.js, and modern technologies. Full-stack development with responsive design.",
                "category": "Technology",
                "subcategory": "Web Development",
                "provider_id": "hum.john.doe.1234",
                "provider_name": "John Doe",
                "location_city": "San Francisco",
                "location_country": "USA",
                "price_min": 50.0,
                "price_max": 150.0,
                "currency": "USD",
                "rating": 4.8,
                "total_reviews": 25,
                "keywords": ["web", "development", "react", "nodejs", "fullstack"],
                "availability": "Mon-Fri 9AM-6PM",
                "status": "active",
                "embedding": [0.1] * 1536,  # Mock embedding
                "created_at": int(time.time())
            }
        }
        
        response = requests.post(
            "http://localhost:8080/document/v1/content/service/docid/test-service-001",
            json=test_doc,
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            print("✅ Test document inserted successfully!")
            return True
        else:
            print(f"❌ Failed to insert document: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error inserting test document: {str(e)}")
        return False

def test_search():
    """Test search functionality"""
    try:
        print("🔍 Testing search functionality...")
        
        # Test keyword search
        search_query = {
            "yql": "select * from sources * where title contains 'web'",
            "hits": 10
        }
        
        response = requests.post(
            "http://localhost:8080/search/",
            json=search_query,
            timeout=10
        )
        
        if response.status_code == 200:
            results = response.json()
            hit_count = results.get("root", {}).get("fields", {}).get("totalCount", 0)
            print(f"✅ Search successful! Found {hit_count} results")
            
            if hit_count > 0:
                print("   Sample result:")
                hits = results.get("root", {}).get("children", [])
                if hits:
                    first_hit = hits[0]
                    fields = first_hit.get("fields", {})
                    print(f"   - Title: {fields.get('title', 'N/A')}")
                    print(f"   - Category: {fields.get('category', 'N/A')}")
            
            return True
        else:
            print(f"❌ Search failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing search: {str(e)}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Vespa locally for HappiDost\n")
    
    # Check prerequisites
    if not check_docker():
        print("\n❌ Docker is required. Please install Docker Desktop and try again.")
        return False
    
    # Setup steps
    steps = [
        ("Starting Vespa container", start_vespa_container),
        ("Creating Vespa application", create_vespa_application),
        ("Deploying application", deploy_vespa_application),
        ("Testing connection", test_vespa_connection),
        ("Inserting test document", insert_test_document),
        ("Testing search", test_search)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            return False
    
    print("\n🎉 Vespa setup completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Update your .env file:")
    print("   VESPA_ENDPOINT=http://localhost:8080")
    print("2. Install Python client: pip install pyvespa")
    print("3. Test the integration with your backend")
    print("4. Vespa is now running on:")
    print("   - Search API: http://localhost:8080")
    print("   - Admin API: http://localhost:19071")
    
    return True

if __name__ == "__main__":
    main()
