import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Mic as MicIcon,
  Stop as StopIcon,
  Psychology as AIIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  LocationOn as LocationIcon,
  AttachMoney as PriceIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Helmet } from 'react-helmet-async';
// Speech functionality will be implemented with Web Speech API

import { useService } from '../../contexts/ServiceContext';
import { ServiceRegistrationData } from '../../types/service';
import LoadingSpinner from '../../components/common/LoadingSpinner';

// Validation schema
const serviceRegistrationSchema = yup.object({
  natural_language_description: yup
    .string()
    .min(100, 'Please provide at least 100 characters describing your service')
    .max(5000, 'Description is too long (maximum 5000 characters)')
    .required('Service description is required'),
  provider_location: yup.string().optional(),
});

const ServiceRegistrationPage: React.FC = () => {
  const navigate = useNavigate();
  const { registerService, loading, error, clearError } = useService();
  
  const [isRecording, setIsRecording] = useState(false);
  const [processingStage, setProcessingStage] = useState<string | null>(null);
  const [extractedData, setExtractedData] = useState<any>(null);

  // Web Speech API integration
  const [speaking, setSpeaking] = useState(false);
  const [listening, setListening] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors, isSubmitting },
  } = useForm<ServiceRegistrationData>({
    resolver: yupResolver(serviceRegistrationSchema),
    defaultValues: {
      natural_language_description: '',
      provider_location: '',
      additional_context: {},
    },
  });

  const description = watch('natural_language_description');

  const onSubmit = async (data: ServiceRegistrationData) => {
    try {
      clearError();
      setProcessingStage('Analyzing your description with AI...');
      
      const result = await registerService(data);
      setExtractedData(result);
      setProcessingStage(null);
      
      // Navigate to the new service page after a short delay
      setTimeout(() => {
        navigate(`/service/${result.service_id}`);
      }, 3000);
    } catch (error) {
      setProcessingStage(null);
    }
  };

  const handleVoiceInput = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      if (listening) {
        recognition.stop();
        setListening(false);
        setIsRecording(false);
      } else {
        recognition.start();
        setListening(true);
        setIsRecording(true);

        recognition.onresult = (event: any) => {
          let transcript = '';
          for (let i = event.resultIndex; i < event.results.length; i++) {
            transcript += event.results[i][0].transcript;
          }
          setValue('natural_language_description', getValues('natural_language_description') + ' ' + transcript);
        };

        recognition.onerror = () => {
          setListening(false);
          setIsRecording(false);
        };

        recognition.onend = () => {
          setListening(false);
          setIsRecording(false);
        };
      }
    } else {
      alert('Speech recognition is not supported in your browser');
    }
  };

  const handleTextToSpeech = () => {
    if ('speechSynthesis' in window) {
      if (speaking) {
        window.speechSynthesis.cancel();
        setSpeaking(false);
      } else {
        const utterance = new SpeechSynthesisUtterance(description);
        utterance.onstart = () => setSpeaking(true);
        utterance.onend = () => setSpeaking(false);
        utterance.onerror = () => setSpeaking(false);
        window.speechSynthesis.speak(utterance);
      }
    } else {
      alert('Text-to-speech is not supported in your browser');
    }
  };

  const exampleDescriptions = [
    {
      title: "Home Cleaning Service",
      description: "I provide professional home cleaning services in Bangalore. I have 3 years of experience and use eco-friendly products. Available Monday to Saturday, 9 AM to 6 PM. Rates start from ₹15 per square foot for regular cleaning and ₹25 for deep cleaning. I serve areas within 10 km of Koramangala.",
    },
    {
      title: "Math Tutoring",
      description: "I'm a qualified math teacher with 5 years of experience teaching high school and college students. I specialize in algebra, calculus, and statistics. Available for both online and in-person sessions in Marathahalli area. My rate is ₹800 per hour, flexible for regular students. Available weekdays 4 PM to 8 PM.",
    },
    {
      title: "Yoga Instruction",
      description: "Certified yoga instructor offering personalized yoga sessions. I teach Hatha, Vinyasa, and meditation. 7 years of experience with all age groups. Available for home visits in Whitefield area or online sessions. ₹1200 per session, package deals available. Morning and evening slots available.",
    },
  ];

  const processingStages = [
    "Analyzing your description with AI...",
    "Extracting service details...",
    "Determining pricing and availability...",
    "Geocoding location information...",
    "Generating keywords for search...",
    "Creating service profile...",
    "Indexing in search database...",
    "Finalizing registration...",
  ];

  return (
    <>
      <Helmet>
        <title>Register Your Service - HappiDost</title>
        <meta name="description" content="Register your service on HappiDost using natural language. Our AI will extract all the details automatically." />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Register Your Service
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
              Describe your service in natural language and let our AI handle the rest
            </Typography>
          </Box>

          {/* Processing Stage */}
          {processingStage && (
            <Card sx={{ mb: 4, border: '2px solid', borderColor: 'primary.main' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AIIcon sx={{ mr: 2, color: 'primary.main' }} />
                  <Typography variant="h6">AI Processing</Typography>
                </Box>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {processingStage}
                </Typography>
                <LinearProgress />
              </CardContent>
            </Card>
          )}

          {/* Success Message */}
          {extractedData && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Alert severity="success" sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  🎉 Service Registered Successfully!
                </Typography>
                <Typography variant="body2">
                  Your service has been processed and is now live on HappiDost. 
                  Redirecting to your service page...
                </Typography>
              </Alert>
            </motion.div>
          )}

          {/* Error Alert */}
          {error && (
            <Alert severity="error" sx={{ mb: 4 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>
            {/* Main Form */}
            <Paper sx={{ p: 4 }}>
              <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
                  Tell us about your service
                </Typography>

                <Controller
                  name="natural_language_description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={12}
                      label="Describe your service in detail"
                      placeholder="Example: I am a professional home cleaning service provider in Bangalore. I have been providing cleaning services for the past 3 years with a team of trained staff. We offer deep cleaning, regular maintenance, and move-in/move-out cleaning services. Our rates start from ₹15 per square foot for regular cleaning and ₹25 per square foot for deep cleaning. We are available Monday to Saturday from 8 AM to 6 PM. We serve all areas within 15 km of Koramangala..."
                      error={!!errors.natural_language_description}
                      helperText={
                        errors.natural_language_description?.message || 
                        `${description.length}/5000 characters`
                      }
                      sx={{ mb: 3 }}
                    />
                  )}
                />

                {/* Voice Input Button */}
                <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                  <Button
                    variant="outlined"
                    startIcon={isRecording ? <StopIcon /> : <MicIcon />}
                    onClick={handleVoiceInput}
                    color={isRecording ? "error" : "primary"}
                    disabled={speaking}
                  >
                    {isRecording ? 'Stop Recording' : 'Voice Input'}
                  </Button>
                  
                  {description && (
                    <Button
                      variant="outlined"
                      onClick={handleTextToSpeech}
                      disabled={speaking || listening}
                    >
                      {speaking ? 'Speaking...' : 'Read Aloud'}
                    </Button>
                  )}
                </Box>

                <Controller
                  name="provider_location"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Your Location (Optional)"
                      placeholder="e.g., Koramangala, Bangalore"
                      helperText="This helps us provide more accurate location-based matching"
                      sx={{ mb: 4 }}
                    />
                  )}
                />

                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  fullWidth
                  disabled={isSubmitting || loading || !description.trim()}
                  sx={{
                    py: 2,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    },
                  }}
                >
                  {isSubmitting || loading ? (
                    <LoadingSpinner size={24} color="white" />
                  ) : (
                    'Register Service with AI'
                  )}
                </Button>
              </Box>
            </Paper>

            {/* Sidebar */}
            <Box>
              {/* AI Features */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                    <AIIcon sx={{ mr: 1, color: 'primary.main' }} />
                    AI-Powered Registration
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon>
                        <CheckIcon color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Automatic categorization"
                        secondary="AI determines the best category for your service"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <PriceIcon color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Pricing extraction"
                        secondary="Automatically detects pricing models and rates"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <LocationIcon color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Location intelligence"
                        secondary="Geocodes addresses and service areas"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <ScheduleIcon color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Schedule parsing"
                        secondary="Understands availability and time slots"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <StarIcon color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="SEO optimization"
                        secondary="Generates keywords for better discovery"
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>

              {/* Tips */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                    <InfoIcon sx={{ mr: 1, color: 'info.main' }} />
                    Tips for Better Results
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="• Include your experience and qualifications"
                        secondary="Helps build trust with customers"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="• Mention your pricing clearly"
                        secondary="Include rates, packages, or pricing models"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="• Specify your availability"
                        secondary="Days, times, and scheduling preferences"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="• Define your service area"
                        secondary="Locations you serve or travel radius"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="• Highlight unique features"
                        secondary="What makes your service special"
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>

              {/* Examples */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6">Example Descriptions</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {exampleDescriptions.map((example, index) => (
                    <Card key={index} sx={{ mb: 2, cursor: 'pointer' }} 
                          onClick={() => setValue('natural_language_description', example.description)}>
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                          {example.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {example.description.substring(0, 150)}...
                        </Typography>
                        <Chip label="Click to use" size="small" sx={{ mt: 1 }} />
                      </CardContent>
                    </Card>
                  ))}
                </AccordionDetails>
              </Accordion>
            </Box>
          </Box>
        </motion.div>
      </Container>
    </>
  );
};

export default ServiceRegistrationPage;
