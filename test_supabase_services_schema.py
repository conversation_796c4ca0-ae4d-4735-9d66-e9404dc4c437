#!/usr/bin/env python3
"""
Test what fields the Supabase services table actually accepts
"""
from supabase import create_client
import uuid

# Configuration
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def test_minimal_service_insert():
    """Test minimal service insert to see what fields are required/accepted"""
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        print("🔍 Testing minimal service insert...")
        
        # Get a provider_id from profiles table
        profiles = supabase.table('profiles').select('id').limit(1).execute()
        if not profiles.data:
            print("❌ No profiles found. Need to create a profile first.")
            return
        
        provider_id = profiles.data[0]['id']
        print(f"✅ Using provider_id: {provider_id}")
        
        # Test 1: Minimal required fields only
        minimal_data = {
            'service_id': f'test_{uuid.uuid4().hex[:8]}',
            'provider_id': provider_id,
            'title': 'Test Service',
            'description': 'Test service description',
            'category': 'Test'
        }
        
        print("\n📝 Testing minimal insert...")
        try:
            result = supabase.table('services').insert(minimal_data).execute()
            if result.data:
                print("✅ Minimal insert successful!")
                service_id = result.data[0]['id']
                
                # Clean up
                supabase.table('services').delete().eq('id', service_id).execute()
                print("🧹 Cleaned up test record")
                
                # Now test with additional fields one by one
                test_additional_fields(supabase, provider_id)
            else:
                print(f"❌ Minimal insert failed: {result}")
        except Exception as e:
            print(f"❌ Minimal insert error: {e}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")

def test_additional_fields(supabase, provider_id):
    """Test additional fields to see which ones are accepted"""
    print("\n🔬 Testing additional fields...")
    
    # Fields to test
    test_fields = {
        'embedding_dims': 1536,
        'embedding_model': 'text-embedding-3-small',
        'search_tags': ['test', 'service'],
        'vespa_document_id': f'test_vespa_{uuid.uuid4().hex[:8]}',
        'subcategory': 'Test Subcategory',
        'keywords': ['test', 'keywords'],
        'price_min': 100.0,
        'price_max': 500.0,
        'currency': 'INR',
        'pricing_model': 'hourly',
        'is_negotiable': True,
        'service_type': 'on_location',
        'service_radius_km': 10,
        'availability': [{'day': 'mon', 'from_time': '09:00', 'to_time': '17:00'}],
        'timezone': 'Asia/Kolkata',
        'verification_status': 'pending',
        'quality_score': 0.0,
        'total_bookings': 0,
        'total_reviews': 0,
        'average_rating': 0.0,
        'status': 'active',
        'featured': False,
        'metadata': {'test': 'data'}
    }
    
    base_data = {
        'service_id': f'test_{uuid.uuid4().hex[:8]}',
        'provider_id': provider_id,
        'title': 'Test Service with Fields',
        'description': 'Test service description with additional fields',
        'category': 'Test'
    }
    
    # Test all fields at once
    full_data = {**base_data, **test_fields}
    
    try:
        result = supabase.table('services').insert(full_data).execute()
        if result.data:
            print("✅ Full insert successful! All fields accepted.")
            service_id = result.data[0]['id']
            
            # Show what was actually stored
            stored = supabase.table('services').select('*').eq('id', service_id).execute()
            if stored.data:
                print("\n📊 Stored fields:")
                for key, value in stored.data[0].items():
                    if key not in ['id', 'created_at', 'updated_at']:
                        print(f"   {key}: {type(value).__name__} = {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
            
            # Clean up
            supabase.table('services').delete().eq('id', service_id).execute()
            print("\n🧹 Cleaned up test record")
        else:
            print(f"❌ Full insert failed: {result}")
    except Exception as e:
        print(f"❌ Full insert error: {e}")
        
        # If full insert fails, test fields individually
        print("\n🔍 Testing fields individually...")
        for field_name, field_value in test_fields.items():
            test_data = {**base_data, field_name: field_value}
            test_data['service_id'] = f'test_{field_name}_{uuid.uuid4().hex[:6]}'
            
            try:
                result = supabase.table('services').insert(test_data).execute()
                if result.data:
                    print(f"   ✅ {field_name}: Accepted")
                    # Clean up
                    service_id = result.data[0]['id']
                    supabase.table('services').delete().eq('id', service_id).execute()
                else:
                    print(f"   ❌ {field_name}: Rejected - {result}")
            except Exception as field_error:
                print(f"   ❌ {field_name}: Error - {field_error}")

if __name__ == "__main__":
    test_minimal_service_insert()
