"""
DOST Object Manager
Handles CRUD operations and lifecycle management for DOST Objects
"""
import logging
import json
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import Column, String, Text, DateTime, Boolean, JSON
# from sqlalchemy.dialects.postgresql import UUID  # Commented out for SQLite compatibility
import uuid
from datetime import datetime

from .models import DOSTObject
from .factory import dost_object_factory
from ...core.database import Base
from ...dss.search.vector_store import vector_store
from ...ai.models.llm_client import llm_client

logger = logging.getLogger(__name__)


class DOSTObjectDB(Base):
    """Database model for DOST Objects."""
    __tablename__ = "dost_objects"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    entity_id = Column(String, nullable=False, index=True)
    object_type = Column(String, nullable=False, index=True)
    status = Column(String, default="active", index=True)
    
    # Core data
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String, nullable=False, index=True)
    subcategory = Column(String, index=True)
    
    # Complete DOST Object as JSON
    dost_data = Column(JSON, nullable=False)
    
    # Search and indexing
    keywords = Column(JSON)
    embedding_vector = Column(JSON)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)


class DOSTObjectManager:
    """
    Manages DOST Objects throughout their lifecycle.
    Handles creation, storage, retrieval, updates, and search indexing.
    """
    
    def __init__(self):
        self.factory = dost_object_factory
        self.vector_store = vector_store
        self.llm_client = llm_client
    
    async def create_object(
        self,
        entity_id: str,
        description: str,
        additional_data: Dict[str, Any] = None,
        db: Session = None
    ) -> DOSTObject:
        """
        Create a new DOST Object.
        
        Args:
            entity_id: Entity creating the object
            description: Natural language description
            additional_data: Additional structured data
            db: Database session
            
        Returns:
            Created DOST Object
        """
        try:
            # Create DOST Object using factory
            dost_object = await self.factory.create_from_natural_language(
                entity_id=entity_id,
                description=description,
                additional_data=additional_data or {}
            )
            
            # Store in database
            if db:
                await self._store_object(dost_object, db)
            
            # Index for search
            await self._index_object(dost_object)
            
            logger.info(f"Created DOST Object: {dost_object.metadata.id}")
            return dost_object
            
        except Exception as e:
            logger.error(f"Error creating DOST Object: {str(e)}")
            raise
    
    async def get_object(self, object_id: str, db: Session) -> Optional[DOSTObject]:
        """Get DOST Object by ID."""
        try:
            db_object = db.query(DOSTObjectDB).filter(
                DOSTObjectDB.id == uuid.UUID(object_id),
                DOSTObjectDB.is_active == True
            ).first()
            
            if not db_object:
                return None
            
            # Reconstruct DOST Object from stored data
            dost_object = DOSTObject.parse_obj(db_object.dost_data)
            return dost_object
            
        except Exception as e:
            logger.error(f"Error getting DOST Object {object_id}: {str(e)}")
            return None
    
    async def update_object(
        self,
        object_id: str,
        updates: Dict[str, Any],
        db: Session
    ) -> Optional[DOSTObject]:
        """Update DOST Object."""
        try:
            # Get existing object
            dost_object = await self.get_object(object_id, db)
            if not dost_object:
                return None
            
            # Apply updates
            updated_object = await self._apply_updates(dost_object, updates)
            
            # Store updated object
            await self._store_object(updated_object, db, update=True)
            
            # Re-index for search
            await self._index_object(updated_object)
            
            logger.info(f"Updated DOST Object: {object_id}")
            return updated_object
            
        except Exception as e:
            logger.error(f"Error updating DOST Object {object_id}: {str(e)}")
            raise
    
    async def delete_object(self, object_id: str, db: Session) -> bool:
        """Soft delete DOST Object."""
        try:
            db_object = db.query(DOSTObjectDB).filter(
                DOSTObjectDB.id == uuid.UUID(object_id)
            ).first()
            
            if not db_object:
                return False
            
            # Soft delete
            db_object.is_active = False
            db_object.status = "inactive"
            db_object.updated_at = datetime.utcnow()
            db.commit()
            
            # Remove from search index
            await self.vector_store.delete_service(object_id, "dost_objects")
            
            logger.info(f"Deleted DOST Object: {object_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting DOST Object {object_id}: {str(e)}")
            return False
    
    async def search_objects(
        self,
        query: str,
        filters: Dict[str, Any] = None,
        limit: int = 10,
        db: Session = None
    ) -> List[DOSTObject]:
        """Search DOST Objects using semantic search."""
        try:
            # Generate query embedding
            query_embedding = await self.llm_client.generate_embedding(query)
            
            # Search vector store
            search_results = await self.vector_store.search_similar(
                query_vector=query_embedding,
                collection_name="dost_objects",
                limit=limit,
                score_threshold=0.7,
                filters=filters
            )
            
            # Convert results to DOST Objects
            objects = []
            for result in search_results:
                object_id = result.get("object_id")
                if object_id and db:
                    dost_object = await self.get_object(object_id, db)
                    if dost_object:
                        objects.append(dost_object)
            
            return objects
            
        except Exception as e:
            logger.error(f"Error searching DOST Objects: {str(e)}")
            return []
    
    async def get_objects_by_entity(
        self,
        entity_id: str,
        db: Session,
        limit: int = 50
    ) -> List[DOSTObject]:
        """Get all objects for an entity."""
        try:
            db_objects = db.query(DOSTObjectDB).filter(
                DOSTObjectDB.entity_id == entity_id,
                DOSTObjectDB.is_active == True
            ).limit(limit).all()
            
            objects = []
            for db_object in db_objects:
                dost_object = DOSTObject.parse_obj(db_object.dost_data)
                objects.append(dost_object)
            
            return objects
            
        except Exception as e:
            logger.error(f"Error getting objects for entity {entity_id}: {str(e)}")
            return []
    
    async def _store_object(self, dost_object: DOSTObject, db: Session, update: bool = False):
        """Store DOST Object in database."""
        try:
            if update:
                # Update existing object
                db_object = db.query(DOSTObjectDB).filter(
                    DOSTObjectDB.id == uuid.UUID(dost_object.metadata.id)
                ).first()
                
                if db_object:
                    db_object.title = dost_object.identity.title
                    db_object.description = dost_object.identity.description
                    db_object.category = dost_object.identity.category
                    db_object.subcategory = dost_object.identity.subcategory
                    db_object.keywords = dost_object.identity.keywords
                    db_object.dost_data = dost_object.dict()
                    db_object.updated_at = datetime.utcnow()
            else:
                # Create new object
                db_object = DOSTObjectDB(
                    id=uuid.UUID(dost_object.metadata.id),
                    entity_id=dost_object.metadata.entity_id,
                    object_type=dost_object.metadata.object_type.value,
                    status=dost_object.metadata.status.value,
                    title=dost_object.identity.title,
                    description=dost_object.identity.description,
                    category=dost_object.identity.category,
                    subcategory=dost_object.identity.subcategory,
                    keywords=dost_object.identity.keywords,
                    dost_data=dost_object.dict()
                )
                db.add(db_object)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error storing DOST Object: {str(e)}")
            db.rollback()
            raise
    
    async def _index_object(self, dost_object: DOSTObject):
        """Index DOST Object for semantic search."""
        try:
            # Create search text
            search_text = f"{dost_object.identity.title} {dost_object.identity.description}"
            search_text += f" {' '.join(dost_object.identity.keywords)}"
            
            # Generate embedding
            embedding = await self.llm_client.generate_embedding(search_text)
            
            # Prepare metadata for vector store
            metadata = {
                "object_id": dost_object.metadata.id,
                "entity_id": dost_object.metadata.entity_id,
                "object_type": dost_object.metadata.object_type.value,
                "title": dost_object.identity.title,
                "description": dost_object.identity.description,
                "category": dost_object.identity.category,
                "subcategory": dost_object.identity.subcategory,
                "keywords": dost_object.identity.keywords,
                "status": dost_object.metadata.status.value
            }
            
            # Store in vector database
            await self.vector_store.upsert_service(
                service_id=dost_object.metadata.id,
                embedding=embedding,
                metadata=metadata,
                collection_name="dost_objects"
            )
            
            # Update AI metadata
            dost_object.ai_metadata.embedding.vector = embedding
            dost_object.ai_metadata.last_indexed = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Error indexing DOST Object: {str(e)}")
    
    async def _apply_updates(self, dost_object: DOSTObject, updates: Dict[str, Any]) -> DOSTObject:
        """Apply updates to DOST Object."""
        # Create a copy of the object data
        object_data = dost_object.dict()
        
        # Apply updates to nested structure
        for key, value in updates.items():
            if "." in key:
                # Handle nested updates like "identity.title"
                keys = key.split(".")
                current = object_data
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = value
            else:
                # Handle top-level updates
                if key in object_data:
                    object_data[key] = value
        
        # Update timestamp
        object_data["metadata"]["updated_at"] = datetime.utcnow()
        
        # Recreate DOST Object
        return DOSTObject.parse_obj(object_data)


# Global manager instance
dost_object_manager = DOSTObjectManager()
