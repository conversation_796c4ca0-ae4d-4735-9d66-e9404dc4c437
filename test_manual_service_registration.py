#!/usr/bin/env python3
"""
Manual service registration test - bypassing LLM issues by manually creating service data
"""
import requests
import json
import time
from supabase import create_client

# Configuration
BACKEND_URL = "http://localhost:8000"
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def register_user_and_get_token():
    """Register a service provider and get auth token"""
    print("🔐 Registering service provider...")
    
    register_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123',
        'full_name': 'Manual Service Provider',
        'role': 'service_provider',
        'terms_accepted': True
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/api/v1/auth/register", json=register_data)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Provider registered: {data['user']['user_id']}")
            return data['token'], data['user']['user_id']
        else:
            print(f"❌ Registration failed: {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None, None

def create_manual_service_data(description, provider_id):
    """Manually create service data (simulating LLM extraction)"""
    print(f"🔧 Manually creating service data...")

    # Service templates based on description keywords
    if "plumbing" in description.lower():
        return {
            'service_id': f"svc_{int(time.time())}",
            'provider_id': provider_id,
            'title': 'Professional Plumbing Services',
            'description': 'Expert plumbing services including repairs, installations, and emergency fixes. Available 24/7 with competitive rates.',
            'category': 'Home Services',
            'subcategory': 'Plumbing',
            'keywords': ['plumbing', 'repairs', 'installation', 'emergency', 'mumbai'],
            'price_min': 500,
            'price_max': 2000,
            'currency': 'INR',
            'pricing_model': 'hourly',
            'is_negotiable': True,
            'service_type': 'on_location',
            'availability': [
                {'day': 'mon', 'from_time': '09:00', 'to_time': '18:00'},
                {'day': 'tue', 'from_time': '09:00', 'to_time': '18:00'},
                {'day': 'wed', 'from_time': '09:00', 'to_time': '18:00'},
                {'day': 'thu', 'from_time': '09:00', 'to_time': '18:00'},
                {'day': 'fri', 'from_time': '09:00', 'to_time': '18:00'},
                {'day': 'sat', 'from_time': '10:00', 'to_time': '16:00'}
            ],
            'status': 'active'
        }
    elif "cleaning" in description.lower():
        return {
            'service_id': f"svc_{int(time.time())+1}",
            'provider_id': provider_id,
            'title': 'Professional Home Cleaning Services',
            'description': 'Expert home cleaning services including deep cleaning, regular maintenance, and eco-friendly solutions for residential properties.',
            'category': 'Home Services',
            'subcategory': 'Cleaning',
            'keywords': ['cleaning', 'home', 'deep cleaning', 'maintenance', 'bangalore'],
            'price_min': 1000,
            'price_max': 3000,
            'currency': 'INR',
            'pricing_model': 'fixed',
            'is_negotiable': True,
            'service_type': 'on_location',
            'availability': [
                {'day': 'mon', 'from_time': '08:00', 'to_time': '17:00'},
                {'day': 'tue', 'from_time': '08:00', 'to_time': '17:00'},
                {'day': 'wed', 'from_time': '08:00', 'to_time': '17:00'},
                {'day': 'thu', 'from_time': '08:00', 'to_time': '17:00'},
                {'day': 'fri', 'from_time': '08:00', 'to_time': '17:00'},
                {'day': 'sat', 'from_time': '09:00', 'to_time': '15:00'}
            ],
            'status': 'active'
        }
    elif "web development" in description.lower() or "website" in description.lower():
        return {
            'service_id': f"svc_{int(time.time())+2}",
            'provider_id': provider_id,
            'title': 'Professional Web Development Services',
            'description': 'Full-stack web development services including responsive websites, e-commerce platforms, and mobile applications using modern technologies.',
            'category': 'Technology',
            'subcategory': 'Web Development',
            'keywords': ['web development', 'website', 'react', 'nodejs', 'ecommerce'],
            'price_min': 25000,
            'price_max': 100000,
            'currency': 'INR',
            'pricing_model': 'fixed',
            'is_negotiable': True,
            'service_type': 'remote',
            'availability': [
                {'day': 'mon', 'from_time': '10:00', 'to_time': '19:00'},
                {'day': 'tue', 'from_time': '10:00', 'to_time': '19:00'},
                {'day': 'wed', 'from_time': '10:00', 'to_time': '19:00'},
                {'day': 'thu', 'from_time': '10:00', 'to_time': '19:00'},
                {'day': 'fri', 'from_time': '10:00', 'to_time': '19:00'}
            ],
            'status': 'active'
        }
    else:
        # Generic service
        return {
            'service_id': f"svc_{int(time.time())+3}",
            'provider_id': provider_id,
            'title': 'Professional Service',
            'description': description,
            'category': 'General',
            'subcategory': 'Other',
            'keywords': ['service', 'professional'],
            'price_min': 500,
            'price_max': 2000,
            'currency': 'INR',
            'pricing_model': 'negotiable',
            'is_negotiable': True,
            'service_type': 'on_location',
            'availability': [
                {'day': 'mon', 'from_time': '09:00', 'to_time': '17:00'},
                {'day': 'tue', 'from_time': '09:00', 'to_time': '17:00'},
                {'day': 'wed', 'from_time': '09:00', 'to_time': '17:00'},
                {'day': 'thu', 'from_time': '09:00', 'to_time': '17:00'},
                {'day': 'fri', 'from_time': '09:00', 'to_time': '17:00'}
            ],
            'status': 'active'
        }

def store_service_in_supabase(service_data):
    """Store service directly in Supabase"""
    print(f"💾 Storing service in Supabase: {service_data['title']}")
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Add timestamps
        service_data['created_at'] = 'now()'
        service_data['updated_at'] = 'now()'
        
        # Insert into Supabase
        result = supabase.table('services').insert(service_data).execute()
        
        if result.data:
            print(f"✅ Service stored successfully!")
            print(f"   Service ID: {service_data['service_id']}")
            print(f"   Title: {service_data['title']}")
            print(f"   Category: {service_data['category']}")
            print(f"   Price Range: ₹{service_data['price_min']}-₹{service_data['price_max']}")
            print(f"   Category: {service_data['category']}")
            return service_data['service_id']
        else:
            print(f"❌ Failed to store in Supabase: {result}")
            return None
            
    except Exception as e:
        print(f"❌ Supabase storage error: {e}")
        return None

def main():
    print("🚀 Testing Manual Service Registration (Bypassing LLM)")
    print("=" * 70)
    
    # Step 1: Register user and get token
    token, user_id = register_user_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Step 2: Test service descriptions
    test_services = [
        "I provide professional plumbing services in Mumbai. I can fix leaky faucets, install new pipes, and handle emergency repairs. Available 24/7. Rates start from ₹500 per hour.",
        
        "Expert home cleaning services in Bangalore. Deep cleaning, regular maintenance, post-construction cleanup. Eco-friendly products used. ₹1500 for 2BHK apartment.",
        
        "Professional web development services. I create responsive websites, e-commerce platforms, and mobile apps using React, Node.js, and Python. Based in Delhi, also work remotely. Starting from ₹25,000 per project."
    ]
    
    successful_registrations = 0
    
    for i, service_desc in enumerate(test_services, 1):
        print(f"\n{'='*20} SERVICE {i} {'='*20}")
        print(f"Description: {service_desc[:80]}...")
        
        # Create manual service data (simulating LLM extraction)
        service_data = create_manual_service_data(service_desc, user_id)
        
        # Store in Supabase
        service_id = store_service_in_supabase(service_data)
        if service_id:
            successful_registrations += 1
        
        time.sleep(1)  # Small delay between registrations
    
    # Step 3: Check final results
    print(f"\n{'='*70}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*70}")
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        services = supabase.table('services').select('*').execute()
        
        print(f"✅ Services successfully registered: {successful_registrations}")
        print(f"✅ Total services in Supabase: {len(services.data)}")
        
        if services.data:
            print("\n📋 Services in database:")
            for service in services.data:
                print(f"   - {service.get('title', 'No title')}")
                print(f"     Category: {service.get('category', 'No category')}")
                print(f"     Service Type: {service.get('service_type', 'No type')}")
                print(f"     Price: ₹{service.get('price_min', 0)}-₹{service.get('price_max', 0)}")
        
        if successful_registrations > 0:
            print("\n🎉 SUCCESS: Manual service registration working!")
            print("   - Service data creation: ✅ Working")
            print("   - Supabase storage: ✅ Working")
            print("   - Database integration: ✅ Working")
            print("\n💡 Next step: Fix LLM client to enable automatic extraction")
        else:
            print("\n❌ No services were successfully registered")
            
    except Exception as e:
        print(f"❌ Error checking results: {e}")

if __name__ == "__main__":
    main()
