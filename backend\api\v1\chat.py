"""
Chat API Router
Handles chat-related endpoints and WebSocket connections
"""

"""
ChatConnectionManager
    connect
    disconnect
    send_message


chat_websocket
handle_text_message
handle_audio_input
handle_service_interaction
send_message
get_chat_history
"""
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from sqlalchemy.orm import Session

from ...core.database import get_db, create_session
from ...dost.events.models import create_text_event, EventType
from ...ai.assistants.user_assistant import user_assistant
from ...ai.models.llm_client import llm_client

logger = logging.getLogger(__name__)

# Create router
chat_router = APIRouter()


class ChatConnectionManager:
    """Manages chat WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, str] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, session_id: str):
        """Accept WebSocket connection."""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = session_id
        logger.info(f"User {user_id} connected to chat session {session_id}")
    
    def disconnect(self, user_id: str):
        """Remove WebSocket connection."""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        logger.info(f"User {user_id} disconnected from chat")
    
    async def send_message(self, user_id: str, message: Dict[str, Any]):
        """Send message to specific user."""
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {user_id}: {str(e)}")
                self.disconnect(user_id)


# Global connection manager
chat_manager = ChatConnectionManager()


@chat_router.websocket("/ws/{user_id}")
async def chat_websocket(websocket: WebSocket, user_id: str, db: Session = Depends(get_db)):
    """
    WebSocket endpoint for real-time chat with AI assistant.
    
    Message format:
    {
        "type": "chat|audio|service_request",
        "content": "message content",
        "metadata": {...}
    }
    """
    session_id = None
    
    try:
        # Create session
        session = create_session([user_id, "ai.happidost.user_assistant"], "user_ai_chat", db)
        session_id = str(session.session_id)
        
        # Connect user
        await chat_manager.connect(websocket, user_id, session_id)
        
        # Send welcome message
        welcome_message = {
            "type": "system",
            "message": "Connected to HappiDost AI Assistant",
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Message processing loop
        while True:
            try:
                # Receive message
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # Validate message format
                if "type" not in message_data:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": "Message type is required",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
                    continue
                
                # Route message based on type
                if message_data["type"] == "chat":
                    await handle_text_message(websocket, user_id, session_id, message_data, db)
                elif message_data["type"] == "audio":
                    await handle_audio_input(websocket, user_id, session_id, message_data, db)
                elif message_data["type"] == "service_request":
                    await handle_service_interaction(websocket, user_id, session_id, message_data, db)
                else:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": f"Unknown message type: {message_data['type']}",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
                    
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": datetime.utcnow().isoformat()
                }))
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Error processing message",
                    "timestamp": datetime.utcnow().isoformat()
                }))
                
    except WebSocketDisconnect:
        chat_manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"WebSocket connection error: {str(e)}")
        chat_manager.disconnect(user_id)


async def handle_text_message(
    websocket: WebSocket,
    user_id: str,
    session_id: str,
    message_data: Dict[str, Any],
    db: Session
):
    """Handle text chat messages."""
    try:
        user_message = message_data.get("content", "")
        if not user_message:
            return
        
        # Create DOST event
        event = create_text_event(
            session_id=session_id,
            source_entity_id=user_id,
            target_entity_id="ai.happidost.user_assistant",
            text_content=user_message
        )
        
        # Process with user assistant
        response_event = await user_assistant.process_event(event, db)
        
        if response_event and response_event.dost_event_message.text:
            response_text = response_event.dost_event_message.text.get("chat", "")
            
            # Generate audio if requested
            audio_data = None
            if message_data.get("include_audio", False):
                audio_bytes = await llm_client.generate_audio(response_text)
                if audio_bytes:
                    import base64
                    audio_data = base64.b64encode(audio_bytes).decode("utf-8")
            
            # Send response
            response = {
                "type": "chat_response",
                "content": response_text,
                "audio": audio_data,
                "event_id": response_event.event_id,
                "metadata": response_event.context or {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        logger.error(f"Error handling text message: {str(e)}")


async def handle_audio_input(
    websocket: WebSocket,
    user_id: str,
    session_id: str,
    message_data: Dict[str, Any],
    db: Session
):
    """Handle audio input messages."""
    try:
        audio_data = message_data.get("audio_data", "")
        if not audio_data:
            return
        
        # Decode and transcribe audio
        import base64
        audio_bytes = base64.b64decode(audio_data)
        transcribed_text = await llm_client.transcribe_audio(audio_bytes)
        
        if transcribed_text:
            # Process as text message
            text_message = {
                "type": "chat",
                "content": transcribed_text,
                "include_audio": True  # Include audio in response
            }
            await handle_text_message(websocket, user_id, session_id, text_message, db)
        else:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Could not transcribe audio",
                "timestamp": datetime.utcnow().isoformat()
            }))
        
    except Exception as e:
        logger.error(f"Error handling audio input: {str(e)}")


async def handle_service_interaction(
    websocket: WebSocket,
    user_id: str,
    session_id: str,
    message_data: Dict[str, Any],
    db: Session
):
    """Handle service interaction requests."""
    try:
        service_id = message_data.get("service_id")
        action = message_data.get("action", "")  # "book", "inquire", "cancel"
        details = message_data.get("details", {})
        
        # Create service interaction event
        event = create_text_event(
            session_id=session_id,
            source_entity_id=user_id,
            target_entity_id=service_id or "ai.happidost.service_assistant",
            text_content=f"Service {action}: {json.dumps(details)}",
            event_type=EventType.REQUEST
        )
        
        # Process with appropriate assistant
        # This will be enhanced when service assistant is implemented
        response = {
            "type": "service_response",
            "message": f"Processing {action} request for service {service_id}",
            "service_id": service_id,
            "action": action,
            "status": "processing",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await websocket.send_text(json.dumps(response))
        
    except Exception as e:
        logger.error(f"Error handling service interaction: {str(e)}")


# REST API endpoints for chat
@chat_router.post("/send-message")
async def send_message(
    user_id: str,
    message: str,
    session_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Send a message via REST API (alternative to WebSocket)."""
    try:
        # Create or get session
        if not session_id:
            session = create_session([user_id, "ai.happidost.user_assistant"], "user_ai_chat", db)
            session_id = str(session.session_id)
        
        # Create and process event
        event = create_text_event(
            session_id=session_id,
            source_entity_id=user_id,
            target_entity_id="ai.happidost.user_assistant",
            text_content=message
        )
        
        response_event = await user_assistant.process_event(event, db)
        
        if response_event and response_event.dost_event_message.text:
            return {
                "success": True,
                "response": response_event.dost_event_message.text.get("chat", ""),
                "session_id": session_id,
                "event_id": response_event.event_id,
                "metadata": response_event.context or {}
            }
        else:
            return {
                "success": False,
                "message": "Failed to generate response",
                "session_id": session_id
            }
            
    except Exception as e:
        logger.error(f"Error in send_message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@chat_router.get("/history/{session_id}")
async def get_chat_history(session_id: str, limit: int = 20, db: Session = Depends(get_db)):
    """Get chat history for a session."""
    try:
        from ...core.database import DOSTEvent as DOSTEventDB
        
        events = db.query(DOSTEventDB).filter(
            DOSTEventDB.session_id == session_id,
            DOSTEventDB.event_type == EventType.MESSAGE.value
        ).order_by(DOSTEventDB.timestamp.desc()).limit(limit).all()
        
        history = []
        for event in reversed(events):
            if event.message_content and event.message_content.get("text"):
                history.append({
                    "event_id": str(event.event_id),
                    "timestamp": event.timestamp.isoformat(),
                    "source": event.source_entity_id,
                    "target": event.target_entity_id,
                    "message": event.message_content["text"].get("chat", ""),
                    "is_ai": event.is_ai_generated
                })
        
        return {
            "success": True,
            "session_id": session_id,
            "history": history,
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"Error getting chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
