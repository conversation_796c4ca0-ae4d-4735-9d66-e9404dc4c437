"""
Service Registration API Endpoints
Handles natural language service registration and DSS integration
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from ...services.service_registration import service_registration_system
from ...core.auth import get_current_user
from ...core.supabase_client import get_supabase_client

# Configure logging
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Router
router = APIRouter(prefix="/service-registration", tags=["Service Registration"])

# Request/Response Models
class ServiceRegistrationRequest(BaseModel):
    """Request model for service registration"""
    natural_language_description: str = Field(
        ..., 
        min_length=50, 
        max_length=5000,
        description="Natural language description of the service (50-5000 characters)"
    )
    provider_location: Optional[str] = Field(
        None,
        description="Optional provider location if not mentioned in description"
    )
    additional_context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional context or metadata"
    )
    
    @validator('natural_language_description')
    def validate_description(cls, v):
        if not v.strip():
            raise ValueError("Description cannot be empty")
        return v.strip()

class ServiceRegistrationResponse(BaseModel):
    """Response model for service registration"""
    success: bool
    service_id: Optional[str] = None
    supabase_id: Optional[str] = None
    vespa_document_id: Optional[str] = None
    extracted_data: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time_ms: Optional[int] = None

class ServiceUpdateRequest(BaseModel):
    """Request model for service updates"""
    service_id: str
    updates: Dict[str, Any]
    reason: Optional[str] = None

class ServiceSearchRequest(BaseModel):
    """Request model for service search"""
    query: str = Field(..., min_length=1, max_length=500)
    location: Optional[Dict[str, float]] = None  # {"lat": 12.9716, "lon": 77.5946}
    max_distance_km: Optional[float] = Field(default=50.0, ge=1.0, le=500.0)
    category: Optional[str] = None
    price_range: Optional[Dict[str, float]] = None  # {"min": 100, "max": 1000}
    limit: Optional[int] = Field(default=20, ge=1, le=100)
    offset: Optional[int] = Field(default=0, ge=0)

class ServiceSearchResponse(BaseModel):
    """Response model for service search"""
    success: bool
    total_results: int
    results: List[Dict[str, Any]]
    search_time_ms: int
    query_info: Dict[str, Any]

# API Endpoints

@router.post("/register", response_model=ServiceRegistrationResponse)
async def register_service(
    request: ServiceRegistrationRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Register a new service using natural language description
    
    This endpoint:
    1. Extracts structured data from natural language using GPT-4
    2. Geocodes location information
    3. Generates embeddings for semantic search
    4. Stores in Supabase database
    5. Indexes in Vespa for search
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"Service registration request from user {current_user['id']}")
        
        # Validate user role
        if current_user.get('role') not in ['service_provider', 'business', 'admin']:
            raise HTTPException(
                status_code=403,
                detail="Only service providers and businesses can register services"
            )
        
        # Get the Supabase user_id from user_profiles table using service key
        # Service key bypasses RLS entirely
        from supabase import create_client
        from ...core.config import settings

        supabase_service = create_client(
            settings.supabase_url,
            settings.supabase_service_key  # Use service key to bypass RLS
        )

        profile_result = supabase_service.table("user_profiles").select("user_id").eq(
            "entity_id", current_user['id']
        ).execute()

        if not profile_result.data or len(profile_result.data) == 0:
            raise HTTPException(
                status_code=404,
                detail="User profile not found. Please complete registration."
            )

        supabase_user_id = profile_result.data[0]['user_id']
        logger.info(f"Using Supabase user_id: {supabase_user_id} for entity: {current_user['id']}")

        # Ensure the user has a record in the profiles table (required for foreign key)
        profiles_check = supabase_service.table("profiles").select("user_id").eq(
            "user_id", supabase_user_id
        ).execute()

        if not profiles_check.data or len(profiles_check.data) == 0:
            logger.info(f"Creating profiles record for user_id: {supabase_user_id}")
            # Create a basic profile record
            profile_data = {
                "user_id": supabase_user_id,
                "full_name": "Service Provider",
                "email": "<EMAIL>",  # This should come from the entity data
                "is_active": True,
                "reputation_score": 0.0,
                "total_transactions": 0,
                "preferences": {},
                "privacy_settings": {"share_location": False, "share_contact": False},
                "notification_settings": {"email_notifications": True, "push_notifications": True}
            }

            supabase_service.table("profiles").insert(profile_data).execute()
            logger.info(f"Created profiles record for user_id: {supabase_user_id}")

        # Register the service
        result = await service_registration_system.register_service(
            provider_id=supabase_user_id,  # Use Supabase UUID instead of HappiDost entity_id
            natural_language_description=request.natural_language_description,
            provider_location=request.provider_location,
            user_token=credentials.credentials
        )
        
        # Calculate processing time
        processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        if result['success']:
            # Schedule background tasks
            background_tasks.add_task(
                _post_registration_tasks,
                result['service_id'],
                current_user['id']
            )
            
            logger.info(f"Service registered successfully: {result['service_id']}")
            
            return ServiceRegistrationResponse(
                success=True,
                service_id=result['service_id'],
                supabase_id=result['supabase_id'],
                vespa_document_id=result['vespa_document_id'],
                extracted_data=result['extracted_data'],
                location=result['location'],
                processing_time_ms=processing_time
            )
        else:
            logger.error(f"Service registration failed: {result['error']}")
            raise HTTPException(
                status_code=400,
                detail=f"Service registration failed: {result['error']}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in service registration: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during service registration"
        )

@router.get("/my-services")
async def get_my_services(
    current_user: Dict[str, Any] = Depends(get_current_user),
    limit: int = 20,
    offset: int = 0
):
    """Get all services registered by the current user"""
    try:
        supabase = get_supabase_client()
        
        # Query user's services
        result = supabase.table("services").select(
            "id, service_id, title, description, category, subcategory, "
            "price_min, price_max, currency, status, verification_status, "
            "average_rating, total_reviews, created_at, updated_at"
        ).eq(
            "provider_id", current_user['id']
        ).order(
            "created_at", desc=True
        ).range(offset, offset + limit - 1).execute()
        
        return {
            "success": True,
            "services": result.data,
            "total": len(result.data)
        }
        
    except Exception as e:
        logger.error(f"Error fetching user services: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch services"
        )

@router.get("/service/{service_id}")
async def get_service_details(
    service_id: str,
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user)
):
    """Get detailed information about a specific service"""
    try:
        supabase = get_supabase_client()
        
        # Query service details
        result = supabase.table("services").select(
            "*, profiles!services_provider_id_fkey(full_name, avatar_url, reputation_score)"
        ).eq("service_id", service_id).single().execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Service not found")
        
        service = result.data
        
        # Check if user can view this service
        if service['status'] != 'active' and (
            not current_user or 
            current_user['id'] != service['provider_id']
        ):
            raise HTTPException(status_code=404, detail="Service not found")
        
        return {
            "success": True,
            "service": service
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching service details: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch service details"
        )

@router.put("/service/{service_id}")
async def update_service(
    service_id: str,
    request: ServiceUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Update an existing service"""
    try:
        supabase = get_supabase_client()
        
        # Verify ownership
        service_result = supabase.table("services").select(
            "id, provider_id, vespa_document_id"
        ).eq("service_id", service_id).single().execute()
        
        if not service_result.data:
            raise HTTPException(status_code=404, detail="Service not found")
        
        service = service_result.data
        
        if service['provider_id'] != current_user['id']:
            raise HTTPException(
                status_code=403,
                detail="You can only update your own services"
            )
        
        # Update in Supabase
        update_data = request.updates.copy()
        update_data['updated_at'] = datetime.now().isoformat()
        
        result = supabase.table("services").update(
            update_data
        ).eq("service_id", service_id).execute()
        
        # TODO: Update in Vespa as well
        
        return {
            "success": True,
            "message": "Service updated successfully",
            "updated_service": result.data[0] if result.data else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating service: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to update service"
        )

@router.delete("/service/{service_id}")
async def delete_service(
    service_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Delete/deactivate a service"""
    try:
        supabase = get_supabase_client()
        
        # Verify ownership
        service_result = supabase.table("services").select(
            "id, provider_id, vespa_document_id"
        ).eq("service_id", service_id).single().execute()
        
        if not service_result.data:
            raise HTTPException(status_code=404, detail="Service not found")
        
        service = service_result.data
        
        if service['provider_id'] != current_user['id']:
            raise HTTPException(
                status_code=403,
                detail="You can only delete your own services"
            )
        
        # Soft delete - update status to 'deleted'
        result = supabase.table("services").update({
            "status": "deleted",
            "updated_at": datetime.now().isoformat()
        }).eq("service_id", service_id).execute()
        
        # TODO: Remove from Vespa index
        
        return {
            "success": True,
            "message": "Service deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting service: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to delete service"
        )

@router.post("/search", response_model=ServiceSearchResponse)
async def search_services(request: ServiceSearchRequest):
    """Search services using hybrid search (BM25 + Vector + Metadata)"""
    start_time = datetime.now()
    
    try:
        # TODO: Implement Vespa search
        # For now, return mock response
        
        search_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        return ServiceSearchResponse(
            success=True,
            total_results=0,
            results=[],
            search_time_ms=search_time,
            query_info={
                "query": request.query,
                "location": request.location,
                "category": request.category,
                "price_range": request.price_range
            }
        )
        
    except Exception as e:
        logger.error(f"Error searching services: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Search failed"
        )

@router.get("/categories")
async def get_service_categories():
    """Get all available service categories"""
    try:
        supabase = get_supabase_client()
        
        # Get distinct categories and subcategories
        result = supabase.table("services").select(
            "category, subcategory"
        ).eq("status", "active").execute()
        
        # Group by category
        categories = {}
        for service in result.data:
            category = service['category']
            subcategory = service.get('subcategory')
            
            if category not in categories:
                categories[category] = set()
            
            if subcategory:
                categories[category].add(subcategory)
        
        # Convert sets to lists
        categories = {
            category: list(subcategories) 
            for category, subcategories in categories.items()
        }
        
        return {
            "success": True,
            "categories": categories
        }
        
    except Exception as e:
        logger.error(f"Error fetching categories: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch categories"
        )

# Background Tasks

async def _post_registration_tasks(service_id: str, provider_id: str):
    """Background tasks to run after service registration"""
    try:
        # 1. Send welcome email to provider
        # 2. Notify admin for verification
        # 3. Update provider statistics
        # 4. Generate service recommendations
        
        logger.info(f"Post-registration tasks completed for service {service_id}")
        
    except Exception as e:
        logger.error(f"Post-registration tasks failed for service {service_id}: {str(e)}")

@router.get("/featured")
async def get_featured_services(limit: int = 6):
    """Get featured services for the homepage"""
    try:
        supabase = get_supabase_client()

        # For now, return the most recent services as featured
        # In production, this would be based on ratings, popularity, etc.
        result = supabase.table("services").select(
            "id, service_id, title, description, category, subcategory, "
            "price_min, price_max, currency, average_rating, total_reviews, "
            "location, availability, created_at, "
            "profiles!services_provider_id_fkey(full_name, avatar_url)"
        ).eq(
            "status", "active"
        ).eq(
            "verification_status", "verified"
        ).order(
            "average_rating", desc=True
        ).limit(limit).execute()

        return {
            "success": True,
            "services": result.data or [],
            "total": len(result.data) if result.data else 0
        }

    except Exception as e:
        logger.error(f"Error fetching featured services: {str(e)}")
        # Return empty list instead of error for better UX
        return {
            "success": True,
            "services": [],
            "total": 0
        }

# Health check
@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "service-registration",
        "timestamp": datetime.now().isoformat()
    }
