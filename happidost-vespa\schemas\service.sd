schema service {
    document service {
        field service_id type string {
            indexing: attribute | summary
        }
        field title type string {
            indexing: attribute | summary | index
        }
        field description type string {
            indexing: index | summary
        }
        field category type string {
            indexing: attribute | summary | index
        }
        field subcategory type string {
            indexing: attribute | summary
        }
        field provider_id type string {
            indexing: attribute | summary
        }
        field provider_name type string {
            indexing: attribute | summary | index
        }
        field location_city type string {
            indexing: attribute | summary | index
        }
        field location_country type string {
            indexing: attribute | summary
        }
        field price_min type double {
            indexing: attribute | summary
        }
        field price_max type double {
            indexing: attribute | summary
        }
        field currency type string {
            indexing: attribute | summary
        }
        field rating type double {
            indexing: attribute | summary
        }
        field total_reviews type int {
            indexing: attribute | summary
        }
        field keywords type array<string> {
            indexing: attribute | summary | index
        }
        field availability type string {
            indexing: attribute | summary
        }
        field status type string {
            indexing: attribute | summary
        }
        field embedding type tensor<float>(x[1536]) {
            indexing: attribute | index
            attribute {
                distance-metric: cosine
            }
        }
        field created_at type long {
            indexing: attribute | summary
        }
    }
    
    rank-profile default {
        first-phase {
            expression: nativeRank(title, description) + attribute(rating) * 0.1
        }
    }
    
    rank-profile semantic inherits default {
        first-phase {
            expression: closeness(field, embedding)
        }
    }
    
    rank-profile hybrid inherits default {
        first-phase {
            expression: nativeRank(title, description) + closeness(field, embedding) * 0.5 + attribute(rating) * 0.1
        }
    }
}
