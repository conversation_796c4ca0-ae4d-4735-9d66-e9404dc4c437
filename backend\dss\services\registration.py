"""
Service Registration Manager
Handles service onboarding and registration processes
"""
import logging
import uuid
from typing import Dict, Any
from sqlalchemy.orm import Session

from ...core.database import Service, Entity
from ...ai.models.llm_client import llm_client
from ..search.vector_store import vector_store

logger = logging.getLogger(__name__)


class ServiceRegistrationManager:
    """
    Manages service registration and onboarding process.
    Handles natural language processing and vector embedding generation.
    """
    
    def __init__(self):
        self.llm_client = llm_client
        self.vector_store = vector_store
    
    async def register_service(self, service_data: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Register a new service."""
        try:
            # Extract required fields
            entity_id = service_data.get("entity_id")
            service_name = service_data.get("service_name", "")
            description = service_data.get("description", "")
            
            if not entity_id or not service_name or not description:
                return {
                    "success": False,
                    "message": "Missing required fields: entity_id, service_name, description"
                }
            
            # Verify entity exists
            entity = db.query(Entity).filter(Entity.entity_id == entity_id).first()
            if not entity:
                return {
                    "success": False,
                    "message": "Entity not found"
                }
            
            # Process service description with AI
            processed_data = await self._process_service_description(description, service_data)
            
            # Create service record
            service = Service(
                service_id=uuid.uuid4(),
                entity_id=entity_id,
                service_name=service_name,
                description=description,
                category=processed_data.get("category", "general"),
                subcategory=processed_data.get("subcategory"),
                capabilities=processed_data.get("capabilities", []),
                pricing_model=service_data.get("pricing_model", "negotiable"),
                pricing_info=service_data.get("pricing_info", {}),
                availability=service_data.get("availability", {}),
                location_info=service_data.get("location_info", {}),
                requirements=service_data.get("requirements", {}),
                tags=processed_data.get("tags", []),
                is_active=True,
                approval_status="pending"
            )
            
            db.add(service)
            db.commit()
            db.refresh(service)
            
            # Generate and store embedding
            await self._generate_service_embedding(service, db)
            
            logger.info(f"Service registered: {service.service_id}")
            
            return {
                "success": True,
                "service_id": str(service.service_id),
                "approval_status": service.approval_status,
                "message": "Service registered successfully"
            }
            
        except Exception as e:
            logger.error(f"Error registering service: {str(e)}")
            db.rollback()
            return {
                "success": False,
                "message": f"Registration failed: {str(e)}"
            }
    
    async def _process_service_description(self, description: str, service_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process service description using AI to extract structured data."""
        try:
            system_prompt = """
You are a service categorization AI. Analyze the service description and extract structured information.

Extract:
1. Category (food_delivery, home_services, transportation, education, healthcare, beauty_wellness, professional, other)
2. Subcategory (specific type within category)
3. Capabilities (list of what the service can do)
4. Tags (relevant keywords for discovery)

Respond in JSON format:
{
  "category": "category_name",
  "subcategory": "subcategory_name",
  "capabilities": ["capability1", "capability2"],
  "tags": ["tag1", "tag2", "tag3"]
}
"""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Service description: {description}"}
            ]
            
            response = await self.llm_client.generate_response(
                messages, 
                {"response_format": {"type": "json_object"}}
            )
            
            import json
            processed_data = json.loads(response)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing service description: {str(e)}")
            return {
                "category": "general",
                "subcategory": None,
                "capabilities": [],
                "tags": []
            }
    
    async def _generate_service_embedding(self, service: Service, db: Session):
        """Generate and store service embedding for semantic search."""
        try:
            # Create embedding text
            embedding_text = f"{service.service_name} {service.description}"
            
            # Add capabilities
            if service.capabilities:
                capabilities_text = " ".join(service.capabilities)
                embedding_text += f" {capabilities_text}"
            
            # Add tags
            if service.tags:
                tags_text = " ".join(service.tags)
                embedding_text += f" {tags_text}"
            
            # Generate embedding
            embedding = await self.llm_client.generate_embedding(embedding_text)
            
            # Prepare metadata
            metadata = {
                "service_id": str(service.service_id),
                "entity_id": service.entity_id,
                "name": service.service_name,
                "description": service.description,
                "category": service.category,
                "subcategory": service.subcategory,
                "capabilities": service.capabilities,
                "pricing_model": service.pricing_model,
                "pricing_info": service.pricing_info,
                "availability": service.availability,
                "location_info": service.location_info,
                "tags": service.tags,
                "is_active": service.is_active,
                "approval_status": service.approval_status
            }
            
            # Store in vector database
            await self.vector_store.upsert_service(
                service_id=str(service.service_id),
                embedding=embedding,
                metadata=metadata
            )
            
            # Cache embedding in database
            service.embedding_vector = embedding
            db.commit()
            
            logger.info(f"Generated embedding for service: {service.service_id}")
            
        except Exception as e:
            logger.error(f"Error generating service embedding: {str(e)}")
    
    async def update_service_embedding(self, service: Service, db: Session):
        """Update service embedding after changes."""
        await self._generate_service_embedding(service, db)


# Global service registration manager
service_registration_manager = ServiceRegistrationManager()
