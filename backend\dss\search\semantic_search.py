"""
Semantic Search Engine for Service Discovery
Uses vector embeddings and similarity matching to find relevant services
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
import numpy as np

from .vector_store import VectorStore
from ...ai.models.llm_client import LLMClient
from ...core.database import Service, Entity
from ...core.config import settings

logger = logging.getLogger(__name__)


class SemanticSearchEngine:
    """
    Semantic search engine for finding services based on natural language queries.
    Uses vector embeddings and similarity matching with contextual filtering.
    """
    
    def __init__(self):
        self.vector_store = VectorStore()
        self.llm_client = LLMClient()
        
        # Search configuration
        self.similarity_threshold = settings.service_similarity_threshold
        self.max_results = settings.max_search_results
    
    async def search_services(
        self, 
        query: str, 
        context: Dict[str, Any] = None,
        limit: int = None,
        filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for services using semantic similarity.
        
        Args:
            query: Natural language search query
            context: Additional context (user location, preferences, etc.)
            limit: Maximum number of results
            filters: Additional filters to apply
            
        Returns:
            List of matching services with similarity scores
        """
        try:
            logger.info(f"Searching services for query: {query}")
            
            # 1. Enhance query with context
            enhanced_query = await self._enhance_query_with_context(query, context)
            
            # 2. Generate query embedding
            query_embedding = await self.llm_client.generate_embedding(enhanced_query)
            
            # 3. Search vector database
            vector_results = await self.vector_store.search_similar(
                query_vector=query_embedding,
                collection_name="services",
                limit=limit or self.max_results,
                score_threshold=self.similarity_threshold
            )
            
            # 4. Apply contextual filters
            filtered_results = await self._apply_contextual_filters(vector_results, context, filters)
            
            # 5. Rank and score results
            ranked_results = await self._rank_results(filtered_results, query, context)
            
            logger.info(f"Found {len(ranked_results)} services for query: {query}")
            return ranked_results
            
        except Exception as e:
            logger.error(f"Error in semantic search: {str(e)}")
            return []
    
    async def _enhance_query_with_context(self, query: str, context: Dict[str, Any] = None) -> str:
        """Enhance search query with contextual information."""
        enhanced_query = query
        
        if not context:
            return enhanced_query
        
        # Add user location context
        user_info = context.get("user_info", {})
        location = user_info.get("location", {})
        
        if location:
            city = location.get("city", "")
            if city:
                enhanced_query += f" in {city}"
        
        # Add user preferences
        preferences = user_info.get("preferences", {})
        if preferences:
            # Add preferred service types
            preferred_categories = preferences.get("service_categories", [])
            if preferred_categories:
                enhanced_query += f" {' '.join(preferred_categories)}"
            
            # Add budget preferences
            budget = preferences.get("budget", "")
            if budget:
                enhanced_query += f" {budget} budget"
        
        # Add time context
        session_info = context.get("session_info", {})
        if session_info:
            # Add urgency context
            if "urgent" in query.lower() or "asap" in query.lower():
                enhanced_query += " immediate available now"
        
        logger.debug(f"Enhanced query: {query} -> {enhanced_query}")
        return enhanced_query
    
    async def _apply_contextual_filters(
        self, 
        results: List[Dict[str, Any]], 
        context: Dict[str, Any] = None,
        filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Apply contextual filters to search results."""
        if not results:
            return results
        
        filtered_results = results.copy()
        
        # Apply user location filter
        if context and context.get("user_info", {}).get("location"):
            user_location = context["user_info"]["location"]
            filtered_results = self._filter_by_location(filtered_results, user_location)
        
        # Apply availability filter
        filtered_results = self._filter_by_availability(filtered_results)
        
        # Apply custom filters
        if filters:
            for filter_key, filter_value in filters.items():
                filtered_results = self._apply_custom_filter(filtered_results, filter_key, filter_value)
        
        return filtered_results
    
    def _filter_by_location(self, results: List[Dict[str, Any]], user_location: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter results by location proximity."""
        if not user_location:
            return results
        
        user_city = user_location.get("city", "").lower()
        if not user_city:
            return results
        
        filtered = []
        for result in results:
            service_location = result.get("location_info", {})
            
            # Check if service operates in user's city
            if isinstance(service_location, dict):
                service_cities = service_location.get("cities", [])
                if isinstance(service_cities, list):
                    service_cities_lower = [city.lower() for city in service_cities]
                    if user_city in service_cities_lower or "global" in service_cities_lower:
                        filtered.append(result)
                elif service_location.get("type") == "global":
                    filtered.append(result)
        
        return filtered if filtered else results  # Return original if no location matches
    
    def _filter_by_availability(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter results by current availability."""
        current_time = datetime.now()
        current_day = current_time.strftime("%A").lower()
        current_hour = current_time.hour
        
        filtered = []
        for result in results:
            availability = result.get("availability", {})
            
            if not availability:
                # If no availability info, assume available
                filtered.append(result)
                continue
            
            # Check if service is available today
            day_schedule = availability.get(current_day, {})
            if day_schedule.get("closed", False):
                continue
            
            # Check if service is available now
            open_time = day_schedule.get("open", "00:00")
            close_time = day_schedule.get("close", "23:59")
            
            try:
                open_hour = int(open_time.split(":")[0])
                close_hour = int(close_time.split(":")[0])
                
                if open_hour <= current_hour <= close_hour:
                    filtered.append(result)
            except (ValueError, IndexError):
                # If time parsing fails, include the service
                filtered.append(result)
        
        return filtered if filtered else results  # Return original if no availability matches
    
    def _apply_custom_filter(self, results: List[Dict[str, Any]], filter_key: str, filter_value: Any) -> List[Dict[str, Any]]:
        """Apply custom filter to results."""
        if filter_key == "category":
            return [r for r in results if r.get("category", "").lower() == filter_value.lower()]
        elif filter_key == "price_range":
            return self._filter_by_price_range(results, filter_value)
        elif filter_key == "rating":
            return [r for r in results if r.get("rating", 0) >= filter_value]
        else:
            return results
    
    def _filter_by_price_range(self, results: List[Dict[str, Any]], price_range: Dict[str, float]) -> List[Dict[str, Any]]:
        """Filter results by price range."""
        min_price = price_range.get("min", 0)
        max_price = price_range.get("max", float('inf'))
        
        filtered = []
        for result in results:
            pricing_info = result.get("pricing_info", {})
            if isinstance(pricing_info, dict):
                base_price = pricing_info.get("base_price", 0)
                if min_price <= base_price <= max_price:
                    filtered.append(result)
        
        return filtered
    
    async def _rank_results(
        self, 
        results: List[Dict[str, Any]], 
        original_query: str, 
        context: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Rank search results based on multiple factors."""
        if not results:
            return results
        
        try:
            # Calculate composite scores
            for result in results:
                score = 0.0
                
                # 1. Semantic similarity (40% weight)
                similarity_score = result.get("similarity_score", 0.0)
                score += similarity_score * 0.4
                
                # 2. Service rating (20% weight)
                rating = result.get("rating", 0.0) / 5.0  # Normalize to 0-1
                score += rating * 0.2
                
                # 3. Availability (20% weight)
                availability_score = 1.0 if result.get("is_available", True) else 0.5
                score += availability_score * 0.2
                
                # 4. Location proximity (10% weight)
                location_score = self._calculate_location_score(result, context)
                score += location_score * 0.1
                
                # 5. User preference match (10% weight)
                preference_score = self._calculate_preference_score(result, context)
                score += preference_score * 0.1
                
                result["composite_score"] = score
            
            # Sort by composite score
            ranked_results = sorted(results, key=lambda x: x.get("composite_score", 0), reverse=True)
            
            return ranked_results
            
        except Exception as e:
            logger.error(f"Error ranking results: {str(e)}")
            return results
    
    def _calculate_location_score(self, result: Dict[str, Any], context: Dict[str, Any] = None) -> float:
        """Calculate location proximity score."""
        if not context or not context.get("user_info", {}).get("location"):
            return 0.5  # Neutral score if no location context
        
        user_location = context["user_info"]["location"]
        service_location = result.get("location_info", {})
        
        # Simple location scoring (can be enhanced with actual distance calculation)
        user_city = user_location.get("city", "").lower()
        service_cities = service_location.get("cities", [])
        
        if isinstance(service_cities, list):
            service_cities_lower = [city.lower() for city in service_cities]
            if user_city in service_cities_lower:
                return 1.0
            elif "global" in service_cities_lower:
                return 0.7
        
        return 0.3
    
    def _calculate_preference_score(self, result: Dict[str, Any], context: Dict[str, Any] = None) -> float:
        """Calculate user preference match score."""
        if not context or not context.get("user_info", {}).get("preferences"):
            return 0.5  # Neutral score if no preferences
        
        preferences = context["user_info"]["preferences"]
        service_category = result.get("category", "")
        
        # Check if service category matches user preferences
        preferred_categories = preferences.get("service_categories", [])
        if service_category in preferred_categories:
            return 1.0
        
        # Check budget preferences
        budget_preference = preferences.get("budget", "")
        service_pricing = result.get("pricing_info", {})
        
        if budget_preference and service_pricing:
            # Simple budget matching (can be enhanced)
            if budget_preference == "budget" and service_pricing.get("price_level") == "low":
                return 0.8
            elif budget_preference == "premium" and service_pricing.get("price_level") == "high":
                return 0.8
        
        return 0.5


# Global semantic search engine instance
semantic_search_engine = SemanticSearchEngine()
