"""
User Assistant
Handles user-facing interactions, intent recognition, and service discovery
"""
import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session

from .base_assistant import BaseAssistant
from ...dost.events.models import DOSTEvent, EventType
from ...ai.nlp.intent_recognition import Intent<PERSON><PERSON><PERSON>nizer
from ...dss.search.semantic_search import SemanticSearchEngine
from ...core.database import UserProfile, Entity

logger = logging.getLogger(__name__)


class UserAssistant(BaseAssistant):
    """
    AI Assistant specialized for user interactions.
    Handles intent recognition, service discovery, and conversation management.
    """
    
    def __init__(self):
        super().__init__(
            entity_id="ai.happidost.user_assistant",
            assistant_type="user_assistant",
            name="Dost User Assistant"
        )
        
        self.intent_recognizer = IntentRecognizer()
        self.search_engine = SemanticSearchEngine()
        self.capabilities = [
            "intent_recognition",
            "service_discovery", 
            "conversation_management",
            "user_support",
            "recommendation_engine"
        ]
    
    async def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get system prompt for user assistant."""
        base_prompt = """
You are <PERSON><PERSON>, an AI assistant for the HappiDost platform. You help users discover and interact with services through natural conversation.

Your capabilities:
1. Understand user intents and needs
2. Find relevant services from the marketplace
3. Facilitate connections between users and service providers
4. Provide helpful information and guidance
5. Manage multi-step service interactions

Guidelines:
- Be friendly, helpful, and conversational
- Ask clarifying questions when user intent is unclear
- Provide specific service recommendations with explanations
- Help users understand service options and pricing
- Facilitate smooth handoffs to service providers
- Maintain context throughout conversations

Response Format:
Always respond in JSON format with these fields:
{
  "response": "Your helpful response to the user",
  "extracted_keywords": ["keyword1", "keyword2"],
  "intent": "discovered_intent",
  "confidence": 0.85,
  "suggested_actions": ["action1", "action2"],
  "service_recommendations": [{"service_id": "id", "reason": "why recommended"}],
  "needs_clarification": false,
  "handoff_to_service": null
}
"""
        
        # Add user context if available
        if context.get("user_info"):
            user_info = context["user_info"]
            base_prompt += f"\n\nUSER CONTEXT:\n"
            base_prompt += f"Name: {user_info.get('name', 'Unknown')}\n"
            base_prompt += f"Location: {user_info.get('location', {})}\n"
            base_prompt += f"Preferences: {user_info.get('preferences', {})}\n"
        
        return base_prompt
    
    async def process_event(self, event: DOSTEvent, db: Session) -> Optional[DOSTEvent]:
        """Process user event and generate appropriate response."""
        try:
            logger.info(f"UserAssistant processing event {event.event_id}")
            
            # 1. Load context
            context = await self._load_user_context(event, db)
            
            # 2. Recognize intent
            user_message = self._extract_user_message(event)
            if not user_message:
                return await self._create_error_response(event, "No message content found")
            
            intent_result = await self.intent_recognizer.recognize_intent(user_message, context)
            
            # 3. Handle based on intent
            if intent_result["intent"] == "service_discovery":
                return await self._handle_service_discovery(event, intent_result, context, db)
            elif intent_result["intent"] == "conversation":
                return await self._handle_conversation(event, intent_result, context, db)
            elif intent_result["intent"] == "help":
                return await self._handle_help_request(event, intent_result, context, db)
            else:
                return await self._handle_general_query(event, intent_result, context, db)
                
        except Exception as e:
            logger.error(f"Error processing user event: {str(e)}")
            return await self.handle_error(e, event)
    
    async def _load_user_context(self, event: DOSTEvent, db: Session) -> Dict[str, Any]:
        """Load user context from database."""
        try:
            # Get user entity
            user_entity = db.query(Entity).filter(
                Entity.entity_id == event.source_entity_id
            ).first()
            
            if not user_entity:
                return {}
            
            # Get user profile
            user_profile = db.query(UserProfile).filter(
                UserProfile.entity_id == event.source_entity_id
            ).first()
            
            context = {
                "user_info": {
                    "entity_id": user_entity.entity_id,
                    "name": user_entity.name,
                    "location": user_entity.location or {},
                    "preferences": user_profile.preferences if user_profile else {},
                    "interaction_history": user_profile.interaction_history if user_profile else []
                },
                "session_info": {
                    "session_id": event.session_id,
                    "timestamp": event.timestamp.isoformat()
                }
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error loading user context: {str(e)}")
            return {}
    
    async def _handle_service_discovery(
        self, 
        event: DOSTEvent, 
        intent_result: Dict[str, Any], 
        context: Dict[str, Any], 
        db: Session
    ) -> DOSTEvent:
        """Handle service discovery requests."""
        try:
            user_message = self._extract_user_message(event)
            
            # Search for services
            search_results = await self.search_engine.search_services(
                query=user_message,
                context=context,
                limit=5
            )
            
            # Generate response with service recommendations
            response_data = {
                "response": self._format_service_recommendations(search_results),
                "extracted_keywords": intent_result.get("keywords", []),
                "intent": "service_discovery",
                "confidence": intent_result.get("confidence", 0.8),
                "suggested_actions": ["select_service", "refine_search", "get_more_info"],
                "service_recommendations": [
                    {
                        "service_id": str(service.get("service_id", "")),
                        "name": service.get("name", ""),
                        "description": service.get("description", ""),
                        "reason": f"Matches your request with {service.get('similarity_score', 0):.0%} confidence"
                    }
                    for service in search_results
                ],
                "needs_clarification": len(search_results) == 0,
                "handoff_to_service": None
            }
            
            return await self.create_response_event(event, response_data)
            
        except Exception as e:
            logger.error(f"Error handling service discovery: {str(e)}")
            return await self.handle_error(e, event)
    
    async def _handle_conversation(
        self, 
        event: DOSTEvent, 
        intent_result: Dict[str, Any], 
        context: Dict[str, Any], 
        db: Session
    ) -> DOSTEvent:
        """Handle general conversation."""
        try:
            # Generate conversational response
            response_data = await self.generate_response(event, context, db)
            
            # Add user assistant specific fields
            response_data.update({
                "intent": "conversation",
                "suggested_actions": ["continue_conversation", "ask_for_help"],
                "service_recommendations": [],
                "needs_clarification": False,
                "handoff_to_service": None
            })
            
            return await self.create_response_event(event, response_data)
            
        except Exception as e:
            logger.error(f"Error handling conversation: {str(e)}")
            return await self.handle_error(e, event)
    
    async def _handle_help_request(
        self, 
        event: DOSTEvent, 
        intent_result: Dict[str, Any], 
        context: Dict[str, Any], 
        db: Session
    ) -> DOSTEvent:
        """Handle help requests."""
        help_response = {
            "response": """I'm Dost, your AI assistant! I can help you:

• Find services like food delivery, home repairs, tutoring, and more
• Connect you with service providers in your area
• Answer questions about available services
• Help you book and manage service requests

Just tell me what you need, like "I need pizza delivery" or "Find a plumber near me" and I'll help you find the right service!""",
            "extracted_keywords": ["help", "assistance", "guide"],
            "intent": "help",
            "confidence": 1.0,
            "suggested_actions": ["ask_for_service", "explore_categories"],
            "service_recommendations": [],
            "needs_clarification": False,
            "handoff_to_service": None
        }
        
        return await self.create_response_event(event, help_response)
    
    async def _handle_general_query(
        self, 
        event: DOSTEvent, 
        intent_result: Dict[str, Any], 
        context: Dict[str, Any], 
        db: Session
    ) -> DOSTEvent:
        """Handle general queries using LLM."""
        try:
            response_data = await self.generate_response(event, context, db)
            
            # Add user assistant specific fields
            response_data.update({
                "intent": intent_result.get("intent", "general"),
                "suggested_actions": ["continue_conversation", "search_services"],
                "service_recommendations": [],
                "needs_clarification": False,
                "handoff_to_service": None
            })
            
            return await self.create_response_event(event, response_data)
            
        except Exception as e:
            logger.error(f"Error handling general query: {str(e)}")
            return await self.handle_error(e, event)
    
    def _format_service_recommendations(self, search_results: List[Dict[str, Any]]) -> str:
        """Format service recommendations for user."""
        if not search_results:
            return "I couldn't find any services matching your request. Could you provide more details about what you're looking for?"
        
        response = "I found these services that might help you:\n\n"
        
        for i, service in enumerate(search_results[:3], 1):
            name = service.get("name", "Unknown Service")
            description = service.get("description", "")
            score = service.get("similarity_score", 0)
            
            response += f"{i}. **{name}**\n"
            if description:
                response += f"   {description[:100]}{'...' if len(description) > 100 else ''}\n"
            response += f"   Match confidence: {score:.0%}\n\n"
        
        response += "Would you like more information about any of these services, or should I search for something else?"
        
        return response
    
    async def _create_error_response(self, event: DOSTEvent, error_message: str) -> DOSTEvent:
        """Create error response event."""
        error_data = {
            "response": f"I apologize, but {error_message}. Please try again.",
            "extracted_keywords": ["error"],
            "intent": "error",
            "confidence": 0.0,
            "suggested_actions": ["retry", "contact_support"],
            "service_recommendations": [],
            "needs_clarification": True,
            "handoff_to_service": None
        }
        
        return await self.create_response_event(event, error_data)


# Global user assistant instance
user_assistant = UserAssistant()
