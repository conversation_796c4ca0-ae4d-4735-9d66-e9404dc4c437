"""
DOST Objects API Router
Handles DOST Object operations and interactions
"""
import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...dost.objects.manager import dost_object_manager
from ...dost.objects.models import DOSTObject, ObjectType
from ...api.v1.auth import verify_token

logger = logging.getLogger(__name__)

# Create router
objects_router = APIRouter()


@objects_router.post("/create")
async def create_dost_object(
    description: str,
    additional_data: Optional[Dict[str, Any]] = None,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Create a new DOST Object from natural language description."""
    try:
        dost_object = await dost_object_manager.create_object(
            entity_id=entity_id,
            description=description,
            additional_data=additional_data or {},
            db=db
        )
        
        return {
            "success": True,
            "message": "DOST Object created successfully",
            "object_id": dost_object.metadata.id,
            "object": dost_object.to_dict()
        }
        
    except Exception as e:
        logger.error(f"Error creating DOST Object: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.get("/{object_id}")
async def get_dost_object(
    object_id: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get a specific DOST Object by ID."""
    try:
        dost_object = await dost_object_manager.get_object(object_id, db)
        
        if not dost_object:
            raise HTTPException(status_code=404, detail="DOST Object not found")
        
        return {
            "success": True,
            "object": dost_object.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting DOST Object: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.put("/{object_id}")
async def update_dost_object(
    object_id: str,
    updates: Dict[str, Any],
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Update a DOST Object."""
    try:
        # Verify ownership
        dost_object = await dost_object_manager.get_object(object_id, db)
        if not dost_object:
            raise HTTPException(status_code=404, detail="DOST Object not found")
        
        if dost_object.metadata.entity_id != entity_id:
            raise HTTPException(status_code=403, detail="Not authorized to update this object")
        
        # Update object
        updated_object = await dost_object_manager.update_object(object_id, updates, db)
        
        return {
            "success": True,
            "message": "DOST Object updated successfully",
            "object": updated_object.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating DOST Object: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.delete("/{object_id}")
async def delete_dost_object(
    object_id: str,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Delete a DOST Object."""
    try:
        # Verify ownership
        dost_object = await dost_object_manager.get_object(object_id, db)
        if not dost_object:
            raise HTTPException(status_code=404, detail="DOST Object not found")
        
        if dost_object.metadata.entity_id != entity_id:
            raise HTTPException(status_code=403, detail="Not authorized to delete this object")
        
        # Delete object
        success = await dost_object_manager.delete_object(object_id, db)
        
        if success:
            return {
                "success": True,
                "message": "DOST Object deleted successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to delete object")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting DOST Object: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.get("/search")
async def search_dost_objects(
    query: str = Query(..., description="Search query"),
    object_type: Optional[str] = Query(None, description="Filter by object type"),
    category: Optional[str] = Query(None, description="Filter by category"),
    limit: int = Query(10, ge=1, le=50, description="Maximum results"),
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Search DOST Objects using semantic search."""
    try:
        # Build filters
        filters = {}
        if object_type:
            filters["object_type"] = object_type
        if category:
            filters["category"] = category
        
        # Search objects
        objects = await dost_object_manager.search_objects(
            query=query,
            filters=filters,
            limit=limit,
            db=db
        )
        
        # Convert to response format
        results = []
        for obj in objects:
            results.append({
                "object_id": obj.metadata.id,
                "title": obj.identity.title,
                "description": obj.identity.short_description or obj.identity.description[:150] + "...",
                "category": obj.identity.category,
                "object_type": obj.metadata.object_type.value,
                "primary_action": obj.get_primary_action().dict() if obj.get_primary_action() else None,
                "base_price": obj.get_base_price(),
                "currency": obj.financials.currency,
                "provider": obj.relationships.provider.name if obj.relationships.provider else None
            })
        
        return {
            "success": True,
            "query": query,
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error searching DOST Objects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.get("/my-objects")
async def get_my_objects(
    object_type: Optional[str] = Query(None, description="Filter by object type"),
    limit: int = Query(50, ge=1, le=100, description="Maximum results"),
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get all DOST Objects owned by the authenticated entity."""
    try:
        objects = await dost_object_manager.get_objects_by_entity(entity_id, db, limit)
        
        # Filter by object type if specified
        if object_type:
            objects = [obj for obj in objects if obj.metadata.object_type.value == object_type]
        
        # Convert to response format
        results = []
        for obj in objects:
            results.append({
                "object_id": obj.metadata.id,
                "title": obj.identity.title,
                "description": obj.identity.short_description or obj.identity.description[:150] + "...",
                "category": obj.identity.category,
                "object_type": obj.metadata.object_type.value,
                "status": obj.metadata.status.value,
                "created_at": obj.metadata.created_at.isoformat(),
                "updated_at": obj.metadata.updated_at.isoformat()
            })
        
        return {
            "success": True,
            "objects": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error getting user objects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.post("/{object_id}/actions/{action_verb}")
async def execute_object_action(
    object_id: str,
    action_verb: str,
    parameters: Optional[Dict[str, Any]] = None,
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Execute an action on a DOST Object."""
    try:
        # Get object
        dost_object = await dost_object_manager.get_object(object_id, db)
        if not dost_object:
            raise HTTPException(status_code=404, detail="DOST Object not found")
        
        # Find action
        action = None
        for obj_action in dost_object.actions:
            if obj_action.verb == action_verb:
                action = obj_action
                break
        
        if not action:
            raise HTTPException(status_code=404, detail=f"Action '{action_verb}' not found")
        
        # TODO: Execute the actual action based on the endpoint and method
        # This would involve calling the appropriate service or API
        
        return {
            "success": True,
            "message": f"Action '{action_verb}' executed successfully",
            "action": action.dict(),
            "parameters": parameters or {}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing action: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@objects_router.get("/{object_id}/similar")
async def get_similar_objects(
    object_id: str,
    limit: int = Query(5, ge=1, le=20, description="Maximum results"),
    entity_id: str = Depends(verify_token),
    db: Session = Depends(get_db)
):
    """Get objects similar to the specified DOST Object."""
    try:
        # Get the reference object
        dost_object = await dost_object_manager.get_object(object_id, db)
        if not dost_object:
            raise HTTPException(status_code=404, detail="DOST Object not found")
        
        # Search for similar objects using the object's description
        search_query = f"{dost_object.identity.title} {dost_object.identity.description}"
        similar_objects = await dost_object_manager.search_objects(
            query=search_query,
            filters={"category": dost_object.identity.category},
            limit=limit + 1,  # +1 to exclude the original object
            db=db
        )
        
        # Remove the original object from results
        similar_objects = [obj for obj in similar_objects if obj.metadata.id != object_id][:limit]
        
        # Convert to response format
        results = []
        for obj in similar_objects:
            results.append({
                "object_id": obj.metadata.id,
                "title": obj.identity.title,
                "description": obj.identity.short_description or obj.identity.description[:150] + "...",
                "category": obj.identity.category,
                "similarity_reason": "Similar category and description",
                "base_price": obj.get_base_price(),
                "currency": obj.financials.currency
            })
        
        return {
            "success": True,
            "reference_object_id": object_id,
            "similar_objects": results,
            "count": len(results)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting similar objects: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
