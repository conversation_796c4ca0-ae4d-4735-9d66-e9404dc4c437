import React, { useEffect } from 'react';
import {
  Box,
  Container,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useState } from 'react';

import { useService } from '../../contexts/ServiceContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const MyServicesPage: React.FC = () => {
  const navigate = useNavigate();
  const { getMyServices, services, loading, deleteService } = useService();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(null);

  useEffect(() => {
    getMyServices();
  }, []);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, serviceId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedServiceId(serviceId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedServiceId(null);
  };

  const handleDelete = async () => {
    if (selectedServiceId) {
      await deleteService(selectedServiceId);
      handleMenuClose();
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <LoadingSpinner size={50} message="Loading your services..." />
      </Box>
    );
  }

  return (
    <>
      <Helmet>
        <title>My Services - HappiDost</title>
        <meta name="description" content="Manage your services on HappiDost. Edit, update, and track performance." />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              My Services
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/register-service')}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              }}
            >
              Add New Service
            </Button>
          </Box>

          {/* Services Grid */}
          {services.length > 0 ? (
            <Grid container spacing={3}>
              {services.map((service, index) => (
                <Grid item xs={12} sm={6} md={4} key={service.service_id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        position: 'relative',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      <CardContent>
                        {/* Menu Button */}
                        <IconButton
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                          onClick={(e) => handleMenuOpen(e, service.service_id)}
                        >
                          <MoreVertIcon />
                        </IconButton>

                        {/* Service Info */}
                        <Typography variant="h6" sx={{ mb: 1, fontWeight: 600, pr: 4 }}>
                          {service.title}
                        </Typography>
                        
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            mb: 2,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                          }}
                        >
                          {service.description}
                        </Typography>

                        {/* Status and Category */}
                        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                          <Chip
                            label={service.is_active ? 'Active' : 'Inactive'}
                            color={service.is_active ? 'success' : 'default'}
                            size="small"
                          />
                          <Chip
                            label={service.category}
                            variant="outlined"
                            size="small"
                          />
                          {service.is_featured && (
                            <Chip
                              label="Featured"
                              color="primary"
                              size="small"
                            />
                          )}
                        </Box>

                        {/* Pricing */}
                        <Typography variant="h6" color="primary.main" sx={{ mb: 2, fontWeight: 600 }}>
                          ₹{service.price_min}
                          {service.price_max > service.price_min && `-₹${service.price_max}`}
                          <Typography component="span" variant="body2" color="text.secondary">
                            /{service.pricing_model}
                          </Typography>
                        </Typography>

                        {/* Stats */}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            {service.total_bookings} bookings
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ⭐ {service.rating} ({service.total_reviews})
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          ) : (
            /* Empty State */
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                No services yet
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Start by registering your first service to attract customers
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigate('/register-service')}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                }}
              >
                Register Your First Service
              </Button>
            </Box>
          )}

          {/* Context Menu */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem
              onClick={() => {
                navigate(`/service/${selectedServiceId}`);
                handleMenuClose();
              }}
            >
              <ViewIcon sx={{ mr: 1 }} />
              View Details
            </MenuItem>
            <MenuItem
              onClick={() => {
                // Navigate to edit page (would be implemented)
                handleMenuClose();
              }}
            >
              <EditIcon sx={{ mr: 1 }} />
              Edit Service
            </MenuItem>
            <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
              <DeleteIcon sx={{ mr: 1 }} />
              Delete Service
            </MenuItem>
          </Menu>
        </motion.div>
      </Container>
    </>
  );
};

export default MyServicesPage;
