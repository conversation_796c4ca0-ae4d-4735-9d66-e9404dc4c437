// User types
export interface User {
  user_id: string;
  email: string;
  full_name: string;
  role: 'user' | 'service_provider' | 'business' | 'admin';
  phone_number?: string;
  profile_image_url?: string;
  location?: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  preferences?: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    privacy: {
      show_phone: boolean;
      show_email: boolean;
      show_location: boolean;
    };
  };
  verification_status: {
    email_verified: boolean;
    phone_verified: boolean;
    identity_verified: boolean;
    business_verified: boolean;
  };
  reputation_score: number;
  total_transactions: number;
  member_since: string;
  last_active: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  full_name: string;
  role: 'user' | 'service_provider' | 'business';
  phone_number?: string;
  location?: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
  };
  terms_accepted: boolean;
  marketing_consent?: boolean;
}

export interface AuthResponse {
  user: User;
  token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
}

export interface PasswordChangeData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface PasswordResetData {
  token: string;
  new_password: string;
  confirm_password: string;
}

export interface ProfileUpdateData {
  full_name?: string;
  phone_number?: string;
  profile_image_url?: string;
  location?: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  preferences?: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    privacy: {
      show_phone: boolean;
      show_email: boolean;
      show_location: boolean;
    };
  };
}

// JWT Token payload
export interface TokenPayload {
  sub: string; // user_id
  email: string;
  role: string;
  exp: number;
  iat: number;
}

// Auth error types
export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

// Social auth types
export interface SocialAuthData {
  provider: 'google' | 'facebook' | 'apple';
  token: string;
  user_info?: {
    email: string;
    full_name: string;
    profile_image_url?: string;
  };
}

// Two-factor authentication
export interface TwoFactorSetupData {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

export interface TwoFactorVerifyData {
  code: string;
  backup_code?: string;
}

// Session types
export interface UserSession {
  session_id: string;
  user_id: string;
  device_info: {
    device_type: string;
    browser: string;
    os: string;
    ip_address: string;
    location?: string;
  };
  created_at: string;
  last_activity: string;
  is_current: boolean;
}

// Permission types
export interface Permission {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface Role {
  name: string;
  display_name: string;
  description: string;
  permissions: Permission[];
}

// Account verification
export interface VerificationRequest {
  type: 'email' | 'phone' | 'identity' | 'business';
  data?: any;
}

export interface VerificationResponse {
  verification_id: string;
  status: 'pending' | 'approved' | 'rejected';
  message: string;
  required_documents?: string[];
}

// Account settings
export interface AccountSettings {
  security: {
    two_factor_enabled: boolean;
    login_notifications: boolean;
    suspicious_activity_alerts: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'private' | 'contacts_only';
    search_visibility: boolean;
    activity_status: boolean;
  };
  notifications: {
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
    marketing_emails: boolean;
  };
  data: {
    data_export_requested: boolean;
    data_deletion_requested: boolean;
    analytics_consent: boolean;
  };
}
