#!/usr/bin/env python3
"""
Check foreign key constraints for services table
"""
import psycopg2
from psycopg2.extras import RealDictCursor

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

def main():
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        print('🔍 Checking services table foreign key constraints:')
        print('=' * 60)
        
        # Check foreign key constraints for services table
        cursor.execute("""
            SELECT 
                tc.constraint_name, 
                tc.table_name, 
                kcu.column_name, 
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
              AND tc.table_name='services'
              AND kcu.column_name='provider_id';
        """)
        
        fk_info = cursor.fetchall()
        if fk_info:
            for fk in fk_info:
                print(f'Foreign Key: {fk["constraint_name"]}')
                print(f'  Column: {fk["column_name"]} -> {fk["foreign_table_name"]}.{fk["foreign_column_name"]}')
        else:
            print('No foreign key constraints found for provider_id')
        
        print()
        print('🔍 Checking what tables exist:')
        print('=' * 30)
        
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('profiles', 'user_profiles')")
        tables = cursor.fetchall()
        for table in tables:
            print(f'  - {table["table_name"]}')
        
        print()
        print('🔍 Checking profiles table structure:')
        print('=' * 40)
        
        cursor.execute("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'profiles' ORDER BY ordinal_position")
        profiles_columns = cursor.fetchall()
        if profiles_columns:
            print('Profiles table columns:')
            for col in profiles_columns:
                print(f'  - {col["column_name"]}: {col["data_type"]}')
        else:
            print('Profiles table does not exist or has no columns')
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    main()
