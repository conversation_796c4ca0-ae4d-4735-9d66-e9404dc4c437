{"name": "happidost-frontend", "version": "1.0.0", "description": "HappiDost Platform Frontend - AI-powered service discovery and registration", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.156", "@mui/material": "^5.15.0", "@mui/x-date-pickers": "^6.18.3", "@supabase/supabase-js": "^2.57.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.202", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-webcam": "^7.2.0", "recharts": "^2.8.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0"}, "proxy": "http://localhost:8000"}