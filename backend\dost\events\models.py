"""
DOST Event System Models
Based on the DOST specification from the project documentation
"""
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class EventType(str, Enum):
    """DOST Event types."""
    MESSAGE = "message"
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    INTRODUCTION = "introduction"
    REGISTRATION = "registration"
    DISCOVERY = "discovery"
    TRANSACTION = "transaction"


class EntityType(str, Enum):
    """Entity types in DOST ecosystem."""
    HUMAN = "human"
    BUSINESS = "business"
    AI_ASSISTANT = "ai_assistant"


class DOSTEventMessage(BaseModel):
    """DOST Event Message content."""
    text: Optional[Dict[str, str]] = None  # {"chat": "message", "language": "en"}
    audio: Optional[Dict[str, str]] = None  # {"url": "audio_url", "format": "mp3", "duration": 30}
    video: Optional[Dict[str, str]] = None  # {"url": "video_url", "format": "mp4", "duration": 60}
    image: Optional[Dict[str, str]] = None  # {"url": "image_url", "format": "jpg", "alt_text": "description"}
    structured_data: Optional[Dict[str, Any]] = None  # For API responses, forms, etc.


class DOSTEventTag(BaseModel):
    """DOST Event Tag for categorization and discovery."""
    tag: str = Field(..., description="Tag name")
    confidence: float = Field(ge=0.0, le=1.0, description="Confidence score")
    source_entity_id: str = Field(..., description="Entity that assigned this tag")
    metadata: Optional[Dict[str, Any]] = None
    
    @validator('tag')
    def validate_tag(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Tag cannot be empty')
        return v.strip().lower()


class DOSTEvent(BaseModel):
    """
    Core DOST Event model following the specification.
    All communications in the DOST ecosystem use this format.
    """
    # Core identifiers
    semantic_version: str = Field(default="1.0.0", description="DOST spec version")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event creation time")
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique event identifier")
    session_id: str = Field(..., description="Session this event belongs to")
    
    # Entity information
    source_entity_id: str = Field(..., description="Sender entity ID in rDNS format")
    target_entity_id: str = Field(..., description="Receiver entity ID in rDNS format")
    source_entity_endpoint: Optional[str] = None
    target_entity_endpoint: Optional[str] = None
    
    # Event metadata
    is_ai_generated: bool = Field(default=False, description="Whether AI generated this event")
    event_type: EventType = Field(..., description="Type of event")
    priority: int = Field(default=5, ge=1, le=10, description="Event priority (1=highest, 10=lowest)")
    requires_response: bool = Field(default=True, description="Whether this event expects a response")
    expiry_timestamp: Optional[datetime] = None
    
    # Content
    dost_event_message: DOSTEventMessage = Field(..., description="Event message content")
    dost_event_tag: List[DOSTEventTag] = Field(default=[], description="Event tags")
    
    # Additional metadata
    context: Optional[Dict[str, Any]] = None  # Additional context
    correlation_id: Optional[str] = None  # For tracking related events
    parent_event_id: Optional[str] = None  # For event chains
    
    @validator('source_entity_id', 'target_entity_id')
    def validate_entity_id(cls, v):
        """Validate entity ID follows rDNS format."""
        if not v:
            raise ValueError('Entity ID cannot be empty')
        
        # Basic rDNS validation
        parts = v.split('.')
        if len(parts) < 2:
            raise ValueError('Entity ID must follow rDNS format (e.g., com.example.service)')
        
        return v
    
    @validator('session_id', 'event_id')
    def validate_uuid(cls, v):
        """Validate UUID format."""
        try:
            uuid.UUID(v)
            return v
        except ValueError:
            raise ValueError('Must be a valid UUID')


class ServiceRegistrationEvent(DOSTEvent):
    """Specialized event for service registration."""
    event_type: EventType = EventType.REGISTRATION
    
    def __init__(self, **data):
        # Ensure message contains service registration data
        if 'dost_event_message' not in data:
            data['dost_event_message'] = DOSTEventMessage()
        
        if not data['dost_event_message'].structured_data:
            data['dost_event_message'].structured_data = {}
        
        super().__init__(**data)


class ServiceDiscoveryEvent(DOSTEvent):
    """Specialized event for service discovery requests."""
    event_type: EventType = EventType.DISCOVERY
    
    def __init__(self, **data):
        # Add discovery-specific context
        if 'context' not in data:
            data['context'] = {}
        
        data['context']['discovery_type'] = 'service_search'
        super().__init__(**data)


class ConversationEvent(DOSTEvent):
    """Specialized event for conversation messages."""
    event_type: EventType = EventType.MESSAGE
    
    def __init__(self, **data):
        # Ensure text message is present
        if 'dost_event_message' not in data:
            data['dost_event_message'] = DOSTEventMessage()
        
        super().__init__(**data)


# Event factory functions
def create_text_event(
    session_id: str,
    source_entity_id: str,
    target_entity_id: str,
    text_content: str,
    language: str = "en",
    event_type: EventType = EventType.MESSAGE,
    **kwargs
) -> DOSTEvent:
    """Create a text-based DOST event."""
    return DOSTEvent(
        session_id=session_id,
        source_entity_id=source_entity_id,
        target_entity_id=target_entity_id,
        event_type=event_type,
        dost_event_message=DOSTEventMessage(
            text={"chat": text_content, "language": language}
        ),
        **kwargs
    )


def create_service_registration_event(
    session_id: str,
    source_entity_id: str,
    service_description: str,
    service_data: Dict[str, Any],
    **kwargs
) -> ServiceRegistrationEvent:
    """Create a service registration event."""
    return ServiceRegistrationEvent(
        session_id=session_id,
        source_entity_id=source_entity_id,
        target_entity_id="ai.happidost.service_assistant",
        dost_event_message=DOSTEventMessage(
            text={"chat": service_description, "language": "en"},
            structured_data=service_data
        ),
        dost_event_tag=[
            DOSTEventTag(
                tag="service_registration",
                confidence=1.0,
                source_entity_id=source_entity_id
            )
        ],
        **kwargs
    )


def create_discovery_event(
    session_id: str,
    source_entity_id: str,
    search_query: str,
    search_context: Dict[str, Any],
    **kwargs
) -> ServiceDiscoveryEvent:
    """Create a service discovery event."""
    return ServiceDiscoveryEvent(
        session_id=session_id,
        source_entity_id=source_entity_id,
        target_entity_id="ai.happidost.discovery_assistant",
        dost_event_message=DOSTEventMessage(
            text={"chat": search_query, "language": "en"},
            structured_data=search_context
        ),
        dost_event_tag=[
            DOSTEventTag(
                tag="service_discovery",
                confidence=1.0,
                source_entity_id=source_entity_id
            )
        ],
        context=search_context,
        **kwargs
    )


# Response models for API
class EventResponse(BaseModel):
    """Response model for event operations."""
    success: bool
    event_id: str
    message: str
    data: Optional[Dict[str, Any]] = None


class SessionResponse(BaseModel):
    """Response model for session operations."""
    success: bool
    session_id: str
    message: str
    participants: List[str]
    expires_at: datetime
