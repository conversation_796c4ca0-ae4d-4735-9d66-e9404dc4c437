#!/usr/bin/env python3
"""
Test what tables exist in Supabase after backend startup
"""
from supabase import create_client

SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def main():
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Check what tables exist now
        tables_to_check = ['entities', 'services', 'profiles', 'transactions', 'chats', 'ai_assistants', 'dost_sessions']
        
        print('🔍 Checking Supabase tables after backend startup:')
        print('=' * 50)
        
        existing_tables = []
        missing_tables = []
        
        for table in tables_to_check:
            try:
                result = supabase.table(table).select('*').limit(1).execute()
                print(f'✅ {table}: Table exists, {len(result.data)} records')
                existing_tables.append(table)
            except Exception as e:
                if 'does not exist' in str(e) or 'relation' in str(e):
                    print(f'❌ {table}: Table does not exist')
                    missing_tables.append(table)
                else:
                    print(f'⚠️  {table}: Error - {str(e)[:50]}...')
        
        print()
        print('📊 SUMMARY:')
        print('=' * 30)
        print(f'✅ Existing tables: {existing_tables}')
        print(f'❌ Missing tables: {missing_tables}')
        
        if 'entities' in existing_tables:
            print()
            print('🎉 SUCCESS: Backend is now using Supabase!')
            print('   - All login/register endpoints will use Supabase')
            print('   - New user registrations will go to Supabase')
            print('   - Service registrations will use Supabase')
            
            # Check if we have any data
            try:
                result = supabase.table('entities').select('*').execute()
                if result.data:
                    print(f'   - Found {len(result.data)} entities in Supabase')
                else:
                    print('   - Supabase database is empty (ready for new data)')
            except:
                pass
        else:
            print()
            print('⚠️  Backend created some tables but not all expected ones')
            print('   This might be due to the foreign key constraint error during startup')
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    main()
