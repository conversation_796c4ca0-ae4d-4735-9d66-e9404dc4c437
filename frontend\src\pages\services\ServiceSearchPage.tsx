import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  InputAdornment,
  Chip,
  Avatar,
  Rating,
} from '@mui/material';
import {
  Search as SearchIcon,
  LocationOn as LocationIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

import { useService } from '../../contexts/ServiceContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const ServiceSearchPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { searchServices, searchResults, searchLoading } = useService();
  
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [location, setLocation] = useState(searchParams.get('location') || '');

  useEffect(() => {
    // Perform initial search if there are URL parameters
    const initialQuery = searchParams.get('q');
    const initialLocation = searchParams.get('location');
    const initialCategory = searchParams.get('category');
    
    if (initialQuery || initialLocation || initialCategory) {
      handleSearch();
    }
  }, []);

  const handleSearch = async () => {
    const searchData = {
      query: query || undefined,
      location: location ? { lat: 0, lon: 0 } : undefined, // This would be geocoded in real implementation
      category: searchParams.get('category') || undefined,
      limit: 20,
    };
    
    await searchServices(searchData);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <>
      <Helmet>
        <title>Search Services - HappiDost</title>
        <meta name="description" content="Find and book trusted service providers on HappiDost. Search by location, category, and more." />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Search Header */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h3"
              sx={{
                textAlign: 'center',
                mb: 3,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Find Your Perfect Service
            </Typography>
            
            {/* Search Bar */}
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                fullWidth
                placeholder="What service do you need?"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <TextField
                placeholder="Location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyPress={handleKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LocationIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 200 }}
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                disabled={searchLoading}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  px: 4,
                }}
              >
                Search
              </Button>
            </Box>
          </Box>

          {/* Loading State */}
          {searchLoading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <LoadingSpinner size={50} message="Searching services..." />
            </Box>
          )}

          {/* Search Results */}
          {!searchLoading && searchResults.length > 0 && (
            <Box>
              <Typography variant="h5" sx={{ mb: 3 }}>
                Found {searchResults.length} services
              </Typography>
              
              <Grid container spacing={3}>
                {searchResults.map((service) => (
                  <Grid item xs={12} sm={6} md={4} key={service.service_id}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Card
                        sx={{
                          height: '100%',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                        onClick={() => navigate(`/service/${service.service_id}`)}
                      >
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                            <Avatar
                              src={service.provider?.profile_image_url}
                              sx={{ mr: 2 }}
                            >
                              {service.provider?.full_name?.charAt(0)}
                            </Avatar>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="h6" sx={{ mb: 0.5, fontWeight: 600 }}>
                                {service.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                by {service.provider?.full_name}
                              </Typography>
                            </Box>
                            {service.is_verified && (
                              <Chip
                                label="Verified"
                                size="small"
                                color="success"
                                sx={{ ml: 1 }}
                              />
                            )}
                          </Box>

                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              mb: 2,
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                            }}
                          >
                            {service.description}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Rating value={service.rating} readOnly size="small" />
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {service.rating} ({service.total_reviews} reviews)
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="h6" color="primary.main" sx={{ fontWeight: 600 }}>
                              ₹{service.price_min}
                              {service.price_max > service.price_min && `-₹${service.price_max}`}
                              <Typography component="span" variant="body2" color="text.secondary">
                                /{service.pricing_model}
                              </Typography>
                            </Typography>
                            <Chip
                              label={service.category}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* No Results */}
          {!searchLoading && searchResults.length === 0 && query && (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                No services found
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Try adjusting your search terms or location
              </Typography>
              <Button
                variant="outlined"
                onClick={() => {
                  setQuery('');
                  setLocation('');
                }}
              >
                Clear Search
              </Button>
            </Box>
          )}

          {/* Empty State */}
          {!searchLoading && searchResults.length === 0 && !query && (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h5" sx={{ mb: 2 }}>
                Start your search
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Enter a service or location to find what you need
              </Typography>
            </Box>
          )}
        </motion.div>
      </Container>
    </>
  );
};

export default ServiceSearchPage;
