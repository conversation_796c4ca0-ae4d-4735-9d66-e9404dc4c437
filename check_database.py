#!/usr/bin/env python3
"""
Check which database the backend is actually using
"""
import sqlite3
import os
import sys

# Add backend to path
sys.path.append('backend')

def check_sqlite_database():
    """Check SQLite database"""
    db_path = './happidost.db'
    if os.path.exists(db_path):
        print(f'✅ SQLite database exists: {db_path}')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f'📋 Tables in SQLite database: {[table[0] for table in tables]}')
        
        # Check entities table
        try:
            cursor.execute('SELECT COUNT(*) FROM entities')
            entity_count = cursor.fetchone()[0]
            print(f'👥 Entities in SQLite: {entity_count}')
            
            if entity_count > 0:
                cursor.execute('SELECT entity_id, name, entity_type FROM entities LIMIT 3')
                entities = cursor.fetchall()
                print('📝 Sample entities:')
                for entity in entities:
                    print(f'   - {entity[0]} ({entity[2]}): {entity[1]}')
        except Exception as e:
            print(f'❌ Error reading entities: {e}')
        
        conn.close()
        return True
    else:
        print('❌ SQLite database not found')
        return False

def check_backend_config():
    """Check backend configuration"""
    try:
        from backend.core.config import settings
        print('\n🔍 Current Backend Configuration:')
        print(f'   DATABASE_URL: {settings.database_url}')
        print(f'   SUPABASE_URL: {settings.supabase_url}')
        
        if 'sqlite' in settings.database_url.lower():
            print('⚠️  Backend is configured to use SQLite')
            return 'sqlite'
        elif 'postgresql' in settings.database_url.lower():
            print('✅ Backend is configured to use PostgreSQL/Supabase')
            return 'postgresql'
        else:
            print('❓ Unknown database type')
            return 'unknown'
    except Exception as e:
        print(f'❌ Error reading backend config: {e}')
        return 'error'

def test_supabase_connection():
    """Test Supabase connection"""
    try:
        from supabase import create_client
        
        SUPABASE_URL = "https://aerrspknmocqsohbjkze.supabase.co"
        SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk"
        
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Test profiles table
        result = supabase.table("profiles").select("*").limit(1).execute()
        print(f'\n✅ Supabase connection working!')
        print(f'📊 Profiles in Supabase: {len(result.data)} records')
        
        # Test services table
        result = supabase.table("services").select("*").limit(1).execute()
        print(f'📊 Services in Supabase: {len(result.data)} records')
        
        return True
    except Exception as e:
        print(f'\n❌ Supabase connection failed: {e}')
        return False

def main():
    """Main check function"""
    print("🔍 Database Usage Check for HappiDost Backend\n")
    
    # Check SQLite
    sqlite_exists = check_sqlite_database()
    
    # Check backend config
    db_type = check_backend_config()
    
    # Test Supabase
    supabase_working = test_supabase_connection()
    
    print("\n" + "="*60)
    print("📊 SUMMARY:")
    print("="*60)
    
    if db_type == 'sqlite':
        print("❌ ISSUE: Backend is using SQLite, not Supabase!")
        print("   - All login/register endpoints are using local SQLite database")
        print("   - Data is stored locally, not in Supabase")
        print("   - Need to switch DATABASE_URL to use Supabase")
    elif db_type == 'postgresql':
        print("✅ Backend is configured for Supabase!")
        print("   - All endpoints should be using Supabase database")
    
    if sqlite_exists:
        print("⚠️  Local SQLite database exists with data")
        print("   - Consider migrating this data to Supabase")
    
    if supabase_working:
        print("✅ Supabase connection is working")
        print("   - Tables are accessible")
    else:
        print("❌ Supabase connection issues")
    
    print("\n🚀 NEXT STEPS:")
    if db_type == 'sqlite':
        print("1. Update .env: DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres")
        print("2. Restart backend server")
        print("3. Test login/register endpoints")
        print("4. Migrate existing SQLite data if needed")
    else:
        print("✅ Configuration looks good!")

if __name__ == "__main__":
    main()
