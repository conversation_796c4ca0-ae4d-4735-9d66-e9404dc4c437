"""
Authentication API Router
Handles user authentication and entity registration
"""
import logging
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError, jwt
from passlib.context import CryptContext

from ...core.database import get_db
from ...core.config import settings
from ...dost.entities.models import EntityRegistration, HumanRegistration, BusinessRegistration, EntityType, ContactInfo, LocationInfo
from ...dost.entities.registry import entity_registry
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# Create router
auth_router = APIRouter()

# Security
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# Pydantic models for frontend compatibility
class RegisterRequest(BaseModel):
    email: str
    password: str
    full_name: str
    role: str  # 'user', 'service_provider', 'business'
    phone_number: Optional[str] = None
    location: Optional[dict] = None
    terms_accepted: bool
    marketing_consent: Optional[bool] = False


class LoginRequest(BaseModel):
    email: str
    password: str
    remember_me: Optional[bool] = False


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token."""
    try:
        payload = jwt.decode(credentials.credentials, settings.secret_key, algorithms=[settings.algorithm])
        entity_id: str = payload.get("sub")
        if entity_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return entity_id
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


@auth_router.post("/register")
async def register(request: RegisterRequest, db: Session = Depends(get_db)):
    """General registration endpoint that routes to appropriate entity type."""
    try:
        # Parse full name to get first and last name
        name_parts = request.full_name.strip().split()
        first_name = name_parts[0] if name_parts else "user"
        last_name = name_parts[-1] if len(name_parts) > 1 else "unknown"

        # Create appropriate registration object based on role
        if request.role in ['user', 'service_provider']:
            # Generate proper entity ID for human
            entity_id = entity_registry.generate_entity_id(
                EntityType.HUMAN,
                first_name=first_name,
                last_name=last_name
            )

            # Create HumanRegistration
            contact_info = ContactInfo(
                email=request.email,
                phone=request.phone_number
            )

            location_info = LocationInfo() if not request.location else LocationInfo(**request.location)

            registration = HumanRegistration(
                entity_id=entity_id,
                name=request.full_name,
                contact_info=contact_info,
                location=location_info,
                additional_data={
                    "email": request.email,
                    "password": request.password,
                    "role": request.role,
                    "terms_accepted": request.terms_accepted,
                    "marketing_consent": request.marketing_consent
                }
            )
        elif request.role == 'business':
            # Generate proper entity ID for business
            entity_id = entity_registry.generate_entity_id(
                EntityType.BUSINESS,
                brand=first_name.lower(),
                service="general",
                location="global"
            )

            # Create BusinessRegistration
            contact_info = ContactInfo(
                email=request.email,
                phone=request.phone_number
            )

            location_info = LocationInfo() if not request.location else LocationInfo(**request.location)

            registration = BusinessRegistration(
                entity_id=entity_id,
                name=request.full_name,
                contact_info=contact_info,
                location=location_info,
                additional_data={
                    "email": request.email,
                    "password": request.password,
                    "role": request.role,
                    "terms_accepted": request.terms_accepted,
                    "marketing_consent": request.marketing_consent
                }
            )
        else:
            raise HTTPException(status_code=400, detail="Invalid role specified")

        # Register the entity
        result = await entity_registry.register_entity(registration, db)

        if result["success"]:
            # Create access token
            access_token = create_access_token(data={"sub": entity_id})

            return {
                "success": True,
                "message": f"{request.role.title()} registered successfully",
                "user": {
                    "user_id": entity_id,
                    "email": request.email,
                    "full_name": request.full_name,
                    "role": request.role,
                    "phone_number": request.phone_number,
                    "verification_status": {
                        "email_verified": False,
                        "phone_verified": False,
                        "identity_verified": False,
                        "business_verified": False
                    },
                    "reputation_score": 0,
                    "total_transactions": 0,
                    "member_since": datetime.utcnow().isoformat(),
                    "last_active": datetime.utcnow().isoformat(),
                    "is_active": True,
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat()
                },
                "token": access_token,
                "token_type": "bearer",
                "expires_in": settings.access_token_expire_minutes * 60
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@auth_router.post("/register/human")
async def register_human(registration: HumanRegistration, db: Session = Depends(get_db)):
    """Register a human entity."""
    try:
        result = await entity_registry.register_entity(registration, db)
        
        if result["success"]:
            # Create access token
            access_token = create_access_token(data={"sub": registration.entity_id})
            
            return {
                "success": True,
                "message": "Human entity registered successfully",
                "entity_id": registration.entity_id,
                "access_token": access_token,
                "token_type": "bearer"
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except Exception as e:
        logger.error(f"Error registering human: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@auth_router.post("/register/business")
async def register_business(registration: BusinessRegistration, db: Session = Depends(get_db)):
    """Register a business entity."""
    try:
        result = await entity_registry.register_entity(registration, db)
        
        if result["success"]:
            # Create access token
            access_token = create_access_token(data={"sub": registration.entity_id})
            
            return {
                "success": True,
                "message": "Business entity registered successfully",
                "entity_id": registration.entity_id,
                "access_token": access_token,
                "token_type": "bearer"
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])
            
    except Exception as e:
        logger.error(f"Error registering business: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@auth_router.post("/login")
async def login(
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """Login entity and get access token (OAuth2 compatible)."""
    try:
        # Find entity by email first (username is email in our case)
        entity = await entity_registry.get_entity_by_email(username, db)

        if not entity:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        # Authenticate entity
        credentials = {"password": password}
        is_authenticated = await entity_registry.authenticate_entity(entity.entity_id, credentials, db)

        if not is_authenticated:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        # Create access token
        access_token = create_access_token(data={"sub": entity.entity_id})

        return {
            "success": True,
            "user": {
                "user_id": entity.entity_id,
                "email": username,
                "full_name": entity.name,
                "role": getattr(entity, 'role', 'user'),
                "verification_status": {
                    "email_verified": entity.verification_status == 'verified',
                    "phone_verified": False,
                    "identity_verified": False,
                    "business_verified": False
                },
                "reputation_score": 0,
                "total_transactions": 0,
                "member_since": entity.created_at.isoformat(),
                "last_active": datetime.utcnow().isoformat(),
                "is_active": entity.is_active,
                "created_at": entity.created_at.isoformat(),
                "updated_at": entity.updated_at.isoformat() if entity.updated_at else entity.created_at.isoformat()
            },
            "token": access_token,
            "token_type": "bearer",
            "expires_in": settings.access_token_expire_minutes * 60
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during login: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@auth_router.get("/me")
async def get_current_entity(entity_id: str = Depends(verify_token), db: Session = Depends(get_db)):
    """Get current authenticated entity information."""
    try:
        entity = await entity_registry.get_entity(entity_id, db)
        
        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        return {
            "entity_id": entity.entity_id,
            "entity_type": entity.entity_type,
            "name": entity.name,
            "description": entity.description,
            "verification_status": entity.verification_status,
            "is_active": entity.is_active,
            "created_at": entity.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current entity: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@auth_router.post("/refresh")
async def refresh_token(entity_id: str = Depends(verify_token)):
    """Refresh access token."""
    try:
        # Create new access token
        access_token = create_access_token(data={"sub": entity_id})
        
        return {
            "success": True,
            "access_token": access_token,
            "token_type": "bearer"
        }
        
    except Exception as e:
        logger.error(f"Error refreshing token: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
