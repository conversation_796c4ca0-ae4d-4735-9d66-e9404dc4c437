#!/usr/bin/env python3
"""
Check Supabase table schemas
"""
from supabase import create_client

# Configuration
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def check_table_schema():
    """Check the actual schema of tables in Supabase"""
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        print("🔍 Checking Supabase table schemas...")
        
        # Check services table
        print("\n📊 SERVICES TABLE:")
        try:
            services = supabase.table('services').select('*').limit(1).execute()
            if services.data:
                print("✅ Services table exists")
                print("📋 Sample record structure:")
                sample = services.data[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__} = {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
            else:
                print("⚠️ Services table is empty, checking with insert to see required fields...")
                # Try a minimal insert to see what fields are required
                try:
                    result = supabase.table('services').insert({'title': 'test'}).execute()
                    print("✅ Minimal insert worked")
                except Exception as e:
                    print(f"❌ Insert failed: {e}")
        except Exception as e:
            print(f"❌ Services table error: {e}")
        
        # Check entities table
        print("\n📊 ENTITIES TABLE:")
        try:
            entities = supabase.table('entities').select('*').limit(3).execute()
            if entities.data:
                print(f"✅ Entities table exists with {len(entities.data)} records")
                print("📋 Sample record structure:")
                sample = entities.data[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__} = {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
            else:
                print("⚠️ Entities table is empty")
        except Exception as e:
            print(f"❌ Entities table error: {e}")
        
        # Check profiles table
        print("\n📊 PROFILES TABLE:")
        try:
            profiles = supabase.table('profiles').select('*').limit(1).execute()
            if profiles.data:
                print(f"✅ Profiles table exists with {len(profiles.data)} records")
                print("📋 Sample record structure:")
                sample = profiles.data[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__} = {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
            else:
                print("⚠️ Profiles table is empty")
        except Exception as e:
            print(f"❌ Profiles table error: {e}")
        
        # Check transactions table
        print("\n📊 TRANSACTIONS TABLE:")
        try:
            transactions = supabase.table('transactions').select('*').limit(1).execute()
            if transactions.data:
                print(f"✅ Transactions table exists with {len(transactions.data)} records")
            else:
                print("⚠️ Transactions table is empty")
        except Exception as e:
            print(f"❌ Transactions table error: {e}")
        
        # Check ai_assistants table
        print("\n📊 AI_ASSISTANTS TABLE:")
        try:
            ai_assistants = supabase.table('ai_assistants').select('*').limit(1).execute()
            if ai_assistants.data:
                print(f"✅ AI assistants table exists with {len(ai_assistants.data)} records")
                print("📋 Sample record structure:")
                sample = ai_assistants.data[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__} = {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
            else:
                print("⚠️ AI assistants table is empty")
        except Exception as e:
            print(f"❌ AI assistants table error: {e}")
            
    except Exception as e:
        print(f"❌ Schema check error: {e}")

if __name__ == "__main__":
    check_table_schema()
