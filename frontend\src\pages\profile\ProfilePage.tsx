import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Avatar,
  Button,
  Grid,
  Chip,
  Divider,
} from '@mui/material';
import {
  Edit as EditIcon,
  Verified as VerifiedIcon,
  LocationOn as LocationIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet-async';

import { useAuth } from '../../hooks/useAuth';

const ProfilePage: React.FC = () => {
  const { user } = useAuth();

  if (!user) return null;

  return (
    <>
      <Helmet>
        <title>Profile - HappiDost</title>
        <meta name="description" content="Manage your HappiDost profile and account settings." />
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <Typography
            variant="h3"
            sx={{
              textAlign: 'center',
              mb: 4,
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            My Profile
          </Typography>

          <Grid container spacing={4}>
            {/* Profile Card */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent sx={{ textAlign: 'center', p: 4 }}>
                  <Avatar
                    src={user.profile_image_url}
                    sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
                  >
                    {user.full_name.charAt(0).toUpperCase()}
                  </Avatar>
                  
                  <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                    {user.full_name}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {user.role.replace('_', ' ').toUpperCase()}
                  </Typography>

                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>
                    {user.verification_status.email_verified && (
                      <Chip
                        icon={<VerifiedIcon />}
                        label="Email Verified"
                        color="success"
                        size="small"
                      />
                    )}
                    {user.verification_status.phone_verified && (
                      <Chip
                        icon={<VerifiedIcon />}
                        label="Phone Verified"
                        color="success"
                        size="small"
                      />
                    )}
                  </Box>

                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                    fullWidth
                    sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    }}
                  >
                    Edit Profile
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* Details */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Personal Information
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Email
                          </Typography>
                          <Typography variant="body1">
                            {user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {user.phone_number && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <PhoneIcon sx={{ mr: 2, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Phone
                            </Typography>
                            <Typography variant="body1">
                              {user.phone_number}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}

                    {user.location && (
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <LocationIcon sx={{ mr: 2, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Location
                            </Typography>
                            <Typography variant="body1">
                              {user.location.city}, {user.location.state}, {user.location.country}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Account Statistics
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary.main" sx={{ fontWeight: 'bold' }}>
                          {user.reputation_score}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Reputation Score
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary.main" sx={{ fontWeight: 'bold' }}>
                          {user.total_transactions}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Total Transactions
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary.main" sx={{ fontWeight: 'bold' }}>
                          {new Date(user.member_since).getFullYear()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Member Since
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary.main" sx={{ fontWeight: 'bold' }}>
                          {user.is_active ? '✓' : '✗'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Account Status
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Verification Status
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Chip
                        label="Email Verification"
                        color={user.verification_status.email_verified ? 'success' : 'default'}
                        icon={user.verification_status.email_verified ? <VerifiedIcon /> : undefined}
                        sx={{ width: '100%', justifyContent: 'flex-start' }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Chip
                        label="Phone Verification"
                        color={user.verification_status.phone_verified ? 'success' : 'default'}
                        icon={user.verification_status.phone_verified ? <VerifiedIcon /> : undefined}
                        sx={{ width: '100%', justifyContent: 'flex-start' }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Chip
                        label="Identity Verification"
                        color={user.verification_status.identity_verified ? 'success' : 'default'}
                        icon={user.verification_status.identity_verified ? <VerifiedIcon /> : undefined}
                        sx={{ width: '100%', justifyContent: 'flex-start' }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Chip
                        label="Business Verification"
                        color={user.verification_status.business_verified ? 'success' : 'default'}
                        icon={user.verification_status.business_verified ? <VerifiedIcon /> : undefined}
                        sx={{ width: '100%', justifyContent: 'flex-start' }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>
      </Container>
    </>
  );
};

export default ProfilePage;
