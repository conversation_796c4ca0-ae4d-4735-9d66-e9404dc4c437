# DOST Object Specification v1.0

## 🎯 Overview

The **DOST Object** is a universal, standardized representation of any entity, service, product, or resource within the HappiDost ecosystem. It serves as the foundational data structure that enables seamless AI-powered discovery, interaction, and transaction across all domains.

## 🏗️ Core Philosophy

Every "noun" in the real world can be represented as a DOST Object:
- **Services**: Pizza delivery, plumbing, tutoring, healthcare
- **Products**: Physical goods, digital content, subscriptions
- **Experiences**: Events, bookings, appointments, reservations
- **Resources**: Information, documents, media, tools

## 📋 DOST Object Schema

```json
{
  "dostObject": {
    "metadata": {
      "id": "uuid-v4-string",
      "version": "1.0.0",
      "createdAt": "ISO-8601-timestamp",
      "updatedAt": "ISO-8601-timestamp",
      "entityId": "rDNS-format-entity-id",
      "objectType": "service|product|experience|resource",
      "status": "active|inactive|pending|suspended"
    },

    "identity": {
      "title": "Human-readable name",
      "description": "Detailed description for AI and humans",
      "shortDescription": "Brief summary for listings",
      "keywords": ["searchable", "tags", "categories"],
      "category": "primary-category",
      "subcategory": "specific-subcategory",
      "aliases": ["alternative", "names", "synonyms"]
    },

    "multimodalAttributes": {
      "textual": {
        "specifications": {
          "key": "value",
          "features": ["list", "of", "features"],
          "requirements": ["prerequisites", "conditions"]
        },
        "reviews": {
          "summary": "AI-generated review summary",
          "sentiment": "positive|neutral|negative",
          "totalReviews": 0,
          "averageRating": 0.0,
          "highlights": ["key", "review", "points"]
        }
      },
      
      "visual": {
        "images": [
          {
            "url": "https://example.com/image.jpg",
            "type": "primary|gallery|thumbnail|icon",
            "altText": "Accessibility description",
            "metadata": {
              "width": 1920,
              "height": 1080,
              "format": "jpg|png|webp"
            }
          }
        ],
        "videos": [
          {
            "url": "https://example.com/video.mp4",
            "type": "demo|tutorial|review|promotional",
            "duration": 120,
            "thumbnail": "https://example.com/thumb.jpg",
            "metadata": {
              "resolution": "1080p",
              "format": "mp4|webm"
            }
          }
        ]
      },

      "audio": [
        {
          "url": "https://example.com/audio.mp3",
          "type": "description|review|demo|music",
          "duration": 60,
          "transcript": "Audio transcription for accessibility",
          "metadata": {
            "format": "mp3|wav|ogg",
            "quality": "high|medium|low"
          }
        }
      ],

      "ratings": {
        "overall": 4.5,
        "aspects": {
          "quality": 4.8,
          "value": 4.2,
          "service": 4.6,
          "reliability": 4.4
        },
        "distribution": {
          "5": 120,
          "4": 80,
          "3": 20,
          "2": 5,
          "1": 2
        }
      }
    },

    "availability": {
      "temporal": {
        "schedule": {
          "monday": {"open": "09:00", "close": "18:00", "closed": false},
          "tuesday": {"open": "09:00", "close": "18:00", "closed": false},
          "wednesday": {"open": "09:00", "close": "18:00", "closed": false},
          "thursday": {"open": "09:00", "close": "18:00", "closed": false},
          "friday": {"open": "09:00", "close": "18:00", "closed": false},
          "saturday": {"open": "10:00", "close": "16:00", "closed": false},
          "sunday": {"closed": true}
        },
        "timezone": "Asia/Kolkata",
        "exceptions": [
          {
            "date": "2024-12-25",
            "status": "closed",
            "reason": "Christmas Holiday"
          }
        ],
        "leadTime": {
          "minimum": "30m",
          "maximum": "7d",
          "typical": "2h"
        }
      },

      "geographical": {
        "serviceAreas": [
          {
            "type": "city|region|country|global",
            "name": "Bangalore",
            "coordinates": {
              "center": {"lat": 12.9716, "lng": 77.5946},
              "radius": 25,
              "unit": "km"
            },
            "specificAreas": ["Marathalli", "Whitefield", "Koramangala"]
          }
        ],
        "deliveryOptions": {
          "pickup": true,
          "delivery": true,
          "virtual": false,
          "onsite": true
        }
      },

      "capacity": {
        "current": 5,
        "maximum": 10,
        "unit": "concurrent_orders|seats|slots"
      }
    },

    "financials": {
      "currency": "INR",
      "taxInclusive": true,
      
      "ownership": {
        "purchase": {
          "basePrice": 1000,
          "instantPay": 1000,
          "cashOnDelivery": 1050,
          "installments": {
            "available": true,
            "plans": [
              {
                "duration": "3m",
                "monthlyAmount": 350,
                "totalAmount": 1050,
                "interestRate": 5.0
              }
            ]
          }
        },
        
        "rental": {
          "hourly": 50,
          "daily": 200,
          "weekly": 1200,
          "monthly": 4000,
          "yearly": 40000,
          "deposit": 500,
          "minimumPeriod": "1d"
        },

        "subscription": {
          "daily": 10,
          "weekly": 60,
          "monthly": 200,
          "quarterly": 540,
          "yearly": 2000,
          "freeTrial": {
            "duration": "7d",
            "available": true
          }
        }
      },

      "additionalCosts": {
        "delivery": 50,
        "setup": 100,
        "maintenance": 25,
        "cancellation": 0
      },

      "discounts": [
        {
          "type": "percentage|fixed|bogo",
          "value": 10,
          "condition": "first_time_user",
          "validUntil": "2024-12-31T23:59:59Z",
          "minimumOrder": 500
        }
      ]
    },

    "actions": [
      {
        "verb": "discover",
        "label": "View Details",
        "description": "Get comprehensive information about this object",
        "endpoint": "/api/v1/objects/{id}",
        "method": "GET",
        "authentication": "optional",
        "parameters": {},
        "response": "detailed_object_info"
      },
      {
        "verb": "inquire",
        "label": "Ask Questions",
        "description": "Get answers about this object from AI or provider",
        "endpoint": "/api/v1/objects/{id}/inquire",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "question": "string",
          "context": "object"
        },
        "response": "ai_generated_answer"
      },
      {
        "verb": "purchase",
        "label": "Buy Now",
        "description": "Purchase this object immediately",
        "endpoint": "/api/v1/transactions/purchase",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "objectId": "string",
          "quantity": "number",
          "paymentMethod": "string",
          "deliveryAddress": "object"
        },
        "response": "transaction_confirmation"
      },
      {
        "verb": "subscribe",
        "label": "Subscribe",
        "description": "Start a subscription for this object",
        "endpoint": "/api/v1/subscriptions/create",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "objectId": "string",
          "plan": "string",
          "startDate": "date"
        },
        "response": "subscription_details"
      },
      {
        "verb": "book",
        "label": "Book Appointment",
        "description": "Schedule a time slot for this service",
        "endpoint": "/api/v1/bookings/create",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "objectId": "string",
          "datetime": "ISO-8601",
          "duration": "string",
          "requirements": "object"
        },
        "response": "booking_confirmation"
      },
      {
        "verb": "addToCart",
        "label": "Add to Cart",
        "description": "Add this object to shopping cart",
        "endpoint": "/api/v1/cart/add",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "objectId": "string",
          "quantity": "number",
          "options": "object"
        },
        "response": "cart_updated"
      },
      {
        "verb": "compare",
        "label": "Compare",
        "description": "Compare this object with similar ones",
        "endpoint": "/api/v1/objects/compare",
        "method": "POST",
        "authentication": "optional",
        "parameters": {
          "objectIds": ["string"],
          "criteria": ["string"]
        },
        "response": "comparison_matrix"
      },
      {
        "verb": "share",
        "label": "Share",
        "description": "Share this object with others",
        "endpoint": "/api/v1/objects/{id}/share",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "platform": "string",
          "recipients": ["string"],
          "message": "string"
        },
        "response": "share_confirmation"
      },
      {
        "verb": "review",
        "label": "Write Review",
        "description": "Submit a review for this object",
        "endpoint": "/api/v1/objects/{id}/reviews",
        "method": "POST",
        "authentication": "required",
        "parameters": {
          "rating": "number",
          "comment": "string",
          "aspects": "object"
        },
        "response": "review_submitted"
      },
      {
        "verb": "track",
        "label": "Track Status",
        "description": "Monitor the status of your order/booking",
        "endpoint": "/api/v1/transactions/{id}/status",
        "method": "GET",
        "authentication": "required",
        "parameters": {
          "transactionId": "string"
        },
        "response": "status_information"
      }
    ],

    "relationships": {
      "provider": {
        "entityId": "com.restaurant.pizzahut.marathalli",
        "name": "Pizza Hut Marathalli",
        "type": "business",
        "verificationStatus": "verified",
        "contactInfo": {
          "phone": "+91-80-12345678",
          "email": "<EMAIL>",
          "website": "https://pizzahut.co.in"
        }
      },

      "alternatives": [
        {
          "objectId": "uuid-of-similar-object",
          "similarity": 0.85,
          "reason": "Similar service in same area"
        }
      ],

      "bundles": [
        {
          "objectId": "uuid-of-bundle",
          "discount": 15,
          "description": "Pizza + Drink combo"
        }
      ],

      "dependencies": [
        {
          "objectId": "uuid-of-required-object",
          "type": "required|recommended|optional",
          "description": "Delivery address required"
        }
      ]
    },

    "aiMetadata": {
      "embedding": {
        "vector": [0.1, 0.2, 0.3],
        "model": "text-embedding-3-small",
        "version": "1.0"
      },
      "searchTags": ["pizza", "food", "delivery", "italian", "fast"],
      "intentMapping": {
        "food_delivery": 0.95,
        "italian_cuisine": 0.88,
        "quick_meal": 0.92
      },
      "lastIndexed": "2024-01-15T10:30:00Z"
    },

    "compliance": {
      "dataPrivacy": {
        "gdprCompliant": true,
        "dataRetention": "2y",
        "consentRequired": ["marketing", "analytics"]
      },
      "accessibility": {
        "wcagLevel": "AA",
        "screenReaderFriendly": true,
        "alternativeFormats": ["audio", "braille"]
      },
      "legal": {
        "termsOfService": "https://example.com/terms",
        "privacyPolicy": "https://example.com/privacy",
        "licenses": ["MIT", "Creative Commons"]
      }
    }
  }
}
```

## 🎯 Key Design Principles

### 1. **Universal Representation**
Every entity in the HappiDost ecosystem can be represented as a DOST Object, ensuring consistency and interoperability.

### 2. **AI-First Design**
Built with AI consumption in mind - structured data, embeddings, and semantic tags enable intelligent discovery and interaction.

### 3. **Multimodal Support**
Supports text, images, videos, audio, and structured data to provide rich, comprehensive representations.

### 4. **Action-Oriented**
Every object defines what actions can be performed on it, enabling dynamic UI generation and API interactions.

### 5. **Relationship Aware**
Objects understand their relationships to other objects, enabling recommendations, bundles, and complex workflows.

## 🔧 Implementation Examples

### Pizza Delivery Service
```json
{
  "dostObject": {
    "metadata": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "entityId": "com.pizzahut.delivery.marathalli",
      "objectType": "service",
      "status": "active"
    },
    "identity": {
      "title": "Pizza Hut Delivery - Marathalli",
      "description": "Fast pizza delivery service in Marathalli area. Fresh, hot pizzas delivered within 30 minutes.",
      "category": "food_delivery",
      "subcategory": "pizza"
    },
    "financials": {
      "currency": "INR",
      "ownership": {
        "purchase": {
          "basePrice": 299,
          "instantPay": 299,
          "cashOnDelivery": 319
        }
      },
      "additionalCosts": {
        "delivery": 25
      }
    },
    "actions": [
      {
        "verb": "order",
        "label": "Order Now",
        "endpoint": "/api/v1/orders/create",
        "method": "POST"
      }
    ]
  }
}
```

### Plumbing Service
```json
{
  "dostObject": {
    "metadata": {
      "objectType": "service",
      "entityId": "com.quickfix.plumbing.bangalore"
    },
    "identity": {
      "title": "Emergency Plumbing Service",
      "category": "home_services",
      "subcategory": "plumbing"
    },
    "availability": {
      "temporal": {
        "schedule": {
          "24x7": true
        },
        "leadTime": {
          "minimum": "30m",
          "typical": "1h"
        }
      }
    },
    "financials": {
      "ownership": {
        "rental": {
          "hourly": 500,
          "minimumPeriod": "1h"
        }
      }
    },
    "actions": [
      {
        "verb": "book",
        "label": "Book Emergency Service",
        "endpoint": "/api/v1/bookings/emergency"
      }
    ]
  }
}
```

### Online Course
```json
{
  "dostObject": {
    "metadata": {
      "objectType": "product",
      "entityId": "edu.coursera.ai.fundamentals"
    },
    "identity": {
      "title": "AI Fundamentals Course",
      "category": "education",
      "subcategory": "technology"
    },
    "financials": {
      "ownership": {
        "subscription": {
          "monthly": 2999,
          "freeTrial": {
            "duration": "7d",
            "available": true
          }
        }
      }
    },
    "actions": [
      {
        "verb": "enroll",
        "label": "Start Free Trial",
        "endpoint": "/api/v1/courses/enroll"
      }
    ]
  }
}
```

## 🤖 AI Integration Points

### 1. **Semantic Search**
```python
# AI can understand: "I need fast food delivery"
# And match it to pizza delivery services based on:
- embedding similarity
- category matching (food_delivery)
- intent mapping (quick_meal: 0.92)
- availability (leadTime: "30m")
```

### 2. **Dynamic Conversations**
```python
# AI Assistant can say:
"I found Pizza Hut delivery in Marathalli. They deliver fresh pizzas in 30 minutes for ₹299. Would you like to order now or see the menu first?"

# Based on DOST Object data:
- title: "Pizza Hut Delivery - Marathalli"
- leadTime: "30m"
- basePrice: 299
- available actions: ["order", "viewMenu"]
```

### 3. **Smart Recommendations**
```python
# AI can suggest:
- alternatives (similar pizza places)
- bundles (pizza + drink combo)
- upsells (premium toppings)
- cross-sells (desserts, beverages)
```

## 🔄 Object Lifecycle

### 1. **Creation**
- Service provider registers via natural language
- AI extracts structured data into DOST Object
- Vector embedding generated for search
- Object stored in database and vector store

### 2. **Discovery**
- User query processed by AI
- Semantic search finds matching objects
- Results ranked by relevance and availability
- Objects presented with available actions

### 3. **Interaction**
- User selects action (book, buy, inquire)
- AI facilitates the interaction
- Transaction processed via defined endpoints
- Status updates tracked and communicated

### 4. **Evolution**
- Reviews and ratings update object reputation
- Usage patterns improve AI recommendations
- Provider updates modify object attributes
- Relationships evolve based on user behavior

## 🎪 Benefits for HappiDost Platform

### 1. **Unified Data Model**
- Single schema for all entities
- Consistent API interactions
- Simplified AI training and inference

### 2. **Dynamic UI Generation**
- Actions define available buttons/options
- Multimodal attributes enable rich displays
- Responsive to object capabilities

### 3. **Intelligent Matching**
- AI understands object semantics
- Context-aware recommendations
- Intent-based discovery

### 4. **Scalable Architecture**
- New object types easily added
- Extensible action framework
- Flexible relationship modeling

### 5. **Enhanced User Experience**
- Rich, multimedia presentations
- Contextual actions and recommendations
- Seamless cross-object workflows

## 🚀 Implementation Roadmap

### Phase 1: Core Object Model
- [ ] Implement basic DOST Object schema
- [ ] Create object validation and storage
- [ ] Build object CRUD APIs

### Phase 2: AI Integration
- [ ] Generate embeddings for semantic search
- [ ] Implement intent mapping
- [ ] Build recommendation engine

### Phase 3: Action Framework
- [ ] Implement dynamic action execution
- [ ] Create action parameter validation
- [ ] Build transaction workflows

### Phase 4: Advanced Features
- [ ] Relationship management
- [ ] Bundle and package handling
- [ ] Advanced analytics and insights

This DOST Object specification provides the foundation for a truly intelligent, AI-powered service ecosystem that can represent and interact with any entity in the real world! 🌟
