#!/usr/bin/env python3
"""
HappiDost AI Backend Startup Script
"""
import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_environment():
    """Check if environment is properly set up."""
    logger.info("Checking environment setup...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        logger.warning(".env file not found. Please copy .env.example to .env and configure it.")
        return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.warning("Virtual environment not activated. Please activate your virtual environment.")
        return False
    
    return True


def install_dependencies():
    """Install Python dependencies."""
    logger.info("Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        logger.info("Dependencies installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False


def setup_database():
    """Set up database (placeholder for now)."""
    logger.info("Database setup will be handled by the application on startup.")
    return True


def start_server():
    """Start the FastAPI server."""
    logger.info("Starting HappiDost AI Backend server...")
    
    try:
        # Change to backend directory
        os.chdir("backend")
        
        # Start server with uvicorn
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload",
            "--log-level", "info"
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start server: {e}")
        return False
    except KeyboardInterrupt:
        logger.info("Server stopped by user.")
        return True


def main():
    """Main startup function."""
    logger.info("🚀 Starting HappiDost AI Backend...")
    
    # Check environment
    if not check_environment():
        logger.error("Environment check failed. Please fix the issues and try again.")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        logger.error("Failed to install dependencies.")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        logger.error("Database setup failed.")
        sys.exit(1)
    
    # Start server
    logger.info("✅ Environment ready. Starting server...")
    start_server()


if __name__ == "__main__":
    main()
