"""
HappiDost Service Registration System
Handles natural language service descriptions and extracts structured data using LLM
"""

import json
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut
import openai
from supabase import create_client, Client
import httpx
import logging

from ..core.config import settings
from ..ai.models.llm_client import LLMClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ServiceLocation:
    """Service location information"""
    latitude: float
    longitude: float
    address: str
    city: str
    state: str
    country: str
    pincode: Optional[str] = None

@dataclass
class ServiceAvailability:
    """Service availability schedule"""
    day: str  # mon, tue, wed, thu, fri, sat, sun
    from_time: str  # HH:MM format
    to_time: str  # HH:MM format

@dataclass
class ExtractedServiceData:
    """Structured service data extracted from natural language"""
    title: str
    description: str
    category: str
    subcategory: Optional[str]
    keywords: List[str]
    price_min: Optional[float]
    price_max: Optional[float]
    currency: str
    pricing_model: str
    is_negotiable: bool
    service_type: str  # on_location, remote, hybrid, at_provider
    service_radius_km: Optional[int]
    location_text: Optional[str]
    availability: List[ServiceAvailability]
    timezone: str
    special_requirements: Optional[str]
    qualifications: List[str]
    experience_years: Optional[int]
    languages: List[str]

class ServiceRegistrationSystem:
    """Complete service registration system with LLM extraction and Vespa integration"""
    
    def __init__(self):
        self.supabase: Client = create_client(
            settings.supabase_url,
            settings.supabase_key
        )
        self.llm_client = LLMClient()
        self.geocoder = Nominatim(user_agent="happidost-service-registration")
        self.vespa_endpoint = settings.vespa_endpoint
        
    async def register_service(
        self,
        provider_id: str,
        natural_language_description: str,
        provider_location: Optional[str] = None,
        user_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Complete service registration pipeline:
        1. Extract structured data from natural language
        2. Geocode location information
        3. Generate embeddings
        4. Store in Supabase
        5. Index in Vespa
        """
        try:
            logger.info(f"Starting service registration for provider {provider_id}")
            
            # Step 1: Extract structured data using LLM
            extracted_data = await self._extract_service_data(natural_language_description)
            logger.info(f"Extracted service data: {extracted_data.title}")
            
            # Step 2: Geocode location
            location_info = None
            if extracted_data.location_text or provider_location:
                location_text = extracted_data.location_text or provider_location
                location_info = await self._geocode_location(location_text)
                logger.info(f"Geocoded location: {location_info.city if location_info else 'Not found'}")
            
            # Step 3: Generate embeddings
            embedding = await self._generate_embedding(extracted_data)
            logger.info(f"Generated embedding with {len(embedding)} dimensions")
            
            # Step 4: Create service record
            service_data = await self._create_service_record(
                provider_id, extracted_data, location_info, embedding
            )
            
            # Step 5: Store in Supabase
            supabase_result = await self._store_in_supabase(service_data, user_token)
            logger.info(f"Stored in Supabase with ID: {supabase_result['id']}")
            
            # Step 6: Index in Vespa
            vespa_result = await self._index_in_vespa(service_data)
            logger.info(f"Indexed in Vespa: {vespa_result['success']}")
            
            return {
                "success": True,
                "service_id": service_data["service_id"],
                "supabase_id": supabase_result["id"],
                "vespa_document_id": service_data["vespa_document_id"],
                "extracted_data": asdict(extracted_data),
                "location": asdict(location_info) if location_info else None
            }
            
        except Exception as e:
            logger.error(f"Service registration failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _extract_service_data(self, description: str) -> ExtractedServiceData:
        """Extract structured data from natural language description using LLM"""
        
        extraction_prompt = f"""
You are an expert service data extractor for the HappiDost platform. Extract structured information from the following service description.

Service Description:
{description}

Extract the following information and return as JSON:

{{
  "title": "Short, descriptive title for the service (max 100 chars)",
  "description": "Clean, professional description (200-500 words)",
  "category": "Main category (Education, Healthcare, Home Services, Beauty, Fitness, Technology, etc.)",
  "subcategory": "Specific subcategory (Tutoring, Cleaning, Repair, etc.)",
  "keywords": ["array", "of", "relevant", "search", "keywords"],
  "price_min": 0.0,
  "price_max": 0.0,
  "currency": "INR",
  "pricing_model": "hourly|fixed|daily|weekly|monthly|per_session|negotiable",
  "is_negotiable": true/false,
  "service_type": "on_location|remote|hybrid|at_provider",
  "service_radius_km": 0,
  "location_text": "Extracted location/area mentioned",
  "availability": [
    {{"day": "mon", "from_time": "09:00", "to_time": "17:00"}},
    {{"day": "tue", "from_time": "09:00", "to_time": "17:00"}}
  ],
  "timezone": "Asia/Kolkata",
  "special_requirements": "Any special requirements or conditions",
  "qualifications": ["list", "of", "qualifications", "or", "certifications"],
  "experience_years": 0,
  "languages": ["English", "Hindi", "etc"]
}}

Guidelines:
1. If pricing is not mentioned, set price_min and price_max to null
2. If location is not specific, extract general area/city
3. Convert time ranges to 24-hour format
4. Use standard day abbreviations: mon, tue, wed, thu, fri, sat, sun
5. Infer service_type based on context (tutoring = on_location, coding = remote, etc.)
6. Extract all relevant keywords for search optimization
7. If availability is not mentioned, leave array empty
8. Be conservative with experience_years - only extract if clearly mentioned
9. Standardize category names to common categories

Return only the JSON object, no additional text.
"""
        
        try:
            response = await self.llm_client.generate_response(
                messages=[{"role": "user", "content": extraction_prompt}],
                config={
                    "model": "gpt-4o",
                    "temperature": 0.1,
                    "max_tokens": 2000,
                    "response_format": {"type": "json_object"}
                }
            )
            
            # Parse JSON response
            extracted_json = json.loads(response.strip())
            
            # Convert to dataclass
            availability = [
                ServiceAvailability(**avail) for avail in extracted_json.get("availability", [])
            ]
            
            return ExtractedServiceData(
                title=extracted_json["title"],
                description=extracted_json["description"],
                category=extracted_json["category"],
                subcategory=extracted_json.get("subcategory"),
                keywords=extracted_json.get("keywords", []),
                price_min=extracted_json.get("price_min"),
                price_max=extracted_json.get("price_max"),
                currency=extracted_json.get("currency", "INR"),
                pricing_model=extracted_json.get("pricing_model", "negotiable"),
                is_negotiable=extracted_json.get("is_negotiable", True),
                service_type=extracted_json.get("service_type", "on_location"),
                service_radius_km=extracted_json.get("service_radius_km"),
                location_text=extracted_json.get("location_text"),
                availability=availability,
                timezone=extracted_json.get("timezone", "Asia/Kolkata"),
                special_requirements=extracted_json.get("special_requirements"),
                qualifications=extracted_json.get("qualifications", []),
                experience_years=extracted_json.get("experience_years"),
                languages=extracted_json.get("languages", ["English"])
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {e}")
            raise ValueError("LLM returned invalid JSON response")
        except Exception as e:
            logger.error(f"LLM extraction failed: {e}")
            raise ValueError(f"Failed to extract service data: {str(e)}")
    
    async def _geocode_location(self, location_text: str) -> Optional[ServiceLocation]:
        """Geocode location text to coordinates"""
        try:
            # Add India context if not present
            if "india" not in location_text.lower():
                location_text += ", India"
            
            location = self.geocoder.geocode(location_text, timeout=10)
            
            if location:
                # Extract address components
                address_parts = location.address.split(", ")
                
                return ServiceLocation(
                    latitude=location.latitude,
                    longitude=location.longitude,
                    address=location.address,
                    city=self._extract_city(address_parts),
                    state=self._extract_state(address_parts),
                    country="India",
                    pincode=self._extract_pincode(address_parts)
                )
            
            return None
            
        except GeocoderTimedOut:
            logger.warning(f"Geocoding timeout for: {location_text}")
            return None
        except Exception as e:
            logger.error(f"Geocoding failed for {location_text}: {e}")
            return None
    
    def _extract_city(self, address_parts: List[str]) -> str:
        """Extract city from address parts"""
        # Simple heuristic - usually the second or third part
        for part in address_parts:
            if any(keyword in part.lower() for keyword in ["bangalore", "mumbai", "delhi", "chennai", "hyderabad", "pune", "kolkata"]):
                return part.strip()
        return address_parts[1] if len(address_parts) > 1 else "Unknown"
    
    def _extract_state(self, address_parts: List[str]) -> str:
        """Extract state from address parts"""
        for part in address_parts:
            if any(keyword in part.lower() for keyword in ["karnataka", "maharashtra", "delhi", "tamil nadu", "telangana", "west bengal"]):
                return part.strip()
        return address_parts[-2] if len(address_parts) > 2 else "Unknown"
    
    def _extract_pincode(self, address_parts: List[str]) -> Optional[str]:
        """Extract pincode from address parts"""
        for part in address_parts:
            # Look for 6-digit numbers
            import re
            pincode_match = re.search(r'\b\d{6}\b', part)
            if pincode_match:
                return pincode_match.group()
        return None
    
    async def _generate_embedding(self, service_data: ExtractedServiceData) -> List[float]:
        """Generate embedding for semantic search"""
        # Combine relevant text for embedding
        embedding_text = f"""
        {service_data.title}
        {service_data.description}
        Category: {service_data.category}
        Subcategory: {service_data.subcategory or ''}
        Keywords: {', '.join(service_data.keywords)}
        Service Type: {service_data.service_type}
        Qualifications: {', '.join(service_data.qualifications)}
        Languages: {', '.join(service_data.languages)}
        """.strip()
        
        try:
            # Use the LLM client's embedding method instead of direct OpenAI call
            embedding = await self.llm_client.generate_embedding(embedding_text)
            return embedding
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            raise ValueError(f"Failed to generate embedding: {str(e)}")
    
    async def _create_service_record(
        self, 
        provider_id: str, 
        extracted_data: ExtractedServiceData,
        location_info: Optional[ServiceLocation],
        embedding: List[float]
    ) -> Dict[str, Any]:
        """Create complete service record for database storage"""
        
        service_id = f"svc_{uuid.uuid4().hex[:8]}"
        vespa_document_id = f"service::{service_id}"
        
        # Convert availability to JSON
        availability_json = [asdict(avail) for avail in extracted_data.availability]
        
        # Create location point for PostGIS
        location_point = None
        if location_info:
            location_point = f"POINT({location_info.longitude} {location_info.latitude})"
        
        return {
            "service_id": service_id,
            "vespa_document_id": vespa_document_id,
            "provider_id": provider_id,
            "title": extracted_data.title,
            "description": extracted_data.description,
            "category": extracted_data.category,
            "subcategory": extracted_data.subcategory,
            "keywords": extracted_data.keywords,
            "price_min": extracted_data.price_min,
            "price_max": extracted_data.price_max,
            "currency": extracted_data.currency,
            "pricing_model": extracted_data.pricing_model,
            "is_negotiable": extracted_data.is_negotiable,
            "location_point": location_point,
            "service_address": asdict(location_info) if location_info else None,
            "service_radius_km": extracted_data.service_radius_km,
            "service_type": extracted_data.service_type,
            "availability": availability_json,
            "timezone": extracted_data.timezone,
            "embedding_model": "text-embedding-3-small",
            "embedding_dims": 1536,
            "search_tags": extracted_data.keywords + [extracted_data.category.lower()],
            "status": "active",
            "metadata": {
                "qualifications": extracted_data.qualifications,
                "experience_years": extracted_data.experience_years,
                "languages": extracted_data.languages,
                "special_requirements": extracted_data.special_requirements,
                "extraction_timestamp": datetime.now(timezone.utc).isoformat()
            },
            "embedding": embedding
        }
    
    async def _store_in_supabase(self, service_data: Dict[str, Any], user_token: Optional[str] = None) -> Dict[str, Any]:
        """Store service in Supabase database"""
        try:
            # Use service key client to bypass RLS for service registration
            logger.info("Using Supabase service key client to bypass RLS")
            from supabase import create_client
            from ..core.config import settings

            supabase_client = create_client(
                settings.supabase_url,
                settings.supabase_service_key  # Service key bypasses RLS
            )
            # Prepare absolute minimal data for Supabase - only the most basic fields
            supabase_data = {
                "service_id": service_data["service_id"],
                "provider_id": service_data["provider_id"],
                "title": service_data["title"],
                "description": service_data["description"],
                "category": service_data["category"]
            }
            
            # Insert into services table using authenticated client
            result = supabase_client.table("services").insert(supabase_data).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise ValueError("No data returned from Supabase insert")
                
        except Exception as e:
            logger.error(f"Supabase storage failed: {e}")
            raise ValueError(f"Failed to store in database: {str(e)}")
    
    async def _index_in_vespa(self, service_data: Dict[str, Any]) -> Dict[str, Any]:
        """Index service in Vespa for search"""
        try:
            # Prepare Vespa document
            vespa_doc = {
                "fields": {
                    "service_id": service_data["service_id"],
                    "provider_id": service_data["provider_id"],
                    "vespa_document_id": service_data["vespa_document_id"],
                    "title": service_data["title"],
                    "description": service_data["description"],
                    "category": service_data["category"],
                    "subcategory": service_data.get("subcategory", ""),
                    "keywords": service_data["keywords"],
                    "price_min": service_data.get("price_min", 0.0),
                    "price_max": service_data.get("price_max", 0.0),
                    "currency": service_data["currency"],
                    "pricing_model": service_data["pricing_model"],
                    "is_negotiable": service_data["is_negotiable"],
                    "service_radius_km": service_data.get("service_radius_km", 0),
                    "service_type": service_data["service_type"],
                    "availability": json.dumps(service_data["availability"]),
                    "timezone": service_data["timezone"],
                    "verification_status": "pending",
                    "quality_score": 0.0,
                    "total_bookings": 0,
                    "total_reviews": 0,
                    "average_rating": 0.0,
                    "embedding": {"values": service_data["embedding"]},
                    "embedding_model": service_data["embedding_model"],
                    "embedding_dims": service_data["embedding_dims"],
                    "search_tags": service_data["search_tags"],
                    "status": service_data["status"],
                    "featured": False,
                    "created_at": int(datetime.now(timezone.utc).timestamp()),
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                    "provider_name": "Unknown",  # Will be updated with actual provider info
                    "provider_rating": 0.0,
                    "provider_verification_status": "unverified",
                    "metadata": json.dumps(service_data["metadata"])
                }
            }
            
            # Add location if available
            if service_data.get("service_address"):
                location_info = service_data["service_address"]
                vespa_doc["fields"]["location"] = {
                    "lat": location_info["latitude"],
                    "lng": location_info["longitude"]
                }
            
            # Send to Vespa
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.vespa_endpoint}/document/v1/services/services/docid/{service_data['vespa_document_id']}",
                    json=vespa_doc,
                    timeout=30.0
                )
                
                if response.status_code in [200, 201]:
                    return {"success": True, "vespa_response": response.json()}
                else:
                    raise ValueError(f"Vespa indexing failed: {response.status_code} - {response.text}")
                    
        except Exception as e:
            logger.error(f"Vespa indexing failed: {e}")
            return {"success": False, "error": str(e)}

# Global instance
service_registration_system = ServiceRegistrationSystem()
