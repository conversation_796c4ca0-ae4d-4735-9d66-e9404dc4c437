alter table public.services enable row level security;

-- SELECT: Everyone can view services (marketplace listings are public)
create policy "Anyone can view services"
on public.services
for select
using (true);

-- INSERT: Only logged-in providers can create services
create policy "Providers can create services"
on public.services
for insert
with check (
  provider_id = auth.uid()::text
);

-- UPDATE: Only provider can update their services
create policy "Providers can update their services"
on public.services
for update
using (
  provider_id = auth.uid()::text
)
with check (
  provider_id = auth.uid()::text
);

-- DELETE: Only provider can delete their services
create policy "Providers can delete their services"
on public.services
for delete
using (
  provider_id = auth.uid()::text
);



alter table public.services enable row level security;

-- SELECT: Everyone can view services (marketplace listings are public)
create policy "Anyone can view services"
on public.services
for select
using (true);

-- INSERT: Only logged-in providers can create services
create policy "Providers can create services"
on public.services
for insert
with check (
  provider_id = auth.uid()::text
);

-- UPDATE: Only provider can update their services
create policy "Providers can update their services"
on public.services
for update
using (
  provider_id = auth.uid()::text
)
with check (
  provider_id = auth.uid()::text
);

-- DELETE: Only provider can delete their services
create policy "Providers can delete their services"
on public.services
for delete
using (
  provider_id = auth.uid()::text
);



alter table public.services enable row level security;

-- SELECT: Everyone can view services (marketplace listings are public)
create policy "Anyone can view services"
on public.services
for select
using (true);

-- INSERT: Only logged-in providers can create services
create policy "Providers can create services"
on public.services
for insert
with check (
  provider_id = auth.uid()::text
);

-- UPDATE: Only provider can update their services
create policy "Providers can update their services"
on public.services
for update
using (
  provider_id = auth.uid()::text
)
with check (
  provider_id = auth.uid()::text
);

-- DELETE: Only provider can delete their services
create policy "Providers can delete their services"
on public.services
for delete
using (
  provider_id = auth.uid()::text
);


alter table public.services enable row level security;

-- SELECT: Everyone can view services (marketplace listings are public)
create policy "Anyone can view services"
on public.services
for select
using (true);

-- INSERT: Only logged-in providers can create services
create policy "Providers can create services"
on public.services
for insert
with check (
  provider_id = auth.uid()::text
);

-- UPDATE: Only provider can update their services
create policy "Providers can update their services"
on public.services
for update
using (
  provider_id = auth.uid()::text
)
with check (
  provider_id = auth.uid()::text
);

-- DELETE: Only provider can delete their services
create policy "Providers can delete their services"
on public.services
for delete
using (
  provider_id = auth.uid()::text
);


create table public.dost_sessions (
  session_id character varying not null,
  participants json not null,
  session_type character varying not null,
  context json null,
  status character varying null,
  created_at timestamp without time zone null,
  last_activity timestamp without time zone null,
  expires_at timestamp without time zone null,
  constraint dost_sessions_pkey primary key (session_id)
) TABLESPACE pg_default;


create table public.dost_sessions (
  session_id character varying not null,
  participants json not null,
  session_type character varying not null,
  context json null,
  status character varying null,
  created_at timestamp without time zone null,
  last_activity timestamp without time zone null,
  expires_at timestamp without time zone null,
  constraint dost_sessions_pkey primary key (session_id)
) TABLESPACE pg_default;


create table public.dost_sessions (
  session_id character varying not null,
  participants json not null,
  session_type character varying not null,
  context json null,
  status character varying null,
  created_at timestamp without time zone null,
  last_activity timestamp without time zone null,
  expires_at timestamp without time zone null,
  constraint dost_sessions_pkey primary key (session_id)
) TABLESPACE pg_default;


create table public.dost_sessions (
  session_id character varying not null,
  participants json not null,
  session_type character varying not null,
  context json null,
  status character varying null,
  created_at timestamp without time zone null,
  last_activity timestamp without time zone null,
  expires_at timestamp without time zone null,
  constraint dost_sessions_pkey primary key (session_id)
) TABLESPACE pg_default;


create table public.dost_sessions (
  session_id character varying not null,
  participants json not null,
  session_type character varying not null,
  context json null,
  status character varying null,
  created_at timestamp without time zone null,
  last_activity timestamp without time zone null,
  expires_at timestamp without time zone null,
  constraint dost_sessions_pkey primary key (session_id)
) TABLESPACE pg_default;


create table public.dost_sessions (
  session_id character varying not null,
  participants json not null,
  session_type character varying not null,
  context json null,
  status character varying null,
  created_at timestamp without time zone null,
  last_activity timestamp without time zone null,
  expires_at timestamp without time zone null,
  constraint dost_sessions_pkey primary key (session_id)
) TABLESPACE pg_default;