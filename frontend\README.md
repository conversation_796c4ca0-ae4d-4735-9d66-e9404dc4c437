# HappiDost Frontend

A modern React TypeScript frontend for the HappiDost AI-powered service discovery platform.

## 🚀 Features

### Core Features
- **User Authentication** - Login, registration, and profile management
- **Service Registration** - AI-powered natural language service registration
- **Service Discovery** - Intelligent search with filters and recommendations
- **Dashboard** - Comprehensive user and provider dashboards
- **Responsive Design** - Mobile-first responsive UI
- **Real-time Updates** - Live notifications and updates

### AI-Powered Features
- **Natural Language Service Registration** - Describe services in plain English
- **Voice Input** - Speech-to-text for service descriptions
- **Intelligent Search** - Semantic search with AI matching
- **Auto-categorization** - AI determines service categories
- **Smart Recommendations** - Personalized service suggestions

### User Experience
- **Modern UI/UX** - Material-UI with custom theming
- **Smooth Animations** - Framer Motion animations
- **Progressive Loading** - Skeleton screens and loading states
- **Error Handling** - Comprehensive error boundaries
- **Accessibility** - WCAG compliant components

## 🛠️ Tech Stack

### Core Technologies
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Material-UI v5** - Component library with custom theming
- **React Router v6** - Client-side routing
- **React Query** - Server state management and caching

### State Management
- **Context API** - Global state management
- **React Hook Form** - Form state and validation
- **Yup** - Schema validation

### UI/UX Libraries
- **Framer Motion** - Smooth animations and transitions
- **React Hot Toast** - Beautiful notifications
- **React Helmet Async** - SEO and meta tag management
- **React Speech Kit** - Voice input and text-to-speech

### Development Tools
- **ESLint** - Code linting
- **TypeScript** - Static type checking
- **React Scripts** - Build tooling

## 📁 Project Structure

```
frontend/
├── public/
│   ├── index.html          # HTML template
│   └── manifest.json       # PWA manifest
├── src/
│   ├── components/         # Reusable components
│   │   ├── auth/          # Authentication components
│   │   ├── common/        # Common UI components
│   │   └── layout/        # Layout components
│   ├── contexts/          # React contexts
│   │   ├── AuthContext.tsx
│   │   └── ServiceContext.tsx
│   ├── hooks/             # Custom hooks
│   │   └── useAuth.ts
│   ├── pages/             # Page components
│   │   ├── auth/          # Authentication pages
│   │   ├── dashboard/     # Dashboard pages
│   │   ├── profile/       # Profile pages
│   │   ├── services/      # Service-related pages
│   │   ├── HomePage.tsx
│   │   └── NotFoundPage.tsx
│   ├── services/          # API services
│   │   └── api.ts
│   ├── types/             # TypeScript type definitions
│   │   ├── auth.ts
│   │   └── service.ts
│   ├── App.tsx            # Main app component
│   ├── index.tsx          # App entry point
│   └── index.css          # Global styles
├── package.json
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ and npm/yarn
- Backend API running on `http://localhost:8000`

### Installation

1. **Install dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Environment Setup**
   Create a `.env` file in the frontend directory:
   ```env
   REACT_APP_API_URL=http://localhost:8000/api/v1
   REACT_APP_ENVIRONMENT=development
   ```

3. **Start Development Server**
   ```bash
   npm start
   ```
   
   The app will be available at `http://localhost:3000`

### Build for Production

```bash
npm run build
```

This creates an optimized production build in the `build/` folder.

## 🎨 Key Components

### Authentication System
- **LoginPage** - User login with email/password
- **RegisterPage** - Multi-step registration process
- **ProtectedRoute** - Route protection with role-based access
- **AuthContext** - Global authentication state management

### Service Registration
- **ServiceRegistrationPage** - AI-powered service registration
- **Natural Language Input** - Describe services in plain English
- **Voice Input** - Speech-to-text integration
- **Real-time Processing** - Live AI processing feedback

### Service Discovery
- **ServiceSearchPage** - Intelligent service search
- **ServiceDetailsPage** - Detailed service information
- **Advanced Filters** - Location, price, category filtering
- **Smart Recommendations** - AI-powered suggestions

### Dashboard & Management
- **DashboardPage** - User/provider dashboard
- **MyServicesPage** - Service provider management
- **ProfilePage** - User profile management
- **Analytics** - Performance metrics and insights

## 🔧 API Integration

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /auth/me` - Get current user
- `PUT /auth/profile` - Update profile

### Service Endpoints
- `POST /service-registration/register` - Register new service
- `GET /service-registration/my-services` - Get user's services
- `GET /service-registration/service/{id}` - Get service details
- `POST /service-registration/search` - Search services
- `PUT /service-registration/service/{id}` - Update service
- `DELETE /service-registration/service/{id}` - Delete service

## 🎯 Key Features Implementation

### AI-Powered Service Registration
```typescript
// Natural language processing for service registration
const registerService = async (data: ServiceRegistrationData) => {
  const response = await serviceAPI.registerService({
    natural_language_description: data.description,
    provider_location: data.location,
    additional_context: data.context
  });
  
  // AI extracts structured data automatically
  return response.data;
};
```

### Voice Input Integration
```typescript
// Speech-to-text for service descriptions
const { listen, listening, stop } = useSpeechRecognition({
  onResult: (result: string) => {
    setValue('description', getValues('description') + ' ' + result);
  },
});
```

### Intelligent Search
```typescript
// Hybrid search with multiple parameters
const searchServices = async (params: ServiceSearchParams) => {
  const response = await serviceAPI.searchServices({
    query: params.query,
    location: params.location,
    category: params.category,
    price_range: params.priceRange,
    sort_by: 'relevance'
  });
  
  return response.results;
};
```

## 🎨 Theming & Styling

### Material-UI Theme
- **Primary Colors** - Gradient from #667eea to #764ba2
- **Typography** - Inter and Poppins fonts
- **Components** - Custom styled Material-UI components
- **Responsive Design** - Mobile-first breakpoints

### Animation System
- **Framer Motion** - Smooth page transitions
- **Loading States** - Skeleton screens and spinners
- **Micro-interactions** - Hover effects and button animations

## 📱 Responsive Design

### Breakpoints
- **Mobile** - 0-768px
- **Tablet** - 769-1024px
- **Desktop** - 1025px+

### Mobile Features
- **Touch-friendly** - Large tap targets
- **Swipe gestures** - Mobile navigation
- **Responsive grids** - Adaptive layouts
- **Mobile menu** - Collapsible navigation

## 🔒 Security Features

### Authentication
- **JWT Tokens** - Secure authentication
- **Protected Routes** - Role-based access control
- **Auto-logout** - Session timeout handling
- **CSRF Protection** - Request validation

### Data Validation
- **Form Validation** - Client-side validation with Yup
- **Type Safety** - TypeScript type checking
- **Input Sanitization** - XSS protection
- **Error Boundaries** - Graceful error handling

## 🚀 Performance Optimizations

### Code Splitting
- **Route-based splitting** - Lazy loading of pages
- **Component splitting** - Dynamic imports
- **Bundle optimization** - Tree shaking

### Caching Strategy
- **React Query** - Server state caching
- **Service Worker** - Offline functionality
- **Image optimization** - Lazy loading and compression

## 🧪 Testing

### Test Setup
```bash
npm test
```

### Testing Strategy
- **Unit Tests** - Component testing with React Testing Library
- **Integration Tests** - API integration testing
- **E2E Tests** - User flow testing
- **Accessibility Tests** - WCAG compliance testing

## 🚀 Deployment

### Build Process
```bash
npm run build
npm run serve
```

### Environment Configuration
- **Development** - Local development with hot reload
- **Staging** - Pre-production testing environment
- **Production** - Optimized production build

### Deployment Targets
- **Vercel** - Recommended for React apps
- **Netlify** - Alternative deployment platform
- **AWS S3** - Static hosting with CloudFront
- **Docker** - Containerized deployment

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

### Code Standards
- **TypeScript** - Strict type checking
- **ESLint** - Code linting rules
- **Prettier** - Code formatting
- **Conventional Commits** - Commit message format

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- **Documentation** - Check the README and code comments
- **Issues** - Create GitHub issues for bugs
- **Discussions** - Use GitHub discussions for questions
- **Email** - Contact the development team

---

Built with ❤️ for the HappiDost platform - Connecting people with AI-powered service discovery!
