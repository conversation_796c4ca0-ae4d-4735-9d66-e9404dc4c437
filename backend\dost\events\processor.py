"""
DOST Event Processor
Handles routing, validation, and processing of DOST events
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable
from sqlalchemy.orm import Session

from .models import DOSTEvent, EventType, EventResponse
from ..entities.registry import EntityRegistry
from ...core.database import get_db, DOSTEvent as DOSTEventDB, DOSTSession
from ...ai.orchestration.agent_manager import AgentManager

logger = logging.getLogger(__name__)


class EventProcessor:
    """
    Core event processor for the DOST ecosystem.
    Handles validation, routing, and processing of all DOST events.
    """
    
    def __init__(self):
        self.entity_registry = EntityRegistry()
        self.agent_manager = AgentManager()
        self.event_handlers: Dict[EventType, Callable] = {}
        self.middleware: List[Callable] = []
        
        # Register default event handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default event handlers."""
        self.event_handlers[EventType.MESSAGE] = self._handle_message_event
        self.event_handlers[EventType.REQUEST] = self._handle_request_event
        self.event_handlers[EventType.RESPONSE] = self._handle_response_event
        self.event_handlers[EventType.REGISTRATION] = self._handle_registration_event
        self.event_handlers[EventType.DISCOVERY] = self._handle_discovery_event
        self.event_handlers[EventType.TRANSACTION] = self._handle_transaction_event
    
    def register_handler(self, event_type: EventType, handler: Callable):
        """Register a custom event handler."""
        self.event_handlers[event_type] = handler
    
    def add_middleware(self, middleware: Callable):
        """Add middleware for event processing."""
        self.middleware.append(middleware)
    
    async def process_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """
        Process a DOST event through the complete pipeline.
        
        Args:
            event: The DOST event to process
            db: Database session
            
        Returns:
            EventResponse with processing result
        """
        try:
            logger.info(f"Processing event {event.event_id} of type {event.event_type}")
            
            # 1. Validate event
            validation_result = await self._validate_event(event, db)
            if not validation_result.success:
                return validation_result
            
            # 2. Apply middleware
            for middleware in self.middleware:
                event = await middleware(event, db)
            
            # 3. Store event in database
            await self._store_event(event, db)
            
            # 4. Route to appropriate handler
            handler = self.event_handlers.get(event.event_type)
            if not handler:
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message=f"No handler found for event type: {event.event_type}"
                )
            
            # 5. Process event
            result = await handler(event, db)
            
            # 6. Update event status
            await self._update_event_status(event.event_id, "completed", db)
            
            logger.info(f"Successfully processed event {event.event_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing event {event.event_id}: {str(e)}")
            await self._update_event_status(event.event_id, "failed", db)
            return EventResponse(
                success=False,
                event_id=event.event_id,
                message=f"Error processing event: {str(e)}"
            )
    
    async def _validate_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Validate DOST event."""
        try:
            # 1. Check semantic version compatibility
            if not self._is_version_compatible(event.semantic_version):
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message=f"Incompatible semantic version: {event.semantic_version}"
                )
            
            # 2. Validate entities exist and are active
            source_entity = await self.entity_registry.get_entity(event.source_entity_id, db)
            if not source_entity or not source_entity.is_active:
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message=f"Invalid or inactive source entity: {event.source_entity_id}"
                )
            
            target_entity = await self.entity_registry.get_entity(event.target_entity_id, db)
            if not target_entity or not target_entity.is_active:
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message=f"Invalid or inactive target entity: {event.target_entity_id}"
                )
            
            # 3. Check session validity
            session = db.query(DOSTSession).filter(
                DOSTSession.session_id == event.session_id
            ).first()
            
            if not session:
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message=f"Invalid session: {event.session_id}"
                )
            
            if session.status != "active":
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message=f"Session is not active: {session.status}"
                )
            
            # 4. Check if entities are participants in the session
            if (event.source_entity_id not in session.participants or 
                event.target_entity_id not in session.participants):
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message="Entities are not participants in this session"
                )
            
            # 5. Check event expiry
            if event.expiry_timestamp and event.expiry_timestamp < datetime.utcnow():
                return EventResponse(
                    success=False,
                    event_id=event.event_id,
                    message="Event has expired"
                )
            
            return EventResponse(
                success=True,
                event_id=event.event_id,
                message="Event validation successful"
            )
            
        except Exception as e:
            logger.error(f"Error validating event: {str(e)}")
            return EventResponse(
                success=False,
                event_id=event.event_id,
                message=f"Validation error: {str(e)}"
            )
    
    def _is_version_compatible(self, version: str) -> bool:
        """Check if semantic version is compatible."""
        # Simple version compatibility check
        # In production, implement proper semantic versioning
        supported_versions = ["1.0.0", "1.0.1", "1.0.2"]
        return version in supported_versions
    
    async def _store_event(self, event: DOSTEvent, db: Session):
        """Store event in database."""
        try:
            db_event = DOSTEventDB(
                event_id=uuid.UUID(event.event_id),
                session_id=uuid.UUID(event.session_id),
                semantic_version=event.semantic_version,
                timestamp=event.timestamp,
                source_entity_id=event.source_entity_id,
                target_entity_id=event.target_entity_id,
                source_entity_endpoint=event.source_entity_endpoint,
                target_entity_endpoint=event.target_entity_endpoint,
                is_ai_generated=event.is_ai_generated,
                event_type=event.event_type.value,
                priority=event.priority,
                requires_response=event.requires_response,
                expiry_timestamp=event.expiry_timestamp,
                message_content=event.dost_event_message.dict(),
                tags=[tag.dict() for tag in event.dost_event_tag],
                metadata=event.context or {}
            )
            
            db.add(db_event)
            db.commit()
            logger.info(f"Stored event {event.event_id} in database")
            
        except Exception as e:
            logger.error(f"Error storing event: {str(e)}")
            db.rollback()
            raise
    
    async def _update_event_status(self, event_id: str, status: str, db: Session):
        """Update event processing status."""
        try:
            db_event = db.query(DOSTEventDB).filter(
                DOSTEventDB.event_id == uuid.UUID(event_id)
            ).first()
            
            if db_event:
                db_event.processing_status = status
                db_event.processed_at = datetime.utcnow()
                db.commit()
                
        except Exception as e:
            logger.error(f"Error updating event status: {str(e)}")
    
    # Event Handlers
    async def _handle_message_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Handle conversation message events."""
        try:
            # Route to appropriate AI assistant
            response_event = await self.agent_manager.route_message(event, db)
            
            return EventResponse(
                success=True,
                event_id=event.event_id,
                message="Message processed successfully",
                data={"response_event": response_event.dict() if response_event else None}
            )
            
        except Exception as e:
            logger.error(f"Error handling message event: {str(e)}")
            return EventResponse(
                success=False,
                event_id=event.event_id,
                message=f"Error handling message: {str(e)}"
            )
    
    async def _handle_request_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Handle service request events."""
        # Implementation for service requests
        return EventResponse(
            success=True,
            event_id=event.event_id,
            message="Request processed successfully"
        )
    
    async def _handle_response_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Handle response events."""
        # Implementation for responses
        return EventResponse(
            success=True,
            event_id=event.event_id,
            message="Response processed successfully"
        )
    
    async def _handle_registration_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Handle service registration events."""
        try:
            # Route to service registration assistant
            response_event = await self.agent_manager.handle_service_registration(event, db)
            
            return EventResponse(
                success=True,
                event_id=event.event_id,
                message="Registration processed successfully",
                data={"response_event": response_event.dict() if response_event else None}
            )
            
        except Exception as e:
            logger.error(f"Error handling registration event: {str(e)}")
            return EventResponse(
                success=False,
                event_id=event.event_id,
                message=f"Error handling registration: {str(e)}"
            )
    
    async def _handle_discovery_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Handle service discovery events."""
        try:
            # Route to discovery assistant
            response_event = await self.agent_manager.handle_service_discovery(event, db)
            
            return EventResponse(
                success=True,
                event_id=event.event_id,
                message="Discovery processed successfully",
                data={"response_event": response_event.dict() if response_event else None}
            )
            
        except Exception as e:
            logger.error(f"Error handling discovery event: {str(e)}")
            return EventResponse(
                success=False,
                event_id=event.event_id,
                message=f"Error handling discovery: {str(e)}"
            )
    
    async def _handle_transaction_event(self, event: DOSTEvent, db: Session) -> EventResponse:
        """Handle transaction events."""
        # Implementation for transactions
        return EventResponse(
            success=True,
            event_id=event.event_id,
            message="Transaction processed successfully"
        )


# Global event processor instance
event_processor = EventProcessor()
