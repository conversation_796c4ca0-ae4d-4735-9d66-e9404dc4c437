import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
} from '@mui/material';
import {
  Home as HomeIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <>
      <Helmet>
        <title>Page Not Found - HappiDost</title>
        <meta name="description" content="The page you're looking for doesn't exist." />
      </Helmet>

      <Container maxWidth="md">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '60vh',
            textAlign: 'center',
            py: 8,
          }}
        >
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* 404 Illustration */}
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '6rem', md: '8rem' },
                fontWeight: 'bold',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
              }}
            >
              404
            </Typography>

            <Typography
              variant="h4"
              sx={{
                mb: 2,
                fontWeight: 600,
                color: 'text.primary',
              }}
            >
              Oops! Page Not Found
            </Typography>

            <Typography
              variant="h6"
              sx={{
                mb: 4,
                color: 'text.secondary',
                maxWidth: 500,
                mx: 'auto',
              }}
            >
              The page you're looking for doesn't exist or has been moved. 
              Let's get you back on track!
            </Typography>

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<HomeIcon />}
                onClick={() => navigate('/')}
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  px: 4,
                  py: 1.5,
                }}
              >
                Go Home
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate(-1)}
                sx={{ px: 4, py: 1.5 }}
              >
                Go Back
              </Button>
            </Box>

            {/* Helpful Links */}
            <Box sx={{ mt: 6 }}>
              <Typography variant="body1" sx={{ mb: 2, color: 'text.secondary' }}>
                Or try these popular pages:
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="text"
                  onClick={() => navigate('/search')}
                  sx={{ color: 'primary.main' }}
                >
                  Search Services
                </Button>
                <Button
                  variant="text"
                  onClick={() => navigate('/register')}
                  sx={{ color: 'primary.main' }}
                >
                  Sign Up
                </Button>
                <Button
                  variant="text"
                  onClick={() => navigate('/login')}
                  sx={{ color: 'primary.main' }}
                >
                  Login
                </Button>
              </Box>
            </Box>
          </motion.div>
        </Box>
      </Container>
    </>
  );
};

export default NotFoundPage;
