"""
Context Manager
Manages conversation context and memory across interactions
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class ContextManager:
    """
    Manages conversation context and maintains memory across interactions.
    Handles context preservation, retrieval, and updates.
    """
    
    def __init__(self):
        self.session_contexts: Dict[str, Dict[str, Any]] = {}
        self.max_context_size = 50  # Maximum context entries per session
    
    async def get_context(self, session_id: str) -> Dict[str, Any]:
        """Get context for a session."""
        return self.session_contexts.get(session_id, {})
    
    async def update_context(self, session_id: str, context_update: Dict[str, Any]):
        """Update context for a session."""
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = {
                "created_at": datetime.utcnow().isoformat(),
                "conversation_history": [],
                "user_preferences": {},
                "current_intent": None,
                "entities": {},
                "metadata": {}
            }
        
        # Update context
        for key, value in context_update.items():
            self.session_contexts[session_id][key] = value
        
        # Update last activity
        self.session_contexts[session_id]["last_activity"] = datetime.utcnow().isoformat()
    
    async def add_to_conversation_history(self, session_id: str, entry: Dict[str, Any]):
        """Add entry to conversation history."""
        if session_id not in self.session_contexts:
            await self.update_context(session_id, {})
        
        history = self.session_contexts[session_id].get("conversation_history", [])
        history.append(entry)
        
        # Limit history size
        if len(history) > self.max_context_size:
            history = history[-self.max_context_size:]
        
        self.session_contexts[session_id]["conversation_history"] = history
    
    async def clear_context(self, session_id: str):
        """Clear context for a session."""
        if session_id in self.session_contexts:
            del self.session_contexts[session_id]


# Global context manager
context_manager = ContextManager()
