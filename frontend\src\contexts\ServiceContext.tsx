import React, { createContext, useContext, useReducer } from 'react';
import { serviceAPI } from '../services/api';
import { Service, ServiceRegistrationData, ServiceSearchParams } from '../types/service';
import toast from 'react-hot-toast';

// Types
interface ServiceState {
  services: Service[];
  currentService: Service | null;
  loading: boolean;
  error: string | null;
  searchResults: Service[];
  searchLoading: boolean;
  totalResults: number;
}

type ServiceAction =
  | { type: 'SERVICE_START' }
  | { type: 'SERVICE_SUCCESS'; payload: Service[] }
  | { type: 'SERVICE_FAILURE'; payload: string }
  | { type: 'SERVICE_SET_CURRENT'; payload: Service }
  | { type: 'SERVICE_CLEAR_CURRENT' }
  | { type: 'SERVICE_ADD'; payload: Service }
  | { type: 'SERVICE_UPDATE'; payload: Service }
  | { type: 'SERVICE_DELETE'; payload: string }
  | { type: 'SERVICE_SEARCH_START' }
  | { type: 'SERVICE_SEARCH_SUCCESS'; payload: { results: Service[]; total: number } }
  | { type: 'SERVICE_SEARCH_FAILURE'; payload: string }
  | { type: 'SERVICE_CLEAR_ERROR' };

interface ServiceContextType extends ServiceState {
  registerService: (data: ServiceRegistrationData) => Promise<Service>;
  getMyServices: () => Promise<void>;
  getService: (id: string) => Promise<void>;
  updateService: (id: string, data: Partial<Service>) => Promise<void>;
  deleteService: (id: string) => Promise<void>;
  searchServices: (params: ServiceSearchParams) => Promise<void>;
  clearError: () => void;
  clearCurrentService: () => void;
}

// Initial state
const initialState: ServiceState = {
  services: [],
  currentService: null,
  loading: false,
  error: null,
  searchResults: [],
  searchLoading: false,
  totalResults: 0,
};

// Reducer
const serviceReducer = (state: ServiceState, action: ServiceAction): ServiceState => {
  switch (action.type) {
    case 'SERVICE_START':
      return {
        ...state,
        loading: true,
        error: null,
      };
    case 'SERVICE_SUCCESS':
      return {
        ...state,
        services: action.payload,
        loading: false,
        error: null,
      };
    case 'SERVICE_FAILURE':
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    case 'SERVICE_SET_CURRENT':
      return {
        ...state,
        currentService: action.payload,
        loading: false,
        error: null,
      };
    case 'SERVICE_CLEAR_CURRENT':
      return {
        ...state,
        currentService: null,
      };
    case 'SERVICE_ADD':
      return {
        ...state,
        services: [action.payload, ...state.services],
        loading: false,
        error: null,
      };
    case 'SERVICE_UPDATE':
      return {
        ...state,
        services: state.services.map(service =>
          service.service_id === action.payload.service_id ? action.payload : service
        ),
        currentService: state.currentService?.service_id === action.payload.service_id
          ? action.payload
          : state.currentService,
        loading: false,
        error: null,
      };
    case 'SERVICE_DELETE':
      return {
        ...state,
        services: state.services.filter(service => service.service_id !== action.payload),
        currentService: state.currentService?.service_id === action.payload
          ? null
          : state.currentService,
        loading: false,
        error: null,
      };
    case 'SERVICE_SEARCH_START':
      return {
        ...state,
        searchLoading: true,
        error: null,
      };
    case 'SERVICE_SEARCH_SUCCESS':
      return {
        ...state,
        searchResults: action.payload.results,
        totalResults: action.payload.total,
        searchLoading: false,
        error: null,
      };
    case 'SERVICE_SEARCH_FAILURE':
      return {
        ...state,
        searchLoading: false,
        error: action.payload,
      };
    case 'SERVICE_CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Context
export const ServiceContext = createContext<ServiceContextType | undefined>(undefined);

// Provider
export const ServiceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(serviceReducer, initialState);

  // Register service
  const registerService = async (data: ServiceRegistrationData): Promise<Service> => {
    try {
      dispatch({ type: 'SERVICE_START' });
      
      const response = await serviceAPI.registerService(data);
      const service = response.data;
      
      dispatch({ type: 'SERVICE_ADD', payload: service });
      toast.success('Service registered successfully!');
      
      return service;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to register service';
      dispatch({ type: 'SERVICE_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Get my services
  const getMyServices = async () => {
    try {
      dispatch({ type: 'SERVICE_START' });
      
      const response = await serviceAPI.getMyServices();
      dispatch({ type: 'SERVICE_SUCCESS', payload: response.services });
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to fetch services';
      dispatch({ type: 'SERVICE_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // Get service by ID
  const getService = async (id: string) => {
    try {
      dispatch({ type: 'SERVICE_START' });
      
      const response = await serviceAPI.getService(id);
      dispatch({ type: 'SERVICE_SET_CURRENT', payload: response.service });
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to fetch service';
      dispatch({ type: 'SERVICE_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // Update service
  const updateService = async (id: string, data: Partial<Service>) => {
    try {
      dispatch({ type: 'SERVICE_START' });
      
      const response = await serviceAPI.updateService(id, data);
      dispatch({ type: 'SERVICE_UPDATE', payload: response.updated_service });
      toast.success('Service updated successfully!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to update service';
      dispatch({ type: 'SERVICE_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // Delete service
  const deleteService = async (id: string) => {
    try {
      dispatch({ type: 'SERVICE_START' });
      
      await serviceAPI.deleteService(id);
      dispatch({ type: 'SERVICE_DELETE', payload: id });
      toast.success('Service deleted successfully!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to delete service';
      dispatch({ type: 'SERVICE_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // Search services
  const searchServices = async (params: ServiceSearchParams) => {
    try {
      dispatch({ type: 'SERVICE_SEARCH_START' });
      
      const response = await serviceAPI.searchServices(params);
      dispatch({ 
        type: 'SERVICE_SEARCH_SUCCESS', 
        payload: { 
          results: response.results, 
          total: response.total_results 
        } 
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Search failed';
      dispatch({ type: 'SERVICE_SEARCH_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'SERVICE_CLEAR_ERROR' });
  };

  // Clear current service
  const clearCurrentService = () => {
    dispatch({ type: 'SERVICE_CLEAR_CURRENT' });
  };

  const value: ServiceContextType = {
    ...state,
    registerService,
    getMyServices,
    getService,
    updateService,
    deleteService,
    searchServices,
    clearError,
    clearCurrentService,
  };

  return (
    <ServiceContext.Provider value={value}>
      {children}
    </ServiceContext.Provider>
  );
};

// Hook
export const useService = () => {
  const context = useContext(ServiceContext);
  if (context === undefined) {
    throw new Error('useService must be used within a ServiceProvider');
  }
  return context;
};
