# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# AI & ML
openai==1.10.0
anthropic==0.7.8
langchain==0.0.350
langchain-openai==0.0.2
langchain-community==0.0.10
sentence-transformers==2.2.2
transformers==4.36.2
torch==2.1.1
numpy==1.24.4
scikit-learn==1.3.2

# Vector Database
qdrant-client==1.7.0
faiss-cpu==1.7.4
pinecone-client==2.2.4
pyvespa==0.39.0
# Data Processing
pandas==2.1.4
pydantic==2.5.1
pydantic-settings==2.1.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# Environment & Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# HTTP & Networking
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
# uuid is built-in to Python

# Logging & Monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2

# Development
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Cloud & Storage (Optional)
boto3==1.34.0
redis==5.0.1

# Text Processing
nltk==3.8.1
spacy==3.7.2

# Audio Processing (for voice features)
speechrecognition==3.10.0
pydub==0.25.1

# Image Processing (for future multimodal features)
pillow==10.1.0
opencv-python==********
