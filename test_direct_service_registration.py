#!/usr/bin/env python3
"""
Direct service registration test using OpenAI client approach
"""
import requests
import json
import time
import os
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client

# Load environment variables
load_dotenv()

# Configuration
BACKEND_URL = "http://localhost:8000"
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

# Initialize OpenAI client with error handling
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_CLIENT = None

if OPENAI_API_KEY:
    try:
        # Try to initialize without any extra parameters that might cause issues
        OPENAI_CLIENT = OpenAI(api_key=OPENAI_API_KEY)
        print(f"✅ OpenAI client initialized with key: {OPENAI_API_KEY[:10]}...{OPENAI_API_KEY[-5:]}")
    except Exception as e:
        print(f"❌ OpenAI client initialization failed: {e}")
        print("Trying alternative initialization...")
        try:
            # Alternative initialization approach
            import openai
            openai.api_key = OPENAI_API_KEY
            OPENAI_CLIENT = openai
            print("✅ OpenAI client initialized using legacy approach")
        except Exception as e2:
            print(f"❌ Alternative initialization also failed: {e2}")
            exit(1)
else:
    print("❌ OpenAI API key not found")
    exit(1)

def extract_service_data_with_llm(description):
    """Extract structured service data using OpenAI directly"""
    print(f"🤖 Extracting service data using LLM...")
    
    extraction_prompt = f"""
Extract structured service information from this natural language description:

"{description}"

Extract the following information and return as JSON:
{{
  "title": "Short, descriptive title for the service (max 100 chars)",
  "description": "Clean, professional description (200-500 words)",
  "category": "Main category (Education, Healthcare, Home Services, Beauty, Fitness, Technology, etc.)",
  "subcategory": "Specific subcategory (Tutoring, Cleaning, Repair, etc.)",
  "keywords": ["array", "of", "relevant", "search", "keywords"],
  "price_min": 0.0,
  "price_max": 0.0,
  "currency": "INR",
  "pricing_model": "hourly|fixed|daily|weekly|monthly|per_session|negotiable",
  "is_negotiable": true/false,
  "service_type": "on_location|remote|hybrid|at_provider",
  "service_radius_km": 0,
  "location_text": "Extracted location/area mentioned",
  "availability": [
    {{"day": "mon", "from_time": "09:00", "to_time": "17:00"}},
    {{"day": "tue", "from_time": "09:00", "to_time": "17:00"}}
  ],
  "timezone": "Asia/Kolkata",
  "special_requirements": "Any special requirements or conditions",
  "qualifications": ["list", "of", "qualifications", "or", "certifications"],
  "experience_years": 0,
  "languages": ["English", "Hindi", "etc"]
}}

Return only valid JSON, no additional text.
"""

    try:
        # Generate response using OpenAI client
        if hasattr(OPENAI_CLIENT, 'chat'):
            # New OpenAI client approach
            response = OPENAI_CLIENT.chat.completions.create(
                model="gpt-4o-2024-11-20",
                messages=[{"role": "user", "content": extraction_prompt}],
                temperature=0.1,
                max_tokens=2000,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                response_format={"type": "json_object"}
            )
            response_text = response.choices[0].message.content.strip()
        else:
            # Legacy OpenAI approach
            response = OPENAI_CLIENT.ChatCompletion.create(
                model="gpt-4o-2024-11-20",
                messages=[{"role": "user", "content": extraction_prompt}],
                temperature=0.1,
                max_tokens=2000,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0
            )
            response_text = response.choices[0].message.content.strip()

        # Parse response with improved error handling similar to your approach

        # Clean up the response text similar to your approach
        ai_response = response_text.strip('```json').strip('```').strip().replace('{\n','{').replace('\n}','}').replace(",\n",",").replace('\n','###')
        ai_response = ai_response.strip().replace('###', '')
        
        # Parse JSON
        extracted_data = json.loads(ai_response)
        
        print(f"✅ LLM extraction successful!")
        print(f"   Title: {extracted_data.get('title', 'N/A')}")
        print(f"   Category: {extracted_data.get('category', 'N/A')}")
        print(f"   Price Range: ₹{extracted_data.get('price_min', 0)}-₹{extracted_data.get('price_max', 0)}")
        print(f"   Location: {extracted_data.get('location_text', 'N/A')}")
        
        return extracted_data
        
    except Exception as e:
        print(f"❌ LLM extraction failed: {e}")
        return None

def register_user_and_get_token():
    """Register a service provider and get auth token"""
    print("🔐 Registering service provider...")
    
    register_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123',
        'full_name': 'Direct Service Provider',
        'role': 'service_provider',
        'terms_accepted': True
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/api/v1/auth/register", json=register_data)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Provider registered: {data['user']['user_id']}")
            return data['token'], data['user']['user_id']
        else:
            print(f"❌ Registration failed: {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None, None

def store_service_directly_in_supabase(extracted_data, provider_id):
    """Store service directly in Supabase"""
    print("💾 Storing service directly in Supabase...")
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Generate service ID
        service_id = f"srv.{provider_id.split('.')[-1]}.{int(time.time())}"
        
        # Prepare service data for Supabase
        service_data = {
            'service_id': service_id,
            'entity_id': provider_id,
            'title': extracted_data.get('title', 'Untitled Service'),
            'description': extracted_data.get('description', ''),
            'category': extracted_data.get('category', 'General'),
            'subcategory': extracted_data.get('subcategory'),
            'keywords': extracted_data.get('keywords', []),
            'pricing': {
                'min': extracted_data.get('price_min', 0),
                'max': extracted_data.get('price_max', 0),
                'currency': extracted_data.get('currency', 'INR'),
                'model': extracted_data.get('pricing_model', 'negotiable'),
                'is_negotiable': extracted_data.get('is_negotiable', True)
            },
            'service_type': extracted_data.get('service_type', 'on_location'),
            'location': extracted_data.get('location_text'),
            'availability': extracted_data.get('availability', []),
            'qualifications': extracted_data.get('qualifications', []),
            'experience_years': extracted_data.get('experience_years', 0),
            'languages': extracted_data.get('languages', ['English']),
            'is_active': True,
            'created_at': 'now()',
            'updated_at': 'now()'
        }
        
        # Insert into Supabase
        result = supabase.table('services').insert(service_data).execute()
        
        if result.data:
            print(f"✅ Service stored in Supabase successfully!")
            print(f"   Service ID: {service_id}")
            return service_id
        else:
            print(f"❌ Failed to store in Supabase: {result}")
            return None
            
    except Exception as e:
        print(f"❌ Supabase storage error: {e}")
        return None

def main():
    print("🚀 Testing Direct Service Registration with LLM Extraction")
    print("=" * 70)
    
    # Step 1: Register user and get token
    token, user_id = register_user_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Step 2: Test service descriptions
    test_services = [
        "I provide professional plumbing services in Mumbai. I can fix leaky faucets, install new pipes, and handle emergency repairs. Available 24/7. Rates start from ₹500 per hour.",
        
        "Expert home cleaning services in Bangalore. Deep cleaning, regular maintenance, post-construction cleanup. Eco-friendly products used. ₹1500 for 2BHK apartment."
    ]
    
    successful_registrations = 0
    
    for i, service_desc in enumerate(test_services, 1):
        print(f"\n{'='*20} SERVICE {i} {'='*20}")
        print(f"Description: {service_desc[:80]}...")
        
        # Extract data using LLM
        extracted_data = extract_service_data_with_llm(service_desc)
        
        if extracted_data:
            # Store directly in Supabase
            service_id = store_service_directly_in_supabase(extracted_data, user_id)
            if service_id:
                successful_registrations += 1
        
        time.sleep(2)  # Small delay between registrations
    
    # Step 3: Check final results
    print(f"\n{'='*70}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*70}")
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        services = supabase.table('services').select('*').execute()
        
        print(f"✅ Services successfully registered: {successful_registrations}")
        print(f"✅ Total services in Supabase: {len(services.data)}")
        
        if services.data:
            print("\n📋 Services in database:")
            for service in services.data:
                print(f"   - {service.get('title', 'No title')}")
                print(f"     Category: {service.get('category', 'No category')}")
                print(f"     Location: {service.get('location', 'No location')}")
        
        if successful_registrations > 0:
            print("\n🎉 SUCCESS: Direct LLM extraction and Supabase storage working!")
            print("   - OpenAI LLM extraction: ✅ Working")
            print("   - Supabase storage: ✅ Working")
            print("   - Service data parsing: ✅ Working")
        else:
            print("\n❌ No services were successfully registered")
            
    except Exception as e:
        print(f"❌ Error checking results: {e}")

if __name__ == "__main__":
    main()
