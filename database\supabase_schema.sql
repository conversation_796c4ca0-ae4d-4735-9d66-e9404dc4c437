-- HappiDost Platform Database Schema for Supabase
-- Created: 2025-01-15
-- Purpose: Complete database structure for DOST ecosystem

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =============================================
-- CORE USER MANAGEMENT
-- =============================================

-- User profiles with role-based access
CREATE TABLE profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role VARCHAR(50) NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'service_provider', 'business', 'admin', 'ai_assistant')),
    avatar_url TEXT,
    bio TEXT,
    location GEOGRAPHY(POINT, 4326),
    address JSONB,
    preferences JSONB DEFAULT '{}',
    verification_status VARCHAR(20) DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'pending', 'verified', 'rejected')),
    reputation_score DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INTEGER DEFAULT 0,
    vespa_user_id VARCHAR(100) UNIQUE, -- For Vespa integration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Create indexes for profiles
CREATE INDEX idx_profiles_user_id ON profiles(user_id);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_location ON profiles USING GIST(location);
CREATE INDEX idx_profiles_vespa_id ON profiles(vespa_user_id);
CREATE INDEX idx_profiles_email ON profiles(email);

-- =============================================
-- SERVICE MANAGEMENT (DSS - DOST Service Store)
-- =============================================

-- Main services table
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_id VARCHAR(50) UNIQUE NOT NULL, -- svc_48321 format
    provider_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    vespa_document_id VARCHAR(100) UNIQUE, -- For Vespa integration
    
    -- Basic service information
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    keywords TEXT[] DEFAULT '{}',
    
    -- Pricing information
    price_min DECIMAL(10,2),
    price_max DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'INR',
    pricing_model VARCHAR(50) DEFAULT 'hourly' CHECK (pricing_model IN ('hourly', 'fixed', 'daily', 'weekly', 'monthly', 'per_session', 'negotiable')),
    is_negotiable BOOLEAN DEFAULT false,
    
    -- Location and availability
    location GEOGRAPHY(POINT, 4326),
    service_address JSONB,
    service_radius_km INTEGER DEFAULT 5,
    service_type VARCHAR(20) DEFAULT 'on_location' CHECK (service_type IN ('on_location', 'remote', 'hybrid', 'at_provider')),
    
    -- Availability schedule
    availability JSONB DEFAULT '[]', -- Array of availability objects
    timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
    
    -- Service quality and verification
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected', 'suspended')),
    quality_score DECIMAL(3,2) DEFAULT 0.00,
    total_bookings INTEGER DEFAULT 0,
    total_reviews INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    
    -- AI and search metadata
    embedding_model VARCHAR(50) DEFAULT 'text-embedding-3-small',
    embedding_dims INTEGER DEFAULT 1536,
    last_embedding_update TIMESTAMP WITH TIME ZONE,
    search_tags TEXT[] DEFAULT '{}',
    
    -- Service status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'paused', 'suspended', 'deleted')),
    featured BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for services
CREATE INDEX idx_services_provider_id ON services(provider_id);
CREATE INDEX idx_services_service_id ON services(service_id);
CREATE INDEX idx_services_vespa_id ON services(vespa_document_id);
CREATE INDEX idx_services_category ON services(category);
CREATE INDEX idx_services_location ON services USING GIST(location);
CREATE INDEX idx_services_keywords ON services USING GIN(keywords);
CREATE INDEX idx_services_search_tags ON services USING GIN(search_tags);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_services_price_range ON services(price_min, price_max);
CREATE INDEX idx_services_rating ON services(average_rating);
CREATE INDEX idx_services_created_at ON services(created_at);

-- Full-text search index
CREATE INDEX idx_services_fulltext ON services USING GIN(to_tsvector('english', title || ' ' || description));

-- =============================================
-- TRANSACTION MANAGEMENT
-- =============================================

-- Service bookings and transactions
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id VARCHAR(50) UNIQUE NOT NULL, -- txn_12345 format
    
    -- Parties involved
    customer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    provider_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE,
    
    -- Transaction details
    transaction_type VARCHAR(20) DEFAULT 'booking' CHECK (transaction_type IN ('booking', 'payment', 'refund', 'cancellation')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'refunded', 'disputed')),
    
    -- Financial information
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'completed', 'failed', 'refunded')),
    payment_gateway_id VARCHAR(100),
    
    -- Service details
    service_date TIMESTAMP WITH TIME ZONE,
    service_duration INTEGER, -- in minutes
    service_location GEOGRAPHY(POINT, 4326),
    service_address JSONB,
    
    -- Additional information
    notes TEXT,
    special_requirements TEXT,
    cancellation_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for transactions
CREATE INDEX idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX idx_transactions_provider_id ON transactions(provider_id);
CREATE INDEX idx_transactions_service_id ON transactions(service_id);
CREATE INDEX idx_transactions_transaction_id ON transactions(transaction_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_payment_status ON transactions(payment_status);
CREATE INDEX idx_transactions_service_date ON transactions(service_date);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);

-- =============================================
-- CHAT AND COMMUNICATION
-- =============================================

-- Chat sessions between users
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(100) UNIQUE NOT NULL, -- DOST session ID
    
    -- Participants
    participants UUID[] NOT NULL, -- Array of user IDs
    session_type VARCHAR(20) DEFAULT 'direct' CHECK (session_type IN ('direct', 'group', 'support', 'ai_assisted')),
    
    -- Session context
    related_service_id UUID REFERENCES services(id),
    related_transaction_id UUID REFERENCES transactions(id),
    context JSONB DEFAULT '{}',
    
    -- Session status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'blocked')),
    last_message_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Individual chat messages (DOST Events)
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id VARCHAR(100) UNIQUE NOT NULL, -- DOST event ID
    session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
    
    -- Message details
    sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'audio', 'video', 'image', 'document', 'system', 'dost_object')),
    
    -- Message content
    content JSONB NOT NULL, -- Full DOST event content
    text_content TEXT, -- Extracted text for search
    
    -- Message metadata
    is_ai_generated BOOLEAN DEFAULT false,
    ai_model VARCHAR(50),
    ai_confidence DECIMAL(3,2),
    
    -- Message status
    status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('draft', 'sent', 'delivered', 'read', 'failed')),
    read_by JSONB DEFAULT '{}', -- Track read status by participants
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Search and indexing
    search_vector tsvector
);

-- Create indexes for chat
CREATE INDEX idx_chat_sessions_participants ON chat_sessions USING GIN(participants);
CREATE INDEX idx_chat_sessions_session_id ON chat_sessions(session_id);
CREATE INDEX idx_chat_sessions_service_id ON chat_sessions(related_service_id);

CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_event_id ON chat_messages(event_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_search ON chat_messages USING GIN(search_vector);

-- =============================================
-- REVIEWS AND RATINGS
-- =============================================

-- Service reviews and ratings
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    provider_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    review_text TEXT,
    
    -- Review aspects
    aspect_ratings JSONB DEFAULT '{}', -- quality, punctuality, communication, etc.
    
    -- Review metadata
    is_verified BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one review per transaction
    UNIQUE(transaction_id, reviewer_id)
);

-- Create indexes for reviews
CREATE INDEX idx_reviews_service_id ON reviews(service_id);
CREATE INDEX idx_reviews_provider_id ON reviews(provider_id);
CREATE INDEX idx_reviews_reviewer_id ON reviews(reviewer_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_created_at ON reviews(created_at);

-- =============================================
-- VESPA INTEGRATION TRACKING
-- =============================================

-- Track Vespa document synchronization
CREATE TABLE vespa_sync (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entity_type VARCHAR(50) NOT NULL, -- 'service', 'user', 'review'
    entity_id UUID NOT NULL,
    vespa_document_id VARCHAR(100) NOT NULL,
    
    -- Sync status
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed', 'deleted')),
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_error TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Document metadata
    document_version INTEGER DEFAULT 1,
    embedding_version VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique mapping
    UNIQUE(entity_type, entity_id)
);

-- Create indexes for Vespa sync
CREATE INDEX idx_vespa_sync_entity ON vespa_sync(entity_type, entity_id);
CREATE INDEX idx_vespa_sync_vespa_id ON vespa_sync(vespa_document_id);
CREATE INDEX idx_vespa_sync_status ON vespa_sync(sync_status);

-- =============================================
-- TRIGGERS AND FUNCTIONS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chat_sessions_updated_at BEFORE UPDATE ON chat_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chat_messages_updated_at BEFORE UPDATE ON chat_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update search vector for chat messages
CREATE OR REPLACE FUNCTION update_chat_message_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector = to_tsvector('english', COALESCE(NEW.text_content, ''));
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for search vector
CREATE TRIGGER update_chat_message_search_vector_trigger 
    BEFORE INSERT OR UPDATE ON chat_messages 
    FOR EACH ROW EXECUTE FUNCTION update_chat_message_search_vector();

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be expanded based on requirements)
-- Users can read their own profile
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = user_id);

-- Service providers can manage their services
CREATE POLICY "Providers can manage own services" ON services FOR ALL USING (provider_id IN (SELECT id FROM profiles WHERE user_id = auth.uid()));

-- Users can view active services
CREATE POLICY "Users can view active services" ON services FOR SELECT USING (status = 'active');

-- =============================================
-- INITIAL DATA AND SETUP
-- =============================================

-- Insert default categories (can be expanded)
INSERT INTO services (service_id, provider_id, title, description, category, subcategory, status) VALUES
('svc_default', (SELECT id FROM profiles LIMIT 1), 'Default Service', 'Default service for testing', 'Education', 'Tutoring', 'draft')
ON CONFLICT (service_id) DO NOTHING;

-- Create indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_services_composite_search 
ON services(category, status, average_rating DESC, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_composite_status 
ON transactions(status, payment_status, created_at DESC);

COMMENT ON TABLE profiles IS 'User profiles with role-based access and Vespa integration';
COMMENT ON TABLE services IS 'Service listings in the DOST Service Store (DSS)';
COMMENT ON TABLE transactions IS 'Service bookings and financial transactions';
COMMENT ON TABLE chat_sessions IS 'Chat sessions between users';
COMMENT ON TABLE chat_messages IS 'Individual DOST events/messages';
COMMENT ON TABLE reviews IS 'Service reviews and ratings';
COMMENT ON TABLE vespa_sync IS 'Vespa document synchronization tracking';
