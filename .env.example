# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/happidost
DATABASE_ECHO=false

# Vector Database Configuration (Qdrant)
VECTOR_DB_TYPE=qdrant
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# Alternative: Pinecone Configuration
PINECONE_API_KEY=
PINECONE_ENVIRONMENT=

# AI Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=
DEFAULT_LLM_MODEL=gpt-4o-2024-11-20
EMBEDDING_MODEL=text-embedding-3-small
MAX_TOKENS=1000
TEMPERATURE=0.1

# Security
SECRET_KEY=your_secret_key_here_make_it_long_and_random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Service Discovery
SERVICE_SIMILARITY_THRESHOLD=0.7
MAX_SEARCH_RESULTS=10

# WebSocket Configuration
WEBSOCKET_TIMEOUT=300
MAX_CONNECTIONS_PER_USER=3

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=["image/jpeg", "image/png", "audio/mpeg", "audio/wav"]

# External APIs
PAYMENT_GATEWAY_URL=
IDENTITY_VERIFICATION_URL=

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=8001

# DOST Configuration
DOST_SEMANTIC_VERSION=1.0.0
MAX_SESSION_DURATION=3600
EVENT_RETENTION_DAYS=30
