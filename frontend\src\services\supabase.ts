import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://aerrspknmocqsohbjkze.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk';

export const supabase = createClient(supabaseUrl, supabaseKey);

// Database types
export interface Profile {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  phone_number?: string;
  role: 'user' | 'service_provider' | 'business';
  profile_image_url?: string;
  location?: {
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  preferences?: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    privacy: {
      show_phone: boolean;
      show_email: boolean;
      show_location: boolean;
    };
  };
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  provider_id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  pricing: {
    type: 'fixed' | 'hourly' | 'negotiable';
    amount?: number;
    currency: string;
    details?: string;
  };
  location: {
    type: 'online' | 'in_person' | 'hybrid';
    address?: string;
    city: string;
    state: string;
    country: string;
    postal_code?: string;
    latitude?: number;
    longitude?: number;
    service_radius?: number;
  };
  availability: {
    schedule: {
      [key: string]: {
        start: string;
        end: string;
        available: boolean;
      };
    };
    timezone: string;
    advance_booking_days: number;
  };
  requirements?: string[];
  tags: string[];
  images?: string[];
  rating?: number;
  review_count: number;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  verification_status: 'unverified' | 'pending' | 'verified';
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  service_id: string;
  client_id: string;
  provider_id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'disputed';
  payment_status: 'pending' | 'paid' | 'refunded' | 'failed';
  scheduled_date?: string;
  completion_date?: string;
  notes?: string;
  rating?: number;
  review?: string;
  created_at: string;
  updated_at: string;
}

export interface Chat {
  id: string;
  transaction_id?: string;
  participants: string[];
  last_message?: string;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

// Supabase helper functions
export const supabaseHelpers = {
  // Profile operations
  async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
    
    return data;
  },

  async updateProfile(userId: string, updates: Partial<Profile>): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating profile:', error);
      return null;
    }
    
    return data;
  },

  // Service operations
  async getServices(filters?: {
    category?: string;
    location?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<Service[]> {
    let query = supabase
      .from('services')
      .select('*')
      .eq('status', 'active');

    if (filters?.category) {
      query = query.eq('category', filters.category);
    }

    if (filters?.search) {
      query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching services:', error);
      return [];
    }
    
    return data || [];
  },

  async getService(serviceId: string): Promise<Service | null> {
    const { data, error } = await supabase
      .from('services')
      .select('*')
      .eq('id', serviceId)
      .single();
    
    if (error) {
      console.error('Error fetching service:', error);
      return null;
    }
    
    return data;
  },

  async createService(service: Omit<Service, 'id' | 'created_at' | 'updated_at'>): Promise<Service | null> {
    const { data, error } = await supabase
      .from('services')
      .insert(service)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating service:', error);
      return null;
    }
    
    return data;
  },

  // Transaction operations
  async getTransactions(userId: string, role: 'client' | 'provider'): Promise<Transaction[]> {
    const column = role === 'client' ? 'client_id' : 'provider_id';
    
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq(column, userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching transactions:', error);
      return [];
    }
    
    return data || [];
  },

  async createTransaction(transaction: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>): Promise<Transaction | null> {
    const { data, error } = await supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating transaction:', error);
      return null;
    }
    
    return data;
  },

  // Chat operations
  async getChats(userId: string): Promise<Chat[]> {
    const { data, error } = await supabase
      .from('chats')
      .select('*')
      .contains('participants', [userId])
      .order('last_message_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching chats:', error);
      return [];
    }
    
    return data || [];
  },

  async createChat(participants: string[], transactionId?: string): Promise<Chat | null> {
    const { data, error } = await supabase
      .from('chats')
      .insert({
        participants,
        transaction_id: transactionId,
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating chat:', error);
      return null;
    }
    
    return data;
  },
};
