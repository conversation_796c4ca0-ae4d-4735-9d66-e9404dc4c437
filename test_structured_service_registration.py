#!/usr/bin/env python3
"""
Test structured service registration using the /api/v1/services/register endpoint
This bypasses the LLM extraction and uses structured data directly
"""
import requests
import json
import time
from supabase import create_client

# Configuration
BACKEND_URL = "http://localhost:8000"
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def register_user_and_get_token():
    """Register a service provider and get auth token"""
    print("🔐 Registering service provider...")
    
    register_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123',
        'full_name': 'Structured Service Provider',
        'role': 'service_provider',
        'terms_accepted': True
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/api/v1/auth/register", json=register_data)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Provider registered: {data['user']['user_id']}")
            return data['token'], data['user']['user_id']
        else:
            print(f"❌ Registration failed: {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None, None

def create_structured_service_data(description, provider_id):
    """Create structured service data for the /api/v1/services/register endpoint"""
    print(f"🔧 Creating structured service data...")
    
    # Service templates based on description keywords
    if "plumbing" in description.lower():
        return {
            "service_name": "Professional Plumbing Services",
            "description": "Expert plumbing services including repairs, installations, and emergency fixes. Available 24/7 with competitive rates in Mumbai area.",
            "pricing_model": "hourly",
            "pricing_info": {
                "base_price": 500,
                "max_price": 2000,
                "currency": "INR",
                "negotiable": True
            },
            "availability": {
                "monday": {"open": "09:00", "close": "18:00"},
                "tuesday": {"open": "09:00", "close": "18:00"},
                "wednesday": {"open": "09:00", "close": "18:00"},
                "thursday": {"open": "09:00", "close": "18:00"},
                "friday": {"open": "09:00", "close": "18:00"},
                "saturday": {"open": "10:00", "close": "16:00"}
            },
            "location_info": {
                "cities": ["mumbai"],
                "areas": ["andheri", "bandra", "kurla", "powai"],
                "service_radius": 15
            },
            "tags": ["plumbing", "repairs", "installation", "emergency", "mumbai"]
        }
    elif "cleaning" in description.lower():
        return {
            "service_name": "Professional Home Cleaning Services",
            "description": "Expert home cleaning services including deep cleaning, regular maintenance, and eco-friendly solutions for residential properties in Bangalore.",
            "pricing_model": "fixed",
            "pricing_info": {
                "base_price": 1500,
                "max_price": 3000,
                "currency": "INR",
                "negotiable": True
            },
            "availability": {
                "monday": {"open": "08:00", "close": "17:00"},
                "tuesday": {"open": "08:00", "close": "17:00"},
                "wednesday": {"open": "08:00", "close": "17:00"},
                "thursday": {"open": "08:00", "close": "17:00"},
                "friday": {"open": "08:00", "close": "17:00"},
                "saturday": {"open": "09:00", "close": "15:00"}
            },
            "location_info": {
                "cities": ["bangalore"],
                "areas": ["koramangala", "indiranagar", "whitefield", "electronic_city"],
                "service_radius": 20
            },
            "tags": ["cleaning", "home", "deep_cleaning", "maintenance", "bangalore"]
        }
    elif "web development" in description.lower() or "website" in description.lower():
        return {
            "service_name": "Professional Web Development Services",
            "description": "Full-stack web development services including responsive websites, e-commerce platforms, and mobile applications using modern technologies like React and Node.js.",
            "pricing_model": "fixed",
            "pricing_info": {
                "base_price": 25000,
                "max_price": 100000,
                "currency": "INR",
                "negotiable": True
            },
            "availability": {
                "monday": {"open": "10:00", "close": "19:00"},
                "tuesday": {"open": "10:00", "close": "19:00"},
                "wednesday": {"open": "10:00", "close": "19:00"},
                "thursday": {"open": "10:00", "close": "19:00"},
                "friday": {"open": "10:00", "close": "19:00"}
            },
            "location_info": {
                "cities": ["delhi", "remote"],
                "areas": ["cp", "gurgaon", "noida", "remote_work"],
                "service_radius": 50
            },
            "tags": ["web_development", "website", "react", "nodejs", "ecommerce", "remote"]
        }
    else:
        # Generic service
        return {
            "service_name": "Professional Service",
            "description": description,
            "pricing_model": "negotiable",
            "pricing_info": {
                "base_price": 500,
                "max_price": 2000,
                "currency": "INR",
                "negotiable": True
            },
            "availability": {
                "monday": {"open": "09:00", "close": "17:00"},
                "tuesday": {"open": "09:00", "close": "17:00"},
                "wednesday": {"open": "09:00", "close": "17:00"},
                "thursday": {"open": "09:00", "close": "17:00"},
                "friday": {"open": "09:00", "close": "17:00"}
            },
            "location_info": {
                "cities": ["india"],
                "areas": ["general"],
                "service_radius": 10
            },
            "tags": ["service", "professional"]
        }

def register_service_structured(token, service_data):
    """Register service using structured data endpoint"""
    print(f"📝 Registering service: {service_data['service_name']}")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/api/v1/services/register", json=service_data, headers=headers)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service registered successfully!")
            print(f"   Service ID: {data.get('service_id', 'N/A')}")
            print(f"   Approval Status: {data.get('approval_status', 'N/A')}")
            print(f"   Message: {data.get('message', 'N/A')}")
            return data.get('service_id')
        else:
            print(f"❌ Service registration failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Service registration error: {e}")
        return None

def check_supabase_data():
    """Check what's stored in Supabase"""
    print("\n🔍 Checking Supabase data...")
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Check entities
        entities = supabase.table('entities').select('*').execute()
        print(f"📊 Entities in Supabase: {len(entities.data)}")
        
        # Check services (this will likely fail due to RLS, but let's try)
        try:
            services = supabase.table('services').select('*').execute()
            print(f"📊 Services in Supabase: {len(services.data)}")
        except Exception as e:
            print(f"⚠️ Could not check services table: {e}")
        
    except Exception as e:
        print(f"❌ Error checking Supabase: {e}")

def main():
    print("🚀 Testing Structured Service Registration (Bypassing LLM)")
    print("=" * 70)
    
    # Step 1: Register user and get token
    token, user_id = register_user_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Step 2: Test service descriptions
    test_services = [
        "I provide professional plumbing services in Mumbai. I can fix leaky faucets, install new pipes, and handle emergency repairs. Available 24/7. Rates start from ₹500 per hour.",
        
        "Expert home cleaning services in Bangalore. Deep cleaning, regular maintenance, post-construction cleanup. Eco-friendly products used. ₹1500 for 2BHK apartment.",
        
        "Professional web development services. I create responsive websites, e-commerce platforms, and mobile apps using React, Node.js, and Python. Based in Delhi, also work remotely. Starting from ₹25,000 per project."
    ]
    
    successful_registrations = 0
    
    for i, service_desc in enumerate(test_services, 1):
        print(f"\n{'='*20} SERVICE {i} {'='*20}")
        print(f"Description: {service_desc[:80]}...")
        
        # Create structured service data
        service_data = create_structured_service_data(service_desc, user_id)
        
        # Register service
        service_id = register_service_structured(token, service_data)
        if service_id:
            successful_registrations += 1
        
        time.sleep(1)  # Small delay between registrations
    
    # Step 3: Check results
    check_supabase_data()
    
    # Step 4: Final summary
    print(f"\n{'='*70}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*70}")
    print(f"✅ Services successfully registered: {successful_registrations}")
    
    if successful_registrations > 0:
        print("\n🎉 SUCCESS: Structured service registration working!")
        print("   - Structured data creation: ✅ Working")
        print("   - API endpoint: ✅ Working")
        print("   - Service registration: ✅ Working")
        print("\n💡 This proves the backend can register services when LLM is bypassed")
    else:
        print("\n❌ No services were successfully registered")
        print("   - Check API endpoint availability")
        print("   - Check authentication")
        print("   - Check data structure")

if __name__ == "__main__":
    main()
