#!/usr/bin/env python3
"""
Test script to verify HappiDost AI Backend setup
"""
import os
import sys
import asyncio
import logging
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_imports():
    """Test if all modules can be imported."""
    logger.info("Testing module imports...")
    
    try:
        # Test core imports
        from backend.core.config import settings
        logger.info("✅ Core config imported successfully")
        
        # Test AI imports
        from backend.ai.models.llm_client import LLMClient
        logger.info("✅ LLM client imported successfully")
        
        # Test DOST imports
        from backend.dost.events.models import DOSTEvent
        logger.info("✅ DOST event models imported successfully")
        
        # Test API imports
        from backend.api.v1.chat import chat_router
        logger.info("✅ Chat router imported successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False


async def test_configuration():
    """Test configuration loading."""
    logger.info("Testing configuration...")
    
    try:
        from backend.core.config import settings
        
        # Check if OpenAI API key is set
        if settings.openai_api_key and settings.openai_api_key != "your_openai_api_key_here":
            logger.info("✅ OpenAI API key is configured")
        else:
            logger.warning("⚠️ OpenAI API key not configured")
        
        # Check database URL
        if settings.database_url:
            logger.info("✅ Database URL is configured")
        else:
            logger.warning("⚠️ Database URL not configured")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration error: {e}")
        return False


async def test_llm_client():
    """Test LLM client initialization."""
    logger.info("Testing LLM client...")
    
    try:
        from backend.ai.models.llm_client import LLMClient
        
        client = LLMClient()
        status = client.get_client_status()
        
        if status.get("openai"):
            logger.info("✅ OpenAI client initialized")
        else:
            logger.warning("⚠️ OpenAI client not initialized")
        
        if status.get("anthropic"):
            logger.info("✅ Anthropic client initialized")
        else:
            logger.info("ℹ️ Anthropic client not configured (optional)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM client error: {e}")
        return False


async def test_event_system():
    """Test DOST event system."""
    logger.info("Testing DOST event system...")
    
    try:
        from backend.dost.events.models import create_text_event, EventType
        
        # Create a test event with proper UUID
        import uuid
        test_session_id = str(uuid.uuid4())

        event = create_text_event(
            session_id=test_session_id,
            source_entity_id="hum.test.user.1234",
            target_entity_id="ai.happidost.user_assistant",
            text_content="Hello, this is a test message"
        )
        
        # Validate event
        if event.event_id and event.session_id and event.dost_event_message.text:
            logger.info("✅ DOST event creation successful")
            return True
        else:
            logger.error("❌ DOST event validation failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ DOST event system error: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("🧪 Running HappiDost AI Backend setup tests...")
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("LLM Client", test_llm_client),
        ("Event System", test_event_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Your setup is ready.")
        return True
    else:
        logger.warning("⚠️ Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
