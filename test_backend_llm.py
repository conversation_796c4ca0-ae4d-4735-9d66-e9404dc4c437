#!/usr/bin/env python3
"""
Test the backend's LLM client directly
"""
import asyncio
import sys
import os

# Add backend to path
sys.path.append('backend')

async def test_backend_llm():
    """Test the backend's LLM client"""
    try:
        from backend.ai.models.llm_client import LLMClient
        
        print("🤖 Testing backend LLM client...")
        
        # Initialize LLM client
        llm_client = LLMClient()
        
        # Test simple response generation
        messages = [
            {"role": "user", "content": "Extract service info from: 'I provide plumbing services in Mumbai for ₹500/hour' and return as JSON with title, category, price_min, price_max, location_text fields only."}
        ]
        
        config = {
            "model": "gpt-4o-2024-11-20",
            "temperature": 0.1,
            "max_tokens": 500,
            "response_format": {"type": "json_object"}
        }
        
        print("📤 Sending request to LLM...")
        response = await llm_client.generate_response(messages, config)
        
        print("📥 LLM Response:")
        print(response)
        
        # Try to parse as JSON
        import json
        try:
            parsed = json.loads(response)
            print("✅ JSON parsing successful!")
            print(f"   Title: {parsed.get('title', 'N/A')}")
            print(f"   Category: {parsed.get('category', 'N/A')}")
            print(f"   Price Min: {parsed.get('price_min', 'N/A')}")
            print(f"   Price Max: {parsed.get('price_max', 'N/A')}")
            print(f"   Location: {parsed.get('location_text', 'N/A')}")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Backend LLM test failed: {e}")
        return False

async def test_service_registration_system():
    """Test the service registration system directly"""
    try:
        from backend.services.service_registration import ServiceRegistrationSystem
        
        print("\n🔧 Testing Service Registration System...")
        
        # Initialize system
        srs = ServiceRegistrationSystem()
        
        # Test service registration
        result = await srs.register_service(
            provider_id="hum.test.provider.123",
            natural_language_description="I provide professional plumbing services in Mumbai. I can fix leaky faucets, install new pipes, and handle emergency repairs. Available 24/7. Rates start from ₹500 per hour."
        )
        
        print("📊 Registration Result:")
        print(f"   Success: {result.get('success', False)}")
        if result.get('success'):
            print(f"   Service ID: {result.get('service_id', 'N/A')}")
            print(f"   Supabase ID: {result.get('supabase_id', 'N/A')}")
            print(f"   Vespa Document ID: {result.get('vespa_document_id', 'N/A')}")
            
            extracted = result.get('extracted_data', {})
            if extracted:
                print(f"   📊 Extracted Data:")
                print(f"      Title: {extracted.get('title', 'N/A')}")
                print(f"      Category: {extracted.get('category', 'N/A')}")
                print(f"      Price Range: ₹{extracted.get('price_min', 0)}-₹{extracted.get('price_max', 0)}")
                print(f"      Location: {extracted.get('location_text', 'N/A')}")
            return True
        else:
            print(f"   Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Service registration system test failed: {e}")
        return False

async def main():
    print("🚀 Testing Backend LLM and Service Registration")
    print("=" * 60)
    
    # Test 1: Backend LLM client
    llm_success = await test_backend_llm()
    
    # Test 2: Service registration system
    if llm_success:
        srs_success = await test_service_registration_system()
    else:
        print("⏭️ Skipping service registration test due to LLM failure")
        srs_success = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Backend LLM Client: {'Working' if llm_success else 'Failed'}")
    print(f"✅ Service Registration: {'Working' if srs_success else 'Failed'}")
    
    if llm_success and srs_success:
        print("\n🎉 SUCCESS: Backend integration is working!")
        print("   - LLM extraction: ✅ Working")
        print("   - Service registration: ✅ Working")
        print("   - Supabase storage: ✅ Working")
    else:
        print("\n❌ Some components need attention")
        if not llm_success:
            print("   - Fix LLM client initialization")
        if not srs_success:
            print("   - Fix service registration system")

if __name__ == "__main__":
    asyncio.run(main())
