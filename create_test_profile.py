#!/usr/bin/env python3
"""
Create a test profile in Supabase for service testing
"""
from supabase import create_client
import uuid

# Configuration
SUPABASE_URL = 'https://aerrspknmocqsohbjkze.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcnJzcGtubW9jcXNvaGJqa3plIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTczNjQzNzMsImV4cCI6MjA3Mjk0MDM3M30.LnIKwKltap_udkSn7sGGZPaQSaBUZlUuUvMNswdFlBk'

def create_test_profile():
    """Create a test profile for service registration testing"""
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        print("🔍 Checking existing profiles...")
        
        # Check if profiles exist
        profiles = supabase.table('profiles').select('*').execute()
        print(f"📊 Found {len(profiles.data)} existing profiles")
        
        if profiles.data:
            print("✅ Using existing profile:")
            profile = profiles.data[0]
            print(f"   ID: {profile['id']}")
            print(f"   Email: {profile['email']}")
            print(f"   Name: {profile['full_name']}")
            print(f"   Role: {profile['role']}")
            return profile['id']
        
        print("\n📝 Creating test profile...")
        
        # Create a test profile
        profile_data = {
            'user_id': str(uuid.uuid4()),  # Fake user_id since we don't have auth.users
            'email': '<EMAIL>',
            'full_name': 'Test Service Provider',
            'role': 'service_provider',
            'verification_status': 'unverified',
            'reputation_score': 0.0,
            'total_ratings': 0
        }
        
        try:
            result = supabase.table('profiles').insert(profile_data).execute()
            if result.data:
                print("✅ Test profile created successfully!")
                profile = result.data[0]
                print(f"   ID: {profile['id']}")
                print(f"   Email: {profile['email']}")
                print(f"   Name: {profile['full_name']}")
                return profile['id']
            else:
                print(f"❌ Profile creation failed: {result}")
                return None
        except Exception as e:
            print(f"❌ Profile creation error: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    profile_id = create_test_profile()
    if profile_id:
        print(f"\n🎉 Profile ready for testing: {profile_id}")
    else:
        print("\n❌ Failed to create/find profile")
